<?php

declare(strict_types=1);

namespace App\V2\Application\DTO\Payment;

readonly class PaymentConfirmationInputDTO
{
    public function __construct(
        private string $payload,
        private array $metadata,
    ) {
    }

    public function getPayload(): string
    {
        return $this->payload;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }
}
