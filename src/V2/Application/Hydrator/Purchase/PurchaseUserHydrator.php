<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\Purchase;

use App\Entity\User;
use App\V2\Application\Hydrator\Hydrator;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Application\Hydrator\Purchase\Exception\PurchaseHydratorException;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseCollection;
use App\V2\Domain\Purchase\PurchaseHydrationCriteria;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

class PurchaseUserHydrator implements Hydrator
{
    private const HydratorPriority PRIORITY = HydratorPriority::First;

    public function __construct(
        private readonly UserRepository $userRepository,
    ) {
    }

    public function getPriority(): HydratorPriority
    {
        return self::PRIORITY;
    }

    public function supports(HydrationCriteria $criteria): bool
    {
        return $criteria instanceof PurchaseHydrationCriteria && $criteria->needsUser();
    }

    /**
     * @throws PurchaseHydratorException
     * @throws InfrastructureException
     * @throws CriteriaException
     */
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof PurchaseCollection) {
            return;
        }

        if ($collection->isEmpty()) {
            return;
        }

        $userIds = $collection->reduce(
            callback: function (array $carry, Purchase $purchase) {
                $carry[] = $purchase->getUserId();

                return $carry;
            },
            initial: []
        );

        $userIds = array_unique($userIds);

        $users = $this->userRepository->findBy(
            UserCriteria::createByIds(new IdCollection($userIds))
        );

        if ($users->isEmpty()) {
            return;
        }

        /** @var array<string, User> $usersById */
        $usersById = array_reduce(
            $users->all(),
            fn ($carry, $user) => $carry + [$user->getId() => $user],
            []
        );

        foreach ($collection->all() as $purchase) {
            if (!isset($usersById[$purchase->getUserId()->value()])) {
                continue;
            }

            $purchase->setUser($usersById[$purchase->getUserId()->value()]->getUserCard());
        }
    }
}
