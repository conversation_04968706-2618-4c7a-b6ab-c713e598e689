<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator;

use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Hydrator\HydratorException;

interface Hydrator
{
    /**
     * @throws HydratorException
     * @throws InfrastructureException
     */
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void;

    public function supports(HydrationCriteria $criteria): bool;

    public function getPriority(): HydratorPriority;
}
