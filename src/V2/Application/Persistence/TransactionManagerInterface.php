<?php

declare(strict_types=1);

namespace App\V2\Application\Persistence;

interface TransactionManagerInterface
{
    public function beginTransaction(): void;

    public function commit(): void;

    public function rollback(): void;

    /**
     * Execute a callable within a transaction.
     * If the callable throws an exception, the transaction is rolled back.
     * If the callable completes successfully, the transaction is committed.
     */
    public function transactional(callable $operation): mixed;
}
