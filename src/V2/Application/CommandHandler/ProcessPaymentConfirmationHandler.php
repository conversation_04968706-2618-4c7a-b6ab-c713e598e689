<?php

declare(strict_types=1);

namespace App\V2\Application\CommandHandler;

use App\V2\Application\Command\ProcessPaymentConfirmation;
use App\V2\Domain\Purchase\Payment\PaymentManagerInterface;

readonly class ProcessPaymentConfirmationHandler
{
    public function __construct(
        private PaymentManagerInterface $paymentManager,
    ) {
    }

    public function handle(ProcessPaymentConfirmation $command): void
    {
        $dto = $command->getPaymentConfirmationInputDTO();
        $this->paymentManager->processWebhook(
            $dto->getPayload(),
            $dto->getMetadata()
        );
    }
}
