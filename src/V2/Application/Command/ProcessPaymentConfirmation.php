<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Application\DTO\Payment\PaymentConfirmationInputDTO;
use App\V2\Domain\Bus\Command;

readonly class ProcessPaymentConfirmation implements Command
{
    public function __construct(
        private PaymentConfirmationInputDTO $paymentConfirmationInputDTO,
    ) {
    }

    public function getPaymentConfirmationInputDTO(): PaymentConfirmationInputDTO
    {
        return $this->paymentConfirmationInputDTO;
    }
}
