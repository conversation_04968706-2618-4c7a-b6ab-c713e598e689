<?php

declare(strict_types=1);

namespace App\V2\Application\Service\Announcement;

use App\Entity\Announcement as LegacyAnnouncement;
use App\Entity\Course as LegacyCourse;
use App\Entity\MaterialCourse;
use App\Entity\TaskCourse;
use App\Entity\User;
use App\Repository\AnnouncementTutorRepository;
use App\Service\SettingsService;
use App\V2\Domain\Announcement\Exception\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\UserNotAuthorizedException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerCriteria;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Course\Manager\CourseManager;
use App\V2\Domain\Course\Manager\CourseManagerCriteria;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Domain\Shared\Id\Id;

readonly class AnnouncementAuthorizationService implements AnnouncementAuthorizationServiceInterface
{
    public function __construct(
        private AnnouncementManagerRepository $announcementManagerRepository,
        private CourseManagerRepository $courseManagerRepository,
        private SettingsService $settingsService,
        private AnnouncementTutorRepository $announcementTutorRepository,
    ) {
    }

    #[\Override]
    public function ensureUserCanManageAnnouncement(User $user, LegacyAnnouncement $announcement): void
    {
        if ($this->userCanManageAnnouncement(user: $user, announcement: $announcement)) {
            return;
        }
        throw UserNotAuthorizedException::userNotAuthorized(
            announcement: $announcement,
            user: $user->getEmail() ?? 'Unknown user'
        );
    }

    private function userCanManageAnnouncement(User $user, LegacyAnnouncement $announcement): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        if ($user->isManager() && $announcement->getCreatedBy()?->getId() === $user->getId()) {
            return true;
        }

        // Shared managers can if sharing is enabled and they are actually shared
        if ($user->isManager() && $this->settingsService->get('app.announcement.managers.sharing')) {
            try {
                $this->announcementManagerRepository->findOneBy(
                    AnnouncementManagerCriteria::createEmpty()
                        ->filterByUserId($user->getId())
                        ->filterByAnnouncementId($announcement->getId())
                );

                return true;
            } catch (AnnouncementManagerNotFoundException) {
                // Manager is not shared for this announcement.
            }
        }

        return false;
    }

    #[\Override]
    public function ensureUserCanCreateAnnouncement(User $user, LegacyCourse $course): void
    {
        if ($user->isAdmin()) {
            return;
        }

        $courseManagerCollection = $this->courseManagerRepository->findBy(
            CourseManagerCriteria::createEmpty()
                ->filterByUserId(new Id($user->getId())),
        );

        if ($courseManagerCollection->isEmpty()) {
            return; // No assigned courses, can create the announcement.
        }

        $result = $courseManagerCollection->filter(
            fn (CourseManager $courseManager) => $courseManager->getCourseId()->value() === $course->getId()
        );

        if (!$result->isEmpty()) {
            return; // Course assigned
        }

        throw UserNotAuthorizedException::userNoPermissionsToCreateAnnouncementForCourse(
            course: $course,
            email: $user->getEmail(),
        );
    }

    public function ensureUserCanDeleteAnnouncement(User $user, LegacyAnnouncement $announcement): void
    {
        if (!LegacyAnnouncement::canChangeStatus($announcement, LegacyAnnouncement::STATUS_ARCHIVED)) {
            throw UserNotAuthorizedException::announcementCannotBeDeleted(
                announcement: $announcement,
            );
        }

        if ($user->isAdmin()) {
            return;
        }

        if ($user->isManager() && $announcement->getCreatedBy()?->getId() === $user->getId()) {
            return;
        }

        throw UserNotAuthorizedException::userNotAuthorizedToDeleteAnnouncement(
            announcement: $announcement,
            email: $user->getEmail(),
        );
    }

    public function ensureUserCanManageAnnouncementResource(User $user, LegacyAnnouncement $announcement): void
    {
        if ($this->userCanManageAnnouncement(user: $user, announcement: $announcement)) {
            return;
        }
        if ($user->isTutor() && $this->announcementTutorRepository->isTutorSharedWithAnnouncement($user, $announcement)) {
            return;
        }
        throw UserNotAuthorizedException::userNotAuthorized(
            announcement: $announcement,
            user: $user->getEmail() ?? 'Unknown user'
        );
    }

    public function ensureUserCanManageAnnouncementMaterials(User $user, MaterialCourse $materialCourse): void
    {
        try {
            $announcement = $materialCourse->getAnnouncement();
            if ($this->userCanManageAnnouncement(user: $user, announcement: $announcement)) {
                return;
            }
        } catch (UserNotAuthorizedException) {
            // Continue to check if the user is a tutor shared with the announcement.
        }

        if ($user->isTutor()
            && $this->announcementTutorRepository->isTutorSharedWithAnnouncement($user, $announcement)
            && $materialCourse->getCreatedBy()?->getId() === $user->getId()) {
            return;
        }

        throw UserNotAuthorizedException::userNotAuthorizedToHandleMaterial(
            announcement: $announcement,
            user: $user->getEmail() ?? 'Unknown user'
        );
    }

    public function ensureUserCanManageTask(User $user, TaskCourse $taskCourse): void
    {
        $announcement = $taskCourse->getAnnouncement();

        if (!$announcement) {
            throw UserNotAuthorizedException::userNotAuthorizedToManageTask(
                taskCourse: $taskCourse,
                user: $user->getEmail() ?? 'Unknown user'
            );
        }

        if ($this->userCanManageAnnouncement(user: $user, announcement: $announcement)) {
            return;
        }

        if ($user->isTutor()
            && $this->announcementTutorRepository->isTutorSharedWithAnnouncement($user, $announcement)
        ) {
            return;
        }

        throw UserNotAuthorizedException::userNotAuthorizedToManageTask(
            taskCourse: $taskCourse,
            user: $user->getEmail() ?? 'Unknown user'
        );
    }
}
