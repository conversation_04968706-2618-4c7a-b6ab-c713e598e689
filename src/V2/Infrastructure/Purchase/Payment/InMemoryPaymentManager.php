<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Purchase\Payment;

use App\V2\Domain\Purchase\Exception\PaymentRepositoryException;
use App\V2\Domain\Purchase\Payment\Payment;
use App\V2\Domain\Purchase\Payment\PaymentManagerInterface;
use App\V2\Domain\Purchase\Payment\PaymentRepository;
use App\V2\Domain\Purchase\Payment\PaymentStatus;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Shared\Uuid\UuidGenerator;

readonly class InMemoryPaymentManager implements PaymentManagerInterface
{
    public function __construct(
        private PaymentRepository $paymentRepository,
        private UuidGenerator $uuidGenerator,
    ) {
    }

    /**
     * @throws PaymentRepositoryException
     */
    public function createPayment(Purchase $purchase): Payment
    {
        $payment = new Payment(
            id: $this->uuidGenerator->generate(),
            purchaseId: $purchase->getId(),
            userId: $purchase->getUserId(),
            paymentIntent: 'pi_test_' . uniqid(), // Fake payment intent ID for testing
            amount: $purchase->getAmount(),
            status: PaymentStatus::Pending,
        );

        $this->paymentRepository->put($payment);

        return $payment;
    }

    /**
     * Process webhook payload for testing purposes.
     *
     * This in-memory implementation intentionally does nothing and never throws exceptions.
     * It's designed for testing scenarios where we want to verify the webhook endpoint
     * functionality without making actual external API calls or validating real signatures.
     *
     * Behavior in tests:
     * - Always succeeds (never throws exceptions)
     * - Does not validate payload structure or signature
     * - Does not update payment or purchase states
     * - Returns immediately without processing
     *
     * This allows functional tests to focus on HTTP layer behavior (routing, request/response)
     * rather than business logic, which should be tested separately with unit tests.
     */
    public function processWebhook(string $payload, array $metadata): void
    {
        // Intentionally empty - this is a no-op implementation for testing
        // Real webhook processing logic is tested via unit tests with mocked dependencies
    }
}
