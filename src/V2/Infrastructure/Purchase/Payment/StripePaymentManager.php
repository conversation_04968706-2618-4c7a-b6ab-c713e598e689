<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Purchase\Payment;

use App\Service\SettingsService;
use App\V2\Application\Purchase\PurchaseFactory;
use App\V2\Domain\Purchase\Exception\PaymentManagerException;
use App\V2\Domain\Purchase\Exception\PaymentManagerKeyNotFoundException;
use App\V2\Domain\Purchase\Exception\PaymentNotFoundException;
use App\V2\Domain\Purchase\Exception\PaymentRepositoryException;
use App\V2\Domain\Purchase\Payment\Payment;
use App\V2\Domain\Purchase\Payment\PaymentCriteria;
use App\V2\Domain\Purchase\Payment\PaymentManagerInterface;
use App\V2\Domain\Purchase\Payment\PaymentRepository;
use App\V2\Domain\Purchase\Payment\PaymentStatus;
use App\V2\Domain\Purchase\Purchase;
use App\V2\Domain\Purchase\PurchaseCriteria;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\EntityNotFoundException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\UuidGenerator;
use App\V2\Infrastructure\Shared\Financial\CurrencyCodeTransformer;
use Stripe\Exception\ApiErrorException;

readonly class StripePaymentManager implements PaymentManagerInterface
{
    public function __construct(
        private SettingsService $settingsService,
        private PaymentRepository $paymentRepository,
        private UuidGenerator $uuidGenerator,
        private PurchaseRepository $purchaseRepository,
        private PurchaseFactory $purchaseFactory,
    ) {
    }

    /**
     * @throws ApiErrorException
     * @throws PaymentRepositoryException
     * @throws PaymentManagerKeyNotFoundException
     */
    public function createPayment(Purchase $purchase): Payment
    {
        $secretKey = $this->settingsService->get('payment.stripe.secret_key');

        if (empty(trim($secretKey ?? ''))) {
            throw PaymentManagerKeyNotFoundException::secretKeyNotFound();
        }

        \Stripe\Stripe::setApiKey($secretKey);

        $paymentIntentConfig = [
            'payment_method_types' => ['card'],
            'amount' => $purchase->getAmount()->value(),
            'currency' => CurrencyCodeTransformer::toString($purchase->getAmount()->currency()->code()),
            'metadata' => ['integration_check' => 'accept_a_payment'],
        ];

        $paymentIntent = \Stripe\PaymentIntent::create($paymentIntentConfig);

        $payment = new Payment(
            id: $this->uuidGenerator->generate(),
            purchaseId: $purchase->getId(),
            userId: $purchase->getUserId(),
            paymentIntent: $paymentIntent->id,
            amount: $purchase->getAmount(),
            status: PaymentStatus::Pending,
        );

        $this->paymentRepository->put($payment);

        return $payment;
    }

    /**
     * @throws PaymentManagerException
     * @throws PaymentManagerKeyNotFoundException
     * @throws PaymentNotFoundException
     * @throws PaymentRepositoryException
     * @throws CriteriaException
     */
    public function processWebhook(string $payload, array $metadata): void
    {
        $secretKey = $this->settingsService->get('payment.stripe.secret_key');
        $webhookSecret = $this->settingsService->get('payment.stripe.webhook_secret');
        $signature = $metadata['signature'] ?? '';

        if (empty(trim($secretKey ?? ''))) {
            throw PaymentManagerKeyNotFoundException::secretKeyNotFound();
        }

        if (empty(trim($webhookSecret ?? ''))) {
            throw PaymentManagerKeyNotFoundException::webhookSecretNotFound();
        }

        \Stripe\Stripe::setApiKey($secretKey);

        try {
            $event = \Stripe\Webhook::constructEvent(
                $payload,
                $signature,
                $webhookSecret
            );
        } catch (\UnexpectedValueException $e) {
            throw PaymentManagerException::invalidPayload();
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            throw PaymentManagerException::invalidSignature();
        }

        $object = $event->data->object;

        switch ($event->type) {
            case 'payment_intent.succeeded':
                $this->paymentIntentSucceeded($object);
                // TODO send email

                break;
            case 'payment_intent.payment_failed':
                $this->paymentIntentFailed($object);
                break;
        }
    }

    /**
     * @throws PaymentManagerException
     * @throws CriteriaException
     */
    private function paymentIntentSucceeded($object): void
    {
        try {
            $paymentIntent = $object->id;
            $payment = $this->paymentRepository->findOneBy(
                PaymentCriteria::createEmpty()->filterByPaymentIntent($paymentIntent)
            );

            $purchase = $this->purchaseRepository->findOneBy(
                PurchaseCriteria::createEmpty()->filterById($payment->getPurchaseId())
            );

            // payment as paid
            $paymentPaid = new Payment(
                id: $payment->getId(),
                purchaseId: $payment->getPurchaseId(),
                userId: $payment->getUserId(),
                paymentIntent: $payment->getPaymentIntent(),
                amount: $payment->getAmount(),
                status: PaymentStatus::Paid,
                createdAt: $payment->getCreatedAt(),
                updatedAt: new \DateTimeImmutable(),
                deletedAt: $payment->getDeletedAt(),
            );
            $this->paymentRepository->put($paymentPaid);

            // Update purchase status
            $updatedPurchase = $this->purchaseFactory->updateWithStatus($purchase, PurchaseStatus::Completed);
            $this->purchaseRepository->put($updatedPurchase);
        } catch (InfrastructureException|EntityNotFoundException $exception) {
            throw PaymentManagerException::fromPrevious($exception);
        }
    }

    /**
     * @throws PaymentNotFoundException
     * @throws PaymentRepositoryException
     */
    private function paymentIntentFailed($object): void
    {
        $paymentIntent = $object->id;
        $payment = $this->paymentRepository->findOneBy(
            PaymentCriteria::createEmpty()->filterByPaymentIntent($paymentIntent)
        );
        $paymentFailed = new Payment(
            id: $payment->getId(),
            purchaseId: $payment->getPurchaseId(),
            userId: $payment->getUserId(),
            paymentIntent: $payment->getPaymentIntent(),
            amount: $payment->getAmount(),
            status: PaymentStatus::Failed,
            createdAt: $payment->getCreatedAt(),
            updatedAt: new \DateTimeImmutable(),
            deletedAt: $payment->getDeletedAt(),
        );
        $this->paymentRepository->put($paymentFailed);
    }
}
