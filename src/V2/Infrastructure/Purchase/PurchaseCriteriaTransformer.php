<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Purchase;

use App\V2\Domain\Purchase\PurchaseCriteria;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Sort;
use App\V2\Domain\Shared\Criteria\SortCollection;
use App\V2\Domain\Shared\Financial\Exception\InvalidCurrencyCodeException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Shared\DateTimeTransformer;
use App\V2\Infrastructure\Shared\Financial\MoneyTransformer;
use App\V2\Infrastructure\Shared\QueryParamTransformer\PaginationTransformer;

class PurchaseCriteriaTransformer
{
    /**
     * @throws InvalidCurrencyCodeException
     * @throws CollectionException
     * @throws InvalidUuidException
     */
    public static function fromArray(array $data): PurchaseCriteria
    {
        $criteria = PurchaseCriteria::createEmpty();

        if (isset($data['user_id'])) {
            $criteria->filterByUserId(new Id((int) $data['user_id']));
        }

        if (isset($data['purchasable_id'])) {
            $criteria->filterByPurchasableId(new Uuid($data['purchasable_id']));
        }

        if (isset($data['status'])) {
            $criteria->filterByStatus(PurchaseStatusTransformer::fromString($data['status']));
        }

        if (isset($data['amount_min'])) {
            $criteria->filterByMinAmount(MoneyTransformer::fromPayload([
                'price_amount' => (int) $data['amount_min'],
                'price_currency' => $data['amount_currency'] ?? 'EUR',
            ]));
        }

        if (isset($data['amount_max'])) {
            $criteria->filterByMaxAmount(MoneyTransformer::fromPayload([
                'price_amount' => (int) $data['amount_max'],
                'price_currency' => $data['amount_currency'] ?? 'EUR',
            ]));
        }

        if (isset($data['start_date'])) {
            $criteria->filterByStartDate(DateTimeTransformer::fromInput($data['start_date']));
        }

        if (isset($data['end_date'])) {
            $criteria->filterByEndDate(DateTimeTransformer::fromInput($data['end_date']));
        }

        if (isset($data['sort_by'])) {
            $criteria->sortBy(
                new SortCollection([
                    new Sort(
                        PurchaseSortableTransformer::toSortableField($data['sort_by']),
                        PurchaseSortableTransformer::toSortDirection($data['sort_dir'] ?? 'asc')
                    ),
                ])
            );
        }

        if (isset($data['page']) || isset($data['page_size'])) {
            $criteria->withPagination(
                PaginationTransformer::toPagination($data)
            );
        }

        return $criteria;
    }
}
