<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Payment;

use App\V2\Domain\Purchase\Exception\PaymentManagerException;
use Symfony\Component\HttpFoundation\Request;

class StripePaymentWebhookValidator
{
    /**
     * @throws PaymentManagerException
     */
    public function validateRequest(Request $request): void
    {
        $this->validatePayload($request);
        $this->validateSignature($request);
    }

    /**
     * @throws PaymentManagerException
     */
    private function validatePayload(Request $request): void
    {
        $payload = $request->getContent();

        if (empty(trim($payload))) {
            throw PaymentManagerException::invalidPayload();
        }
    }

    /**
     * @throws PaymentManagerException
     */
    private function validateSignature(Request $request): void
    {
        $signature = $request->headers->get('Stripe-Signature', '');

        if (empty(trim($signature))) {
            throw PaymentManagerException::invalidSignature();
        }
    }
}
