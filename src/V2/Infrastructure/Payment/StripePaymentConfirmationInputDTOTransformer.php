<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Payment;

use App\V2\Application\DTO\Payment\PaymentConfirmationInputDTO;
use Symfony\Component\HttpFoundation\Request;

class StripePaymentConfirmationInputDTOTransformer implements PaymentConfirmationInputDTOTransformer
{
    public static function fromRequest(Request $request): PaymentConfirmationInputDTO
    {
        return new PaymentConfirmationInputDTO(
            payload: $request->getContent(),
            metadata: [
                'signature' => $request->headers->get('Stripe-Signature', ''),
            ],
        );
    }
}
