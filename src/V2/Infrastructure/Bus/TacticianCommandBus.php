<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Bus;

use App\V2\Domain\Bus\Command;
use League\Tactician\CommandBus;

readonly class TacticianCommandBus implements \App\V2\Domain\Bus\CommandBus
{
    public function __construct(private CommandBus $commandBus)
    {
    }

    public function execute(Command $command): mixed
    {
        return $this->commandBus->handle($command);
    }
}
