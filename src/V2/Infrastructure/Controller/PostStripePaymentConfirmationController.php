<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller;

use App\V2\Application\Command\ProcessPaymentConfirmation;
use App\V2\Domain\Purchase\Exception\PaymentManagerException;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Payment\StripePaymentConfirmationInputDTOTransformer;
use App\V2\Infrastructure\Payment\StripePaymentWebhookValidator;
use App\V2\Infrastructure\Response\ApiResponseContent;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostStripePaymentConfirmationController extends CommandBusAccessor
{
    /**
     * @throws PaymentManagerException
     */
    public function __invoke(Request $request, StripePaymentWebhookValidator $webhookValidator): Response
    {
        // Validate request input before processing
        $webhookValidator->validateRequest($request);

        $paymentConfirmationInputDTO = StripePaymentConfirmationInputDTOTransformer::fromRequest($request);

        $this->execute(
            new ProcessPaymentConfirmation($paymentConfirmationInputDTO)
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData([])->toArray(),
            status: Response::HTTP_OK,
        );
    }
}
