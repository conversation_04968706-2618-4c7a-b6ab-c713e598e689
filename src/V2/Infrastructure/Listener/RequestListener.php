<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Listener;

use App\V2\Domain\Security\FirewallInterface;
use App\V2\Infrastructure\Security\FirewallException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Security\Core\User\UserInterface;

readonly class RequestListener implements EventSubscriberInterface
{
    public const string LOGGED_USER_ATTRIBUTE = 'logged_user';

    public function __construct(
        private FirewallInterface $firewall,
        private ContainerInterface $container,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => ['firewall', 5],
        ];
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws FirewallException
     * @throws NotFoundExceptionInterface
     */
    public function firewall($event): void
    {
        $request = $event->getRequest();
        $user = $this->getUser();

        if (!$this->firewall->isGranted($request, $user)) {
            throw FirewallException::forbidden();
        }

        if ($user) {
            $event->getRequest()->attributes->set(self::LOGGED_USER_ATTRIBUTE, $user);
        }
    }

    /**
     * @throws NotFoundExceptionInterface
     * @throws ContainerExceptionInterface
     */
    private function getUser(): ?UserInterface
    {
        if (!$this->container->has('security.token_storage')) {
            throw new \LogicException('Missing SecurityBundle.');
        }

        if (null === $token = $this->container->get('security.token_storage')->getToken()) {
            return null;
        }

        // @deprecated since 5.4, $user will always be a UserInterface instance
        if (!\is_object($user = $token->getUser())) {
            // e.g. anonymous authentication
            return null;
        }

        return $user;
    }
}
