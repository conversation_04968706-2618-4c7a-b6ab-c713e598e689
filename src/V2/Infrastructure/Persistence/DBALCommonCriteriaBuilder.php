<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence;

use App\V2\Domain\Shared\Criteria\Criteria;
use App\V2\Domain\Shared\Criteria\CriteriaId;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Query\QueryBuilder;

class DBALCommonCriteriaBuilder
{
    public static function filterByCommonCriteria(
        Criteria $criteria,
        QueryBuilder $queryBuilder,
        bool $onlyFilter = false,
    ): void {
        if ($criteria instanceof CriteriaId) {
            self::filterByCommonCriteriaId($criteria, $queryBuilder);
        }

        if ($onlyFilter) {
            return;
        }

        if ($criteria->getPagination()) {
            $queryBuilder->setFirstResult($criteria->getPagination()->offset());
            $queryBuilder->setMaxResults($criteria->getPagination()->limit());
        }

        // Apply sorting if set
        $sortBy = $criteria->getSortBy();
        if (null !== $sortBy) {
            foreach ($sortBy->all() as $sortByItem) {
                $queryBuilder->addOrderBy(
                    't.' . $sortByItem->getField()->value(),
                    DBALSortTransformer::fromSortDirection($sortByItem->getDirection())
                );
            }
        }
    }

    private static function filterByCommonCriteriaId(CriteriaId $criteria, QueryBuilder $queryBuilder): void
    {
        if ($criteria->getId()) {
            $queryBuilder->setParameter('id', $criteria->getId()->value());
            $queryBuilder->andWhere('t.id = :id');
        }

        if ($criteria->getIds() && !$criteria->getIds()->isEmpty()) {
            $queryBuilder->setParameter('ids', $criteria->getIds()->all(), ArrayParameterType::STRING);
            $queryBuilder->andWhere('t.id in (:ids)');
        }
    }
}
