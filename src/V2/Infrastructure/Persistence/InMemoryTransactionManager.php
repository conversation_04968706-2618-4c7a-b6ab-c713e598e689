<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence;

use App\V2\Application\Persistence\TransactionManagerInterface;

class InMemoryTransactionManager implements TransactionManagerInterface
{
    private bool $inTransaction = false;

    public function beginTransaction(): void
    {
        $this->inTransaction = true;
    }

    public function commit(): void
    {
        $this->inTransaction = false;
    }

    public function rollback(): void
    {
        $this->inTransaction = false;
    }

    public function transactional(callable $operation): mixed
    {
        $this->beginTransaction();

        try {
            $result = $operation();
            $this->commit();

            return $result;
        } catch (\Throwable $e) {
            $this->rollback();
            throw $e;
        }
    }

    public function isInTransaction(): bool
    {
        return $this->inTransaction;
    }
}
