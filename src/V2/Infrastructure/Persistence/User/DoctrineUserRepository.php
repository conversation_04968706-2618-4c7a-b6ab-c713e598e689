<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\User;

use App\Entity\User;
use App\Repository\UserRepository as LegacyUserRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\User\Exception\UserNotFoundException;
use App\V2\Domain\User\LegacyUserCollection;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;
use App\V2\Infrastructure\Persistence\DoctrineCommonCriteriaBuilder;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;

readonly class DoctrineUserRepository implements UserRepository
{
    public function __construct(
        private EntityManagerInterface $entityManager,
    ) {
    }

    private function getRepository(): EntityRepository|LegacyUserRepository
    {
        return $this->entityManager->getRepository(User::class);
    }

    /**
     * @throws InfrastructureException
     */
    public function put(User $user): void
    {
        try {
            $this->entityManager->persist($user);
            $this->entityManager->flush();
        } catch (\Exception $exception) {
            throw InfrastructureException::fromPrevious($exception);
        }
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotFoundException
     */
    public function findOneBy(UserCriteria $criteria): User
    {
        try {
            return $this->createQueryBuilder($criteria)
                ->getQuery()
                ->getSingleResult();
        } catch (NoResultException $exception) {
            throw new UserNotFoundException();
        } catch (NonUniqueResultException $exception) {
            throw InfrastructureException::fromPrevious($exception);
        }
    }

    /**
     * @throws CollectionException
     */
    public function findBy(UserCriteria $criteria): LegacyUserCollection
    {
        $users = $this
            ->createQueryBuilder($criteria)
            ->getQuery()
            ->getResult();

        return new LegacyUserCollection($users);
    }

    /**
     * @throws InfrastructureException
     */
    public function countBy(UserCriteria $criteria): int
    {
        $queryBuilder = $this->createQueryBuilder(
            criteria: $criteria,
            onlyFilter: true
        );

        try {
            return (int) $queryBuilder
                ->select('COUNT(u.id)')
                ->getQuery()
                ->getSingleScalarResult();
        } catch (NoResultException|NonUniqueResultException $exception) {
            throw InfrastructureException::fromPrevious($exception);
        }
    }

    private function createQueryBuilder(UserCriteria $criteria, bool $onlyFilter = false): QueryBuilder
    {
        $queryBuilder = $this->getRepository()->createQueryBuilder('u');

        // Apply filters from criteria
        if (null !== $criteria->getIsActive()) {
            $queryBuilder
                ->andWhere('u.isActive = :isActive')
                ->setParameter('isActive', $criteria->getIsActive());
        }

        if (null !== $criteria->getStartDate()) {
            $queryBuilder
                ->andWhere('u.createdAt >= :startDate')
                ->setParameter('startDate', $criteria->getStartDate());
        }

        if (null !== $criteria->getEndDate()) {
            $queryBuilder
                ->andWhere('u.createdAt <= :endDate')
                ->setParameter('endDate', $criteria->getEndDate());
        }

        if (null !== $criteria->getSearch()) {
            $queryBuilder
                ->andWhere('u.email LIKE :search
                    OR u.firstName LIKE :search
                    OR u.lastName LIKE :search
                    OR CONCAT(u.firstName, \' \', u.lastName) LIKE :search
                ')
                ->setParameter('search', '%' . $criteria->getSearch() . '%');
        }

        if (null !== $criteria->getRole()) {
            $queryBuilder
                ->andWhere('JSON_CONTAINS(u.roles, :role) = 1')
                ->setParameter('role', '"' . $criteria->getRole() . '"');
        }

        if ($criteria->needsToHideSuperAdmin()) {
            $queryBuilder
                ->andWhere('JSON_CONTAINS(u.roles, :superAdminRole) = 0')
                ->setParameter('superAdminRole', '"ROLE_SUPER_ADMIN"');
        }

        if (null !== $criteria->getFilters()) {
            $filters = $criteria->getFilters();
            $addCreatedBy = $criteria->getAddCreatedBy();

            // Group filters by category
            $filtersByCategory = [];
            foreach ($filters->all() as $filter) {
                $categoryId = $filter->getFilterCategory()->getId();
                if (!isset($filtersByCategory[$categoryId])) {
                    $filtersByCategory[$categoryId] = [];
                }
                $filtersByCategory[$categoryId][] = $filter->getId();
            }

            // If there are filters and/or addCreatedBy, we need a compound condition
            if (!empty($filtersByCategory) || null !== $addCreatedBy) {
                $subQuery = $this->getRepository()->createQueryBuilder('uc')
                    ->select('distinct(uc.id)');

                // Condition for filters
                if (!empty($filtersByCategory)) {
                    $filtersQuery = $this->getRepository()->createQueryBuilder('uf')
                        ->select('distinct(uf.id)');

                    foreach ($filtersByCategory as $categoryId => $filterIds) {
                        $filtersQuery->innerJoin('uf.filter', 'f' . $categoryId);
                        $filtersQuery->andWhere('f' . $categoryId . '.id IN (:filterIds' . $categoryId . ')');
                        $queryBuilder->setParameter('filterIds' . $categoryId, $filterIds, ArrayParameterType::INTEGER);
                    }

                    $subQuery->orWhere(
                        $subQuery->expr()->in('uc.id', $filtersQuery->getDQL())
                    );
                }

                // Condition for addCreatedBy
                if (null !== $addCreatedBy) {
                    $subQuery->orWhere('uc.createdBy = :createdBy');
                    $queryBuilder->setParameter('createdBy', $addCreatedBy);
                }

                // Add the compound condition to the query builder
                $queryBuilder->andWhere($queryBuilder->expr()->in('u.id', $subQuery->getDQL()));
            }
        }

        if (null !== $criteria->getEmail()) {
            $queryBuilder
                ->andWhere('u.email = :email')
                ->setParameter('email', $criteria->getEmail()->value());
        }

        DoctrineCommonCriteriaBuilder::filterByCommonCriteria(
            criteria: $criteria,
            queryBuilder: $queryBuilder,
            onlyFilter: $onlyFilter
        );

        return $queryBuilder;
    }
}
