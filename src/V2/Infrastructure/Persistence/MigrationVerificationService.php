<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence;

use App\V2\Infrastructure\Persistence\MigrationReader\MigrationReaderService;
use App\V2\Infrastructure\Persistence\SchemaAnalyzer\DBALSchemaAnalyzerService;
use App\V2\Infrastructure\Persistence\SchemaAnalyzer\MigrationSchemaAnalyzerService;
use App\V2\Infrastructure\Persistence\SchemaComparator\SchemaComparatorService;

class MigrationVerificationService
{
    public function __construct(
        private readonly MigrationReaderService $migrationReader,
        private readonly MigrationSchemaAnalyzerService $migrationSchemaAnalyzer,
        private readonly DBALSchemaAnalyzerService $databaseSchemaAnalyzer,
        private readonly SchemaComparatorService $schemaComparator
    ) {
    }

    /**
     * Obtiene la estructura esperada de la base de datos basada en las migraciones.
     */
    public function getExpectedDatabaseStructure(): array
    {
        // Leer todas las migraciones
        $migrations = $this->migrationReader->readAllMigrations();

        // Analizar las migraciones para extraer la estructura esperada
        $expectedStructure = $this->migrationSchemaAnalyzer->analyzeStructure($migrations);

        return [
            'migrations' => $migrations,
            'structure' => $expectedStructure,
        ];
    }

    /**
     * Obtiene la estructura actual de la base de datos.
     */
    public function getCurrentDatabaseStructure(): array
    {
        return $this->databaseSchemaAnalyzer->getCurrentStructure();
    }

    /**
     * Compara las estructuras y devuelve las diferencias encontradas.
     */
    public function compareStructures(
        array $expectedStructure,
        array $currentStructure,
        ?string $specificTable = null
    ): array {
        return $this->schemaComparator->compare(
            $expectedStructure['structure'],
            $currentStructure,
            $specificTable
        );
    }
}
