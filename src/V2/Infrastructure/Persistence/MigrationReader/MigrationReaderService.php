<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\MigrationReader;

use Symfony\Component\Finder\Finder;

class MigrationReaderService
{
    private string $migrationsPath;

    public function __construct(string $projectDir)
    {
        $this->migrationsPath = $projectDir . '/src/Migrations';
    }

    /**
     * Lee todas las migraciones y extrae la información SQL.
     */
    public function readAllMigrations(): array
    {
        $migrations = [];

        $finder = new Finder();
        $finder->files()
            ->in($this->migrationsPath)
            ->name('Version*.php')
            ->sortByName();

        foreach ($finder as $file) {
            $migrationData = $this->parseMigrationFile($file->getPathname());
            if ($migrationData) {
                $migrations[] = $migrationData;
            }
        }

        return $migrations;
    }

    /**
     * Parsea un archivo de migración individual.
     */
    private function parseMigrationFile(string $filePath): ?array
    {
        $content = file_get_contents($filePath);
        if (!$content) {
            return null;
        }

        $filename = basename($filePath, '.php');

        // Extraer la clase de migración
        if (!preg_match('/class\s+(\w+)\s+extends/', $content, $classMatches)) {
            return null;
        }

        $className = $classMatches[1];

        // Extraer la descripción
        $description = '';
        if (preg_match(
            '/public function getDescription\(\):\s*string\s*\{[^}]*return\s*[\'"]([^\'"]*)[\'"]/',
            $content,
            $descMatches
        )) {
            $description = $descMatches[1];
        }

        // Extraer todas las sentencias SQL del método up()
        $upSqlStatements = $this->extractSqlStatements($content, 'up');

        // Extraer todas las sentencias SQL del método down()
        $downSqlStatements = $this->extractSqlStatements($content, 'down');

        return [
            'filename' => $filename,
            'class' => $className,
            'description' => $description,
            'up_sql' => $upSqlStatements,
            'down_sql' => $downSqlStatements,
            'file_path' => $filePath,
        ];
    }

    /**
     * Extrae las sentencias SQL de un método específico (up o down).
     */
    private function extractSqlStatements(string $content, string $method): array
    {
        $sqlStatements = [];

        // Buscar el método específico
        $pattern = '/public function ' . $method . '\(Schema \$schema\):\s*void\s*\{(.*?)\}/s';
        if (!preg_match($pattern, $content, $methodMatches)) {
            return $sqlStatements;
        }

        $methodContent = $methodMatches[1];

        // Extraer todas las llamadas a addSql() con heredoc
        // Usar un enfoque más específico para manejar delimitadores heredoc correctamente
        $addSqlPattern = '/\$this->addSql\s*\(\s*<<<[\'"]?(\w+)[\'"]?\s*\n(.*?)\n\s*\1\s*\)/s';
        if (preg_match_all($addSqlPattern, $methodContent, $heredocMatches, PREG_SET_ORDER)) {
            foreach ($heredocMatches as $match) {
                $sql = trim($match[2]);
                if (!empty($sql)) {
                    $sqlStatements[] = $sql;
                }
            }
        }

        // También buscar addSql con strings normales (usando una aproximación más robusta)
        // Buscar todas las llamadas a addSql y extraer el contenido entre comillas
        if (preg_match_all(
            '/\$this->addSql\s*\(\s*([\'"])(.*?)\1(?:\s*,\s*\[[^\]]*\])?\s*\);/s',
            $methodContent,
            $stringMatches,
            PREG_SET_ORDER
        )) {
            foreach ($stringMatches as $match) {
                $sql = trim($match[2]);
                if (!empty($sql)) {
                    // Desescapar las comillas
                    $sql = str_replace(['\\\'', '\\"'], ["'", '"'], $sql);
                    $sqlStatements[] = $sql;
                }
            }
        }

        // Buscar addSql con concatenación de strings multilínea
        $multilinePattern = '/\$this->addSql\s*\(\s*[\'"]([^\'"]*)[\'"]\s*\.\s*[\r\n\s]*[\'"]([^\'"]*)[\'"]/s';
        if (preg_match_all($multilinePattern, $methodContent, $multilineMatches, PREG_SET_ORDER)) {
            foreach ($multilineMatches as $match) {
                $sql = trim($match[1] . ' ' . $match[2]);
                if (!empty($sql)) {
                    $sqlStatements[] = $sql;
                }
            }
        }

        return $sqlStatements;
    }
}
