<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\SchemaAnalyzer;

class MigrationSchemaAnalyzerService
{
    /**
     * Analyzes migrations and builds the expected database structure.
     */
    public function analyzeStructure(array $migrations): array
    {
        $structure = [
            'tables' => [],
            'foreign_keys' => [],
            'indexes' => [],
        ];

        foreach ($migrations as $migration) {
            foreach ($migration['up_sql'] as $sql) {
                $this->processSqlStatement($sql, $structure);
            }
        }

        return $structure;
    }

    /**
     * Processes an individual SQL statement and updates the structure.
     */
    private function processSqlStatement(string $sql, array &$structure): void
    {
        $sql = trim($sql);
        $sqlUpper = strtoupper($sql);

        if (str_starts_with($sqlUpper, 'CREATE TABLE')) {
            $this->processCreateTable($sql, $structure);
        } elseif (str_starts_with($sqlUpper, 'ALTER TABLE')) {
            $this->processAlterTable($sql, $structure);
        } elseif (str_starts_with($sqlUpper, 'DROP TABLE')) {
            $this->processDropTable($sql, $structure);
        } elseif (str_starts_with($sqlUpper, 'CREATE INDEX') || str_starts_with($sqlUpper, 'CREATE UNIQUE INDEX')) {
            $this->processCreateIndex($sql, $structure);
        } elseif (str_starts_with($sqlUpper, 'DROP INDEX')) {
            $this->processDropIndex($sql, $structure);
        }
    }

    /**
     * Processes a CREATE TABLE statement.
     */
    private function processCreateTable(string $sql, array &$structure): void
    {
        // Extract table name (with support for IF NOT EXISTS)
        if (!preg_match('/CREATE TABLE\s+(?:IF NOT EXISTS\s*)?`?(\w+)`?\s*\(/i', $sql, $matches)) {
            return;
        }

        $tableName = $matches[1];

        if (!isset($structure['tables'][$tableName])) {
            $structure['tables'][$tableName] = [
                'columns' => [],
                'primary_key' => null,
                'indexes' => [],
                'foreign_keys' => [],
            ];
        }

        // Extract column definitions
        $this->extractColumnsFromCreateTable($sql, $structure['tables'][$tableName]);

        // Extract primary key
        $this->extractPrimaryKeyFromCreateTable($sql, $structure['tables'][$tableName]);

        // Extract inline indexes
        $this->extractInlineIndexesFromCreateTable($sql, $structure['tables'][$tableName], $tableName);

        // Extract inline foreign keys
        $this->extractInlineForeignKeysFromCreateTable($sql, $structure['tables'][$tableName], $tableName);
    }

    /**
     * Extracts columns from a CREATE TABLE statement.
     */
    private function extractColumnsFromCreateTable(string $sql, array &$tableStructure): void
    {
        // Get content between parentheses (with support for IF NOT EXISTS)
        if (!preg_match('/CREATE TABLE(?:\s+IF NOT EXISTS\s*)?[^(]+\((.*)\)[^)]*$/is', $sql, $matches)) {
            return;
        }

        $tableContent = $matches[1];

        // Split by lines or by commas (to handle SQL in a single line)
        $lines = explode("\n", $tableContent);

        // If everything is in a single line, split by commas
        if (1 == \count($lines) && str_contains($tableContent, ',')) {
            // Split by commas, but being careful not to split inside parentheses or quotes
            $parts = $this->splitSqlByCommas($tableContent);
            $lines = $parts;
        }

        foreach ($lines as $line) {
            $line = trim($line, " \t\n\r\0\x0B,");

            if (empty($line)
                || str_starts_with(strtoupper($line), 'PRIMARY KEY')
                || str_starts_with(strtoupper($line), 'INDEX')
                || str_starts_with(strtoupper($line), 'KEY')
                || str_starts_with(strtoupper($line), 'UNIQUE')
                || str_starts_with(strtoupper($line), 'FOREIGN KEY')
                || str_starts_with(strtoupper($line), 'CONSTRAINT')
                || str_starts_with(strtoupper($line), 'REFERENCES')
                || str_contains(strtoupper($line), 'CHARACTER SET')
                || str_contains(strtoupper($line), 'ENGINE')) {
                continue;
            }

            // Extract column definition
            if (preg_match('/^`?(\w+)`?\s+(.+)$/i', $line, $columnMatches)) {
                $columnName = $columnMatches[1];
                $columnDefinition = trim($columnMatches[2]);

                $tableStructure['columns'][$columnName] = $columnDefinition;
            }
        }
    }

    /**
     * Parses index columns from a string like "col1, col2" or "`col1`, `col2`".
     */
    private function parseIndexColumns(string $columnsStr): array
    {
        $columns = [];
        $parts = explode(',', $columnsStr);

        foreach ($parts as $part) {
            $part = trim($part);
            // Remove backticks if they exist
            $part = trim($part, '`');
            if (!empty($part)) {
                $columns[] = $part;
            }
        }

        return $columns;
    }

    /**
     * Extracts the primary key from a CREATE TABLE statement.
     */
    private function extractPrimaryKeyFromCreateTable(string $sql, array &$tableStructure): void
    {
        // Look for PRIMARY KEY with one or multiple columns
        if (preg_match('/PRIMARY KEY\s*\(\s*([^)]+)\s*\)/i', $sql, $matches)) {
            $columnsStr = $matches[1];

            // Parse columns (can be multiple separated by commas)
            $columns = $this->parseIndexColumns($columnsStr);

            // If it's a single column, save as string for compatibility
            // If multiple, save as array
            $tableStructure['primary_key'] = 1 === \count($columns) ? $columns[0] : $columns;
        }
    }

    /**
     * Extracts inline indexes from a CREATE TABLE statement.
     */
    private function extractInlineIndexesFromCreateTable(string $sql, array &$tableStructure, string $tableName): void
    {
        // Look for indexes of type INDEX name (column1, column2, ...)
        if (preg_match_all('/(?:^|,)\s*INDEX\s+`?(\w+)`?\s*\(\s*([^)]+)\s*\)/im', $sql, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $indexName = $match[1];
                $columnsStr = $match[2];

                // Parse columns (can be multiple separated by commas)
                $columns = $this->parseIndexColumns($columnsStr);

                $tableStructure['indexes'][] = [
                    'name' => $indexName,
                    'columns' => $columns,
                    'unique' => false,
                ];
            }
        }

        // Look for unique indexes of type UNIQUE INDEX name (column1, column2, ...)
        if (preg_match_all('/(?:^|,)\s*UNIQUE\s+INDEX\s+`?(\w+)`?\s*\(\s*([^)]+)\s*\)/im', $sql, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $indexName = $match[1];
                $columnsStr = $match[2];

                // Parse columns (can be multiple separated by commas)
                $columns = $this->parseIndexColumns($columnsStr);

                $tableStructure['indexes'][] = [
                    'name' => $indexName,
                    'columns' => $columns,
                    'unique' => true,
                ];
            }
        }
    }

    /**
     * Extracts inline foreign keys from a CREATE TABLE statement.
     */
    private function extractInlineForeignKeysFromCreateTable(string $sql, array &$tableStructure, string $tableName): void
    {
        // Look for CONSTRAINT name FOREIGN KEY (column) REFERENCES table (column)
        $pattern = '/CONSTRAINT\s+`?(\w+)`?\s+FOREIGN KEY\s*\(\s*`?(\w+)`?\s*\)\s+REFERENCES\s+`?(\w+)`?\s*\(\s*`?(\w+)`?\s*\)/i';
        if (preg_match_all($pattern, $sql, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $constraintName = $match[1];
                $localColumn = $match[2];
                $foreignTable = $match[3];
                $foreignColumn = $match[4];

                $tableStructure['foreign_keys'][] = [
                    'name' => $constraintName,
                    'local_column' => $localColumn,
                    'foreign_table' => $foreignTable,
                    'foreign_column' => $foreignColumn,
                ];

                // Note: MySQL automatically creates indexes for foreign keys at the database level,
                // but the migration schema analyzer only tracks explicitly defined indexes in migrations.
                // The automatic index creation is handled by MySQL itself, not by the migration.
            }
        }

        // Look for FOREIGN KEY without explicit names: FOREIGN KEY (column) REFERENCES table (column)
        // MySQL assigns automatic names like table_ibfk_1, table_ibfk_2, etc.
        $patternUnnamed = '/(?:^|,)\s*FOREIGN KEY\s*\(\s*`?(\w+)`?\s*\)\s+REFERENCES\s+`?(\w+)`?\s*\(\s*`?(\w+)`?\s*\)(?:\s+ON\s+DELETE\s+\w+)?(?:\s+ON\s+UPDATE\s+\w+)?/im';
        if (preg_match_all($patternUnnamed, $sql, $matches, PREG_SET_ORDER)) {
            $ibfkCounter = 1;
            foreach ($matches as $match) {
                $localColumn = $match[1];
                $foreignTable = $match[2];
                $foreignColumn = $match[3];

                // MySQL generates automatic names like table_ibfk_1, table_ibfk_2, etc.
                $constraintName = $tableName . '_ibfk_' . $ibfkCounter;
                ++$ibfkCounter;

                $tableStructure['foreign_keys'][] = [
                    'name' => $constraintName,
                    'local_column' => $localColumn,
                    'foreign_table' => $foreignTable,
                    'foreign_column' => $foreignColumn,
                ];

                // Note: MySQL automatically creates indexes for foreign keys at the database level,
                // but the migration schema analyzer only tracks explicitly defined indexes in migrations.
                // The automatic index creation is handled by MySQL itself, not by the migration.
            }
        }
    }

    /**
     * Processes an ALTER TABLE statement.
     */
    private function processAlterTable(string $sql, array &$structure): void
    {
        // Extract table name
        if (!preg_match('/ALTER TABLE\s+`?(\w+)`?\s+(.*)/is', $sql, $matches)) {
            return;
        }

        $tableName = $matches[1];
        $alterStatement = $matches[2];

        // Ensure the table exists in the structure
        if (!isset($structure['tables'][$tableName])) {
            $structure['tables'][$tableName] = [
                'columns' => [],
                'primary_key' => null,
                'indexes' => [],
                'foreign_keys' => [],
            ];
        }

        $this->processAlterStatement($alterStatement, $structure['tables'][$tableName], $tableName);
    }

    /**
     * Processes the different ALTER TABLE operations.
     */
    private function processAlterStatement(string $statement, array &$tableStructure, string $tableName): void
    {
        // ALTER TABLE operations can have multiple operations separated by commas
        // We need to split them carefully respecting parentheses and quotes
        $operations = $this->splitAlterOperations($statement);

        foreach ($operations as $operation) {
            $operation = trim($operation);
            $operationUpper = strtoupper($operation);

            if (str_starts_with($operationUpper, 'ADD COLUMN') || (str_starts_with($operationUpper, 'ADD ') && !str_contains($operationUpper, 'ADD CONSTRAINT') && !str_contains($operationUpper, 'ADD INDEX'))) {
                $this->processAddColumn($operation, $tableStructure);
            } elseif (str_starts_with($operationUpper, 'DROP COLUMN') || (str_starts_with($operationUpper, 'DROP ') && !str_contains($operationUpper, 'DROP FOREIGN KEY') && !str_contains($operationUpper, 'DROP INDEX'))) {
                $this->processDropColumn($operation, $tableStructure);
            } elseif (str_starts_with($operationUpper, 'CHANGE ')) {
                $this->processChangeColumn($operation, $tableStructure);
            } elseif (str_starts_with($operationUpper, 'MODIFY ')) {
                $this->processModifyColumn($operation, $tableStructure);
            } elseif (str_starts_with($operationUpper, 'RENAME COLUMN')) {
                $this->processRenameColumn($operation, $tableStructure);
            } elseif (str_contains($operationUpper, 'ADD CONSTRAINT') && str_contains($operationUpper, 'FOREIGN KEY')) {
                $this->processAddForeignKey($operation, $tableStructure);
            } elseif (str_contains($operationUpper, 'DROP FOREIGN KEY')) {
                $this->processDropForeignKey($operation, $tableStructure);
            }
        }
    }

    /**
     * Processes ADD COLUMN.
     */
    private function processAddColumn(string $statement, array &$tableStructure): void
    {
        // The operation already comes as a single ADD operation from splitAlterOperations
        // Example: "ADD created_by_id INT DEFAULT NULL"
        if (preg_match('/ADD\s+(?:COLUMN\s+)?`?(\w+)`?\s+(.+)/i', $statement, $matches)) {
            $columnName = $matches[1];
            $columnDefinition = trim($matches[2]);

            $tableStructure['columns'][$columnName] = $columnDefinition;
        }
    }

    /**
     * Processes DROP COLUMN.
     */
    private function processDropColumn(string $statement, array &$tableStructure): void
    {
        if (preg_match('/DROP\s+(?:COLUMN\s+)?`?(\w+)`?/i', $statement, $matches)) {
            $columnName = $matches[1];
            unset($tableStructure['columns'][$columnName]);
        }
    }

    /**
     * Processes CHANGE COLUMN (can change name and definition).
     */
    private function processChangeColumn(string $statement, array &$tableStructure): void
    {
        // CHANGE old_column_name new_column_name column_definition
        if (preg_match('/CHANGE\s+`?(\w+)`?\s+`?(\w+)`?\s+(.+)/i', $statement, $matches)) {
            $oldColumnName = $matches[1];
            $newColumnName = $matches[2];
            $columnDefinition = trim($matches[3]);

            // Remove the old column
            unset($tableStructure['columns'][$oldColumnName]);

            // Add the new column
            $tableStructure['columns'][$newColumnName] = $columnDefinition;
        }
    }

    /**
     * Processes MODIFY COLUMN (changes only the definition, keeps the name).
     */
    private function processModifyColumn(string $statement, array &$tableStructure): void
    {
        // MODIFY column_name column_definition
        if (preg_match('/MODIFY\s+(?:COLUMN\s+)?`?(\w+)`?\s+(.+)/i', $statement, $matches)) {
            $columnName = $matches[1];
            $columnDefinition = trim($matches[2]);

            $tableStructure['columns'][$columnName] = $columnDefinition;
        }
    }

    /**
     * Processes RENAME COLUMN.
     */
    private function processRenameColumn(string $statement, array &$tableStructure): void
    {
        // RENAME COLUMN old_name TO new_name
        if (preg_match('/RENAME COLUMN\s+`?(\w+)`?\s+TO\s+`?(\w+)`?/i', $statement, $matches)) {
            $oldColumnName = $matches[1];
            $newColumnName = $matches[2];

            if (isset($tableStructure['columns'][$oldColumnName])) {
                $columnDefinition = $tableStructure['columns'][$oldColumnName];
                unset($tableStructure['columns'][$oldColumnName]);
                $tableStructure['columns'][$newColumnName] = $columnDefinition;
            }
        }
    }

    /**
     * Processes ADD CONSTRAINT ... FOREIGN KEY.
     */
    private function processAddForeignKey(string $statement, array &$tableStructure): void
    {
        $pattern = '/ADD CONSTRAINT\s+`?(\w+)`?\s+FOREIGN KEY\s*\(\s*`?(\w+)`?\s*\)\s+REFERENCES\s+`?(\w+)`?\s*\(\s*`?(\w+)`?\s*\)/i';
        if (preg_match($pattern, $statement, $matches)) {
            $constraintName = $matches[1];
            $localColumn = $matches[2];
            $foreignTable = $matches[3];
            $foreignColumn = $matches[4];

            $tableStructure['foreign_keys'][] = [
                'name' => $constraintName,
                'local_column' => $localColumn,
                'foreign_table' => $foreignTable,
                'foreign_column' => $foreignColumn,
            ];

            // Note: MySQL automatically creates indexes for foreign keys at the database level,
            // but the migration schema analyzer only tracks explicitly defined indexes in migrations.
            // The automatic index creation is handled by MySQL itself, not by the migration.
        }
    }

    /**
     * Processes DROP FOREIGN KEY.
     */
    private function processDropForeignKey(string $statement, array &$tableStructure): void
    {
        if (preg_match('/DROP FOREIGN KEY\s+`?(\w+)`?/i', $statement, $matches)) {
            $constraintName = $matches[1];

            $tableStructure['foreign_keys'] = array_filter(
                $tableStructure['foreign_keys'],
                fn ($fk) => $fk['name'] !== $constraintName
            );
        }
    }

    /**
     * Processes DROP TABLE.
     */
    private function processDropTable(string $sql, array &$structure): void
    {
        if (preg_match('/DROP TABLE\s+`?(\w+)`?/i', $sql, $matches)) {
            $tableName = $matches[1];
            unset($structure['tables'][$tableName]);
        }
    }

    /**
     * Processes CREATE INDEX.
     */
    private function processCreateIndex(string $sql, array &$structure): void
    {
        $pattern = '/CREATE\s+(?:UNIQUE\s+)?INDEX\s+`?(\w+)`?\s+ON\s+`?(\w+)`?\s*\(\s*`?(\w+)`?\s*\)/i';
        if (preg_match($pattern, $sql, $matches)) {
            $indexName = $matches[1];
            $tableName = $matches[2];
            $columnName = $matches[3];
            $isUnique = str_contains(strtoupper($sql), 'UNIQUE');

            if (!isset($structure['tables'][$tableName])) {
                $structure['tables'][$tableName] = [
                    'columns' => [],
                    'primary_key' => null,
                    'indexes' => [],
                    'foreign_keys' => [],
                ];
            }

            $structure['tables'][$tableName]['indexes'][] = [
                'name' => $indexName,
                'columns' => [$columnName],
                'unique' => $isUnique,
            ];
        }
    }

    /**
     * Processes DROP INDEX.
     */
    private function processDropIndex(string $sql, array &$structure): void
    {
        $pattern = '/DROP INDEX\s+`?(\w+)`?\s+ON\s+`?(\w+)`?/i';
        if (preg_match($pattern, $sql, $matches)) {
            $indexName = $matches[1];
            $tableName = $matches[2];

            if (isset($structure['tables'][$tableName])) {
                $structure['tables'][$tableName]['indexes'] = array_filter(
                    $structure['tables'][$tableName]['indexes'],
                    fn ($index) => $index['name'] !== $indexName
                );
            }
        }
    }

    /**
     * Splits an SQL string by commas, respecting parentheses and quotes.
     */
    private function splitSqlByCommas(string $sql): array
    {
        $parts = [];
        $current = '';
        $inQuotes = false;
        $quoteChar = '';
        $parenthesesLevel = 0;
        $length = \strlen($sql);

        for ($i = 0; $i < $length; ++$i) {
            $char = $sql[$i];

            if (!$inQuotes) {
                if ('"' === $char || "'" === $char) {
                    $inQuotes = true;
                    $quoteChar = $char;
                } elseif ('(' === $char) {
                    ++$parenthesesLevel;
                } elseif (')' === $char) {
                    --$parenthesesLevel;
                } elseif (',' === $char && 0 === $parenthesesLevel) {
                    $parts[] = trim($current);
                    $current = '';
                    continue;
                }
            } else {
                if ($char === $quoteChar) {
                    // Check if it's an escaped quote
                    if ($i > 0 && '\\' !== $sql[$i - 1]) {
                        $inQuotes = false;
                        $quoteChar = '';
                    }
                }
            }

            $current .= $char;
        }

        if (!empty(trim($current))) {
            $parts[] = trim($current);
        }

        return $parts;
    }

    /**
     * Splits ALTER TABLE operations separated by commas.
     */
    private function splitAlterOperations(string $statement): array
    {
        $operations = [];
        $current = '';
        $inQuotes = false;
        $quoteChar = '';
        $parenthesesLevel = 0;
        $length = \strlen($statement);

        for ($i = 0; $i < $length; ++$i) {
            $char = $statement[$i];

            if (!$inQuotes) {
                if ('"' === $char || "'" === $char) {
                    $inQuotes = true;
                    $quoteChar = $char;
                } elseif ('(' === $char) {
                    ++$parenthesesLevel;
                } elseif (')' === $char) {
                    --$parenthesesLevel;
                } elseif (',' === $char && 0 === $parenthesesLevel) {
                    $operations[] = trim($current);
                    $current = '';
                    continue;
                }
            } else {
                if ($char === $quoteChar) {
                    // Check if it's an escaped quote
                    if ($i > 0 && '\\' !== $statement[$i - 1]) {
                        $inQuotes = false;
                        $quoteChar = '';
                    }
                }
            }

            $current .= $char;
        }

        if (!empty(trim($current))) {
            $operations[] = trim($current);
        }

        return $operations;
    }

    /**
     * Generates a Doctrine-style index name.
     * Doctrine uses a specific algorithm to generate automatic index names.
     */
    private function generateDoctrineIndexName(string $tableName, array $columns, bool $isUnique = false): string
    {
        $prefix = $isUnique ? 'UNIQ_' : 'IDX_';

        // Doctrine uses a hash based on table and columns
        // The exact algorithm is complex, but we can use an approximation
        $data = $tableName . implode('', $columns);
        $hash = strtoupper(substr(md5($data), 0, 10));

        return $prefix . $hash;
    }
}
