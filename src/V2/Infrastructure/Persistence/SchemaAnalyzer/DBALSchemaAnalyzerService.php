<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\SchemaAnalyzer;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;

readonly class DBALSchemaAnalyzerService
{
    public function __construct(
        private Connection $connection,
    ) {
    }

    /**
     * Obtiene la estructura actual de la base de datos.
     *
     * @throws Exception
     */
    public function getCurrentStructure(): array
    {
        $schemaManager = $this->connection->createSchemaManager();

        $structure = [
            'tables' => [],
        ];

        $tables = $schemaManager->listTables();

        foreach ($tables as $table) {
            $tableName = $table->getName();

            $structure['tables'][$tableName] = [
                'columns' => $this->getTableColumns($table),
                'primary_key' => $this->getTablePrimaryKey($table),
                'indexes' => $this->getTableIndexes($table),
                'foreign_keys' => $this->getTableForeignKeys($table),
            ];
        }

        return $structure;
    }

    /**
     * Obtiene las columnas de una tabla.
     */
    private function getTableColumns(\Doctrine\DBAL\Schema\Table $table): array
    {
        $columns = [];

        foreach ($table->getColumns() as $column) {
            $columnName = $column->getName();
            $columnDefinition = $this->buildColumnDefinition($column);

            $columns[$columnName] = $columnDefinition;
        }

        return $columns;
    }

    /**
     * Construye la definición de una columna en formato similar al SQL.
     */
    private function buildColumnDefinition(\Doctrine\DBAL\Schema\Column $column): string
    {
        $definition = [];

        // Tipo de dato
        $type = $column->getType()->getName();
        $definition[] = strtoupper($type);

        // Longitud para tipos que la soportan
        if (null !== $column->getLength() && \in_array($type, ['string', 'varchar', 'char'])) {
            $definition[] = '(' . $column->getLength() . ')';
        }

        // Precisión y escala para decimales
        if (
            null !== $column->getPrecision()
            && null !== $column->getScale()
            && \in_array($type, ['decimal', 'numeric'])
        ) {
            $definition[] = '(' . $column->getPrecision() . ',' . $column->getScale() . ')';
        }

        // NOT NULL
        if ($column->getNotnull()) {
            $definition[] = 'NOT NULL';
        }

        // AUTO_INCREMENT
        if ($column->getAutoincrement()) {
            $definition[] = 'AUTO_INCREMENT';
        }

        // DEFAULT
        if (null !== $column->getDefault()) {
            $default = $column->getDefault();
            if (\is_string($default)) {
                $definition[] = "DEFAULT '" . $default . "'";
            } else {
                $definition[] = 'DEFAULT ' . $default;
            }
        }

        // COMMENT
        if (null !== $column->getComment() && '' !== $column->getComment()) {
            $definition[] = "COMMENT '" . $column->getComment() . "'";
        }

        return implode(' ', $definition);
    }

    /**
     * Obtiene la primary key de una tabla.
     */
    private function getTablePrimaryKey(\Doctrine\DBAL\Schema\Table $table): ?string
    {
        if (!$table->hasPrimaryKey()) {
            return null;
        }

        $primaryKey = $table->getPrimaryKey();
        $columns = $primaryKey->getColumns();

        // Por simplicidad, asumimos primary keys de una sola columna
        return 1 === \count($columns) ? $columns[0] : implode(',', $columns);
    }

    /**
     * Obtiene los índices de una tabla.
     */
    private function getTableIndexes(\Doctrine\DBAL\Schema\Table $table): array
    {
        $indexes = [];

        foreach ($table->getIndexes() as $index) {
            // Saltar la primary key ya que la manejamos por separado
            if ($index->isPrimary()) {
                continue;
            }

            $indexes[] = [
                'name' => $index->getName(),
                'columns' => $index->getColumns(),
                'unique' => $index->isUnique(),
            ];
        }

        return $indexes;
    }

    /**
     * Obtiene las foreign keys de una tabla.
     */
    private function getTableForeignKeys(\Doctrine\DBAL\Schema\Table $table): array
    {
        $foreignKeys = [];

        foreach ($table->getForeignKeys() as $foreignKey) {
            $localColumns = $foreignKey->getLocalColumns();
            $foreignColumns = $foreignKey->getForeignColumns();

            // Por simplicidad, asumimos foreign keys de una sola columna
            $foreignKeys[] = [
                'name' => $foreignKey->getName(),
                'local_column' => 1 === \count($localColumns) ? $localColumns[0] : implode(',', $localColumns),
                'foreign_table' => $foreignKey->getForeignTableName(),
                'foreign_column' => 1 === \count($foreignColumns) ? $foreignColumns[0] : implode(',', $foreignColumns),
            ];
        }

        return $foreignKeys;
    }

    /**
     * Verifica si una tabla existe en la base de datos.
     */
    public function tableExists(string $tableName): bool
    {
        $schemaManager = $this->connection->createSchemaManager();

        return $schemaManager->tablesExist([$tableName]);
    }

    /**
     * Obtiene información específica de una tabla.
     */
    public function getTableStructure(string $tableName): ?array
    {
        if (!$this->tableExists($tableName)) {
            return null;
        }

        $schemaManager = $this->connection->createSchemaManager();
        $table = $schemaManager->introspectTable($tableName);

        return [
            'columns' => $this->getTableColumns($table),
            'primary_key' => $this->getTablePrimaryKey($table),
            'indexes' => $this->getTableIndexes($table),
            'foreign_keys' => $this->getTableForeignKeys($table),
        ];
    }
}
