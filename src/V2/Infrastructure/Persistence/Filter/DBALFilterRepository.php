<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Filter;

use App\V2\Domain\Filter\Exception\FilterNotFoundException;
use App\V2\Domain\Filter\Filter;
use App\V2\Domain\Filter\FilterCollection;
use App\V2\Domain\Filter\FilterCriteria;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Sort;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Domain\Shared\Criteria\SortCollection;
use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Exception\NullException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Infrastructure\Persistence\DBALCommonCriteriaBuilder;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DBALFilterRepository implements FilterRepository
{
    public function __construct(
        private Connection $connection,
        private string $filterTableName,
    ) {
    }

    #[\Override]
    public function put(Filter $filter): void
    {
        try {
            if (null === $filter->getId()->valueOrNull()) {
                $this->insert($filter);

                return;
            }

            $this->update($filter);
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InfrastructureException
     * @throws NullException
     * @throws DBALException
     */
    private function insert(Filter $filter): void
    {
        $this->connection->insert(
            table: $this->filterTableName,
            data: $this->fromFilterToArray($filter),
        );

        $lastId = $this->connection->lastInsertId();
        if (false === $lastId) {
            throw new InfrastructureException('Failed to generate auto-increment ID for Filter');
        }

        $filter->setId(new Id((int) $lastId));
    }

    /**
     * @throws NullException
     * @throws DBALException
     */
    private function update(Filter $filter): void
    {
        $data = $this->fromFilterToArray($filter);
        unset($data['id']);
        $this->connection->update(
            table: $this->filterTableName,
            data: $data,
            criteria: ['id' => $filter->getId()->value()],
        );
    }

    #[\Override]
    public function findOneBy(FilterCriteria $criteria): Filter
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new FilterNotFoundException();
            }

            return $this->fromArrayToFilter($result);
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findBy(FilterCriteria $criteria): FilterCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new FilterCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToFilter($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @param array<string, mixed> $values
     */
    private function fromArrayToFilter(array $values): Filter
    {
        return new Filter(
            id: new Id($values['id']),
            filterCategoryId: new Id($values['filter_category_id']),
            name: $values['name'],
            code: $values['code'],
            sort: $values['sort'],
            parentId: isset($values['parent_id']) ? new Id($values['parent_id']) : null,
        );
    }

    /**
     * @return array<string, mixed>
     *
     * @throws NullException
     */
    private function fromFilterToArray(Filter $filter): array
    {
        return array_merge(
            null !== $filter->getId()->valueOrNull() ? ['id' => $filter->getId()->value()] : [],
            [
                'filter_category_id' => $filter->getFilterCategoryId()->value(),
                'name' => $filter->getName(),
                'code' => $filter->getCode(),
                'sort' => $filter->getSort(),
                'parent_id' => $filter->getParentId()?->value(),
                // source
            ]
        );
    }

    /**
     * @throws CollectionException
     */
    private function getQueryBuilderByCriteria(FilterCriteria $criteria): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->filterTableName, 't');

        if (null !== $criteria->getCategoryId()) {
            $queryBuilder->andWhere('t.filter_category_id = :categoryId')
                ->setParameter('categoryId', $criteria->getCategoryId()->value());
        }

        if (null !== $criteria->getCategoriesIds()) {
            $queryBuilder->andWhere('t.filter_category_id IN (:categoriesIds)')
                ->setParameter('categoriesIds', $criteria->getCategoriesIds()->all(), ArrayParameterType::STRING);
        }

        if (null !== $criteria->getParentId()) {
            $queryBuilder->andWhere('t.parent_id = :parentId')
                ->setParameter('parentId', $criteria->getParentId()->value());
        }

        if (null !== $criteria->getSearch()) {
            $queryBuilder->andWhere('t.name LIKE :search OR t.code LIKE :search')
                ->setParameter('search', '%' . $criteria->getSearch() . '%');
        }

        $criteria->sortBy(
            new SortCollection([
                new Sort(
                    field: new SortableField('sort'),
                    direction: SortDirection::ASC,
                ),
            ])
        );

        DBALCommonCriteriaBuilder::filterByCommonCriteria(
            criteria: $criteria,
            queryBuilder: $queryBuilder,
        );

        return $queryBuilder;
    }
}
