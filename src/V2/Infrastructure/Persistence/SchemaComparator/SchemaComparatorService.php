<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\SchemaComparator;

class SchemaComparatorService
{
    /**
     * Tablas que deben ser ignoradas en la comparación.
     * Estas son tablas internas del sistema que no aparecen en las migraciones.
     */
    private const IGNORED_TABLES = [
        'migration_versions', // Tabla interna de Doctrine Migrations
    ];

    /**
     * Patrones de nombres de índices que Doctrine genera automáticamente.
     * Estos índices pueden existir en la base de datos pero no en las migraciones.
     */
    private const DOCTRINE_AUTO_INDEX_PATTERNS = [
        '/^IDX_[A-F0-9]{10,16}$/', // Índices automáticos de Doctrine: IDX_1234567890 o IDX_1234567890ABCDEF
        '/^UNIQ_[A-F0-9]{10,16}$/', // Índices únicos automáticos de Doctrine: UNIQ_1234567890 o UNIQ_1234567890ABCDEF
    ];

    /**
     * Compara la estructura esperada con la actual y devuelve las diferencias.
     */
    public function compare(array $expectedStructure, array $currentStructure, ?string $specificTable = null): array
    {
        $differences = [];

        $expectedTables = $expectedStructure['tables'] ?? [];
        $currentTables = $currentStructure['tables'] ?? [];

        // Filtrar tablas ignoradas de la estructura actual
        $currentTables = array_filter($currentTables, function ($tableName) {
            return !\in_array($tableName, self::IGNORED_TABLES);
        }, ARRAY_FILTER_USE_KEY);

        // Si se especifica una tabla, filtrar solo esa tabla
        if (null !== $specificTable) {
            $expectedTables = isset($expectedTables[$specificTable]) ? [$specificTable => $expectedTables[$specificTable]] : [];
            $currentTables = isset($currentTables[$specificTable]) ? [$specificTable => $currentTables[$specificTable]] : [];
        }

        // Comparar tablas
        $differences = array_merge($differences, $this->compareTables($expectedTables, $currentTables));

        return $differences;
    }

    /**
     * Compara las tablas entre la estructura esperada y actual.
     */
    private function compareTables(array $expectedTables, array $currentTables): array
    {
        $differences = [];

        // Buscar tablas faltantes
        foreach ($expectedTables as $tableName => $expectedTable) {
            if (!isset($currentTables[$tableName])) {
                $differences[] = [
                    'type' => 'missing_table',
                    'table' => $tableName,
                    'expected_structure' => $expectedTable,
                ];
                continue;
            }

            // Comparar estructura de la tabla
            $differences = array_merge(
                $differences,
                $this->compareTableStructure($tableName, $expectedTable, $currentTables[$tableName])
            );
        }

        // Buscar tablas extra
        foreach ($currentTables as $tableName => $currentTable) {
            if (!isset($expectedTables[$tableName])) {
                $differences[] = [
                    'type' => 'extra_table',
                    'table' => $tableName,
                ];
            }
        }

        return $differences;
    }

    /**
     * Compara la estructura de una tabla específica.
     */
    private function compareTableStructure(string $tableName, array $expectedTable, array $currentTable): array
    {
        $differences = [];

        // Comparar columnas
        $differences = array_merge(
            $differences,
            $this->compareColumns($tableName, $expectedTable['columns'] ?? [], $currentTable['columns'] ?? [])
        );

        // Comparar foreign keys
        $differences = array_merge(
            $differences,
            $this->compareForeignKeys($tableName, $expectedTable['foreign_keys'] ?? [], $currentTable['foreign_keys'] ?? [])
        );

        // Comparar índices
        $differences = array_merge(
            $differences,
            $this->compareIndexes($tableName, $expectedTable['indexes'] ?? [], $currentTable['indexes'] ?? [])
        );

        return $differences;
    }

    /**
     * Compara las columnas de una tabla.
     */
    private function compareColumns(string $tableName, array $expectedColumns, array $currentColumns): array
    {
        $differences = [];

        // Buscar columnas faltantes
        foreach ($expectedColumns as $columnName => $expectedDefinition) {
            if (!isset($currentColumns[$columnName])) {
                $differences[] = [
                    'type' => 'missing_column',
                    'table' => $tableName,
                    'column' => $columnName,
                    'expected_definition' => $expectedDefinition,
                ];
                continue;
            }

            // Comparar definición de columna
            $currentDefinition = $currentColumns[$columnName];
            if (!$this->compareColumnDefinitions($expectedDefinition, $currentDefinition)) {
                $differences[] = [
                    'type' => 'column_mismatch',
                    'table' => $tableName,
                    'column' => $columnName,
                    'expected' => $expectedDefinition,
                    'actual' => $currentDefinition,
                ];
            }
        }

        // Buscar columnas extra
        foreach ($currentColumns as $columnName => $currentDefinition) {
            if (!isset($expectedColumns[$columnName])) {
                $differences[] = [
                    'type' => 'extra_column',
                    'table' => $tableName,
                    'column' => $columnName,
                ];
            }
        }

        return $differences;
    }

    /**
     * Compara las definiciones de columnas.
     */
    private function compareColumnDefinitions(string $expected, string $actual): bool
    {
        // Normalizar las definiciones para comparación
        $expectedNormalized = $this->normalizeColumnDefinition($expected);
        $actualNormalized = $this->normalizeColumnDefinition($actual);

        return $expectedNormalized === $actualNormalized;
    }

    /**
     * Normaliza una definición de columna para comparación.
     */
    private function normalizeColumnDefinition(string $definition): string
    {
        // Convertir a mayúsculas
        $normalized = strtoupper(trim($definition));

        // Normalizar espacios múltiples
        $normalized = preg_replace('/\s+/', ' ', $normalized);

        // Quitar COLLATE y CHARACTER SET (orden importa - antes de otros cambios)
        $normalized = preg_replace('/\s+COLLATE\s+\w+/', '', $normalized);
        $normalized = preg_replace('/\s+CHARACTER\s+SET\s+\w+/', '', $normalized);

        // Normalizar tipos con paréntesis PRIMERO (antes del mapeo de tipos)
        $normalized = preg_replace('/STRING\s*\(\s*(\d+)\s*\)/', 'VARCHAR($1)', $normalized);
        $normalized = preg_replace('/DECIMAL\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)/', 'NUMERIC($1, $2)', $normalized);

        // Normalizar INT con display width: int(11) -> INT (pero no TINYINT(1))
        $normalized = preg_replace('/\bINT\s*\(\s*\d+\s*\)/', 'INT', $normalized);

        // Normalizar tipos de datos equivalentes (orden importa - más específicos primero)
        $typeMapping = [
            'DOUBLE PRECISION' => 'FLOAT',
            'DATETIME_IMMUTABLE' => 'DATETIME',
            'INTEGER' => 'INT',
            'BOOLEAN' => 'TINYINT(1)',
            'STRING' => 'VARCHAR',
            'JSON' => 'LONGTEXT',
            'ARRAY' => 'LONGTEXT',
        ];

        foreach ($typeMapping as $from => $to) {
            $normalized = str_replace($from, $to, $normalized);
        }

        // Caso especial: TINYINT(1) sin DEFAULT implícitamente es DEFAULT NULL
        if (str_contains($normalized, 'TINYINT(1)') && !str_contains($normalized, 'DEFAULT') && !str_contains($normalized, 'NOT NULL')) {
            $normalized = str_replace('TINYINT(1)', 'TINYINT(1) DEFAULT NULL', $normalized);
        }

        // Normalizar TEXT a LONGTEXT (pero no LONGTEXT a LONGLONGTEXT)
        $normalized = preg_replace('/\bTEXT\b/', 'LONGTEXT', $normalized);

        // Normalizar CHAR a VARCHAR
        $normalized = preg_replace('/\bCHAR\b/', 'VARCHAR', $normalized);

        // Normalizar orden de AUTO_INCREMENT y NOT NULL
        // Mover AUTO_INCREMENT al final
        if (preg_match('/(.+?)\s+AUTO_INCREMENT\s+(.+)/', $normalized, $matches)) {
            $normalized = trim($matches[1] . ' ' . $matches[2]) . ' AUTO_INCREMENT';
        }

        // Normalizar DEFAULT values y NULL/NOT NULL
        // Primero, extraer y normalizar DEFAULT values
        $defaultValue = '';
        if (preg_match('/DEFAULT\s+\'([^\']+)\'/', $normalized, $matches)) {
            $defaultValue = " DEFAULT '" . $matches[1] . "'";
        } elseif (preg_match('/DEFAULT\s+([^\s]+)/', $normalized, $matches)) {
            $defaultValue = ' DEFAULT ' . $matches[1];
        }

        // Normalizar DEFAULT values para BOOLEAN/TINYINT(1)
        // '1' y 1 son equivalentes, '0' y 0 son equivalentes
        // TRUE y FALSE también son equivalentes a 1 y 0
        if ($defaultValue) {
            $defaultValue = str_ireplace(' DEFAULT TRUE', ' DEFAULT 1', $defaultValue);
            $defaultValue = str_ireplace(' DEFAULT FALSE', ' DEFAULT 0', $defaultValue);
            $defaultValue = str_replace(" DEFAULT '1'", ' DEFAULT 1', $defaultValue);
            $defaultValue = str_replace(" DEFAULT '0'", ' DEFAULT 0', $defaultValue);
        }

        // Remover todos los DEFAULT para normalización
        $normalized = preg_replace('/\s+DEFAULT\s+[^\s]+/', '', $normalized);
        $normalized = preg_replace('/\s+DEFAULT\s+\'[^\']*\'/', '', $normalized);

        // Remover comentarios para comparación (pueden variar en formato)
        $normalized = preg_replace('/\s+COMMENT\s+\'[^\']*\'/', '', $normalized);
        $normalized = preg_replace('/\s+COMMENT\s+\([^)]*\)/', '', $normalized);

        // Para la comparación, ignorar diferencias de NULL/NOT NULL
        // Esto es controvertido pero según los ejemplos del usuario,
        // campos como "INT DEFAULT NULL" vs "INTEGER NOT NULL" deben considerarse iguales
        $normalized = preg_replace('/\s+(NOT\s+)?NULL/', '', $normalized);

        // Agregar DEFAULT solo si no es DEFAULT NULL (que es implícito para campos nullable)
        if ($defaultValue && !str_contains($defaultValue, 'DEFAULT NULL')) {
            $normalized .= $defaultValue;
        }

        // Limpiar espacios extra después de todas las transformaciones
        $normalized = preg_replace('/\s+/', ' ', $normalized);

        return trim($normalized);
    }

    /**
     * Compara las foreign keys de una tabla.
     */
    private function compareForeignKeys(string $tableName, array $expectedForeignKeys, array $currentForeignKeys): array
    {
        $differences = [];

        // Crear mapas por nombre de constraint para comparación
        $expectedFkMap = [];
        foreach ($expectedForeignKeys as $fk) {
            $expectedFkMap[$fk['name']] = $fk;
        }

        $currentFkMap = [];
        foreach ($currentForeignKeys as $fk) {
            $currentFkMap[$fk['name']] = $fk;
        }

        // Buscar foreign keys faltantes
        foreach ($expectedFkMap as $fkName => $expectedFk) {
            if (!isset($currentFkMap[$fkName])) {
                $differences[] = [
                    'type' => 'missing_foreign_key',
                    'table' => $tableName,
                    'constraint' => $fkName,
                    'expected_definition' => $this->formatForeignKeyDefinition($expectedFk),
                ];
                continue;
            }

            // Comparar definición de foreign key
            $currentFk = $currentFkMap[$fkName];
            if (!$this->compareForeignKeyDefinitions($expectedFk, $currentFk)) {
                $differences[] = [
                    'type' => 'foreign_key_mismatch',
                    'table' => $tableName,
                    'constraint' => $fkName,
                    'expected' => $this->formatForeignKeyDefinition($expectedFk),
                    'actual' => $this->formatForeignKeyDefinition($currentFk),
                ];
            }
        }

        // Buscar foreign keys extra
        foreach ($currentFkMap as $fkName => $currentFk) {
            if (!isset($expectedFkMap[$fkName])) {
                $differences[] = [
                    'type' => 'extra_foreign_key',
                    'table' => $tableName,
                    'constraint' => $fkName,
                ];
            }
        }

        return $differences;
    }

    /**
     * Compara las definiciones de foreign keys.
     */
    private function compareForeignKeyDefinitions(array $expected, array $actual): bool
    {
        return $expected['local_column'] === $actual['local_column']
               && $expected['foreign_table'] === $actual['foreign_table']
               && $expected['foreign_column'] === $actual['foreign_column'];
    }

    /**
     * Formatea una definición de foreign key para mostrar.
     */
    private function formatForeignKeyDefinition(array $fk): string
    {
        return \sprintf(
            'FOREIGN KEY (%s) REFERENCES %s (%s)',
            $fk['local_column'],
            $fk['foreign_table'],
            $fk['foreign_column']
        );
    }

    /**
     * Compara los índices de una tabla.
     */
    private function compareIndexes(string $tableName, array $expectedIndexes, array $currentIndexes): array
    {
        $differences = [];

        // Crear mapas por funcionalidad (columnas + unique) para comparación inteligente
        $expectedFunctionalMap = [];
        foreach ($expectedIndexes as $index) {
            $key = $this->getIndexFunctionalKey($index);
            $expectedFunctionalMap[$key] = $index;
        }

        $currentFunctionalMap = [];
        foreach ($currentIndexes as $index) {
            $key = $this->getIndexFunctionalKey($index);
            $currentFunctionalMap[$key] = $index;
        }

        // Buscar índices faltantes (por funcionalidad)
        foreach ($expectedFunctionalMap as $functionalKey => $expectedIndex) {
            if (!isset($currentFunctionalMap[$functionalKey])) {
                $differences[] = [
                    'type' => 'missing_index',
                    'table' => $tableName,
                    'index' => $expectedIndex['name'],
                    'expected_definition' => $this->formatIndexDefinition($expectedIndex),
                ];
            }
        }

        // Buscar índices extra (por funcionalidad)
        foreach ($currentFunctionalMap as $functionalKey => $currentIndex) {
            if (!isset($expectedFunctionalMap[$functionalKey])) {
                // Verificar si es un índice automático de Doctrine que debería ser ignorado
                if ($this->isDoctrineAutoIndex($currentIndex['name'])) {
                    continue; // Ignorar índices automáticos de Doctrine
                }

                $differences[] = [
                    'type' => 'extra_index',
                    'table' => $tableName,
                    'index' => $currentIndex['name'],
                ];
            }
        }

        return $differences;
    }

    /**
     * Genera una clave funcional para un índice basada en columnas y tipo.
     */
    private function getIndexFunctionalKey(array $index): string
    {
        $columns = \is_array($index['columns']) ? $index['columns'] : [$index['columns']];
        sort($columns); // Normalizar orden

        $unique = $index['unique'] ?? false;

        return implode(',', $columns) . '|' . ($unique ? 'unique' : 'normal');
    }

    /**
     * Compara las definiciones de índices.
     */
    private function compareIndexDefinitions(array $expected, array $actual): bool
    {
        return $expected['columns'] === $actual['columns']
               && $expected['unique'] === $actual['unique'];
    }

    /**
     * Formatea una definición de índice para mostrar.
     */
    private function formatIndexDefinition(array $index): string
    {
        $type = $index['unique'] ? 'UNIQUE INDEX' : 'INDEX';
        $columns = implode(', ', $index['columns']);

        return \sprintf('%s (%s)', $type, $columns);
    }

    /**
     * Verifica si un nombre de índice es un índice automático generado por Doctrine.
     */
    private function isDoctrineAutoIndex(string $indexName): bool
    {
        foreach (self::DOCTRINE_AUTO_INDEX_PATTERNS as $pattern) {
            if (preg_match($pattern, $indexName)) {
                return true;
            }
        }

        return false;
    }
}
