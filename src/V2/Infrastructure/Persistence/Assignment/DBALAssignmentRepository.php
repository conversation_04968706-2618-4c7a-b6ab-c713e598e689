<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Assignment;

use App\V2\Domain\Assignment\Assignment;
use App\V2\Domain\Assignment\AssignmentCollection;
use App\V2\Domain\Assignment\AssignmentCriteria;
use App\V2\Domain\Assignment\AssignmentRepository;
use App\V2\Domain\Assignment\Exception\AssignmentNotFoundException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Resource\Resource;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Persistence\DBALCommonCriteriaBuilder;
use App\V2\Infrastructure\Persistence\DBALDateTimeFormatter;
use App\V2\Infrastructure\Shared\Resource\ResourceTypeTransformer;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DBALAssignmentRepository implements AssignmentRepository
{
    public function __construct(
        private Connection $connection,
        private string $assignmentTableName,
    ) {
    }

    #[\Override]
    public function create(Assignment $assignment): void
    {
        try {
            $this->connection->insert(
                table: $this->assignmentTableName,
                data: $this->fromAssignmentToArray($assignment),
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findOneBy(AssignmentCriteria $criteria): Assignment
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->setMaxResults(1)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new AssignmentNotFoundException();
            }

            return $this->fromArrayToAssignment($result);
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function findBy(AssignmentCriteria $criteria): AssignmentCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new AssignmentCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToAssignment($values),
                    $result
                )
            );
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    #[\Override]
    public function delete(Assignment $assignment): void
    {
        try {
            $assignment->markAsDeleted();

            $this->update($assignment);
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    /**
     * @throws InfrastructureException
     */
    private function update(Assignment $assignment): void
    {
        try {
            $data = $this->fromAssignmentToArray($assignment);
            unset($data['id']); // Don't update the ID

            $result = $this->connection->update(
                table: $this->assignmentTableName,
                data: $data,
                criteria: ['id' => $assignment->getId()->value()]
            );

            if (0 === $result) {
                throw new AssignmentNotFoundException();
            }
        } catch (DBALException $e) {
            throw InfrastructureException::fromPrevious($e);
        }
    }

    private function getQueryBuilderByCriteria(AssignmentCriteria $criteria): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->assignmentTableName, 't')
            ->andWhere('t.deleted_at IS NULL');

        // Apply Assignment-specific filters following V2 DBAL pattern
        if (null !== $criteria->getUserId()) {
            $qb->andWhere('t.user_id = :userId')
                ->setParameter('userId', $criteria->getUserId()->value());
        }

        if (null !== $criteria->getResource()) {
            $qb->andWhere('t.resource_type = :resourceType AND t.resource_id = :resourceId')
                ->setParameter('resourceType', ResourceTypeTransformer::toString($criteria->getResource()->getType()))
                ->setParameter('resourceId', $criteria->getResource()->getId()->value());
        }

        if (null !== $criteria->getResourceType()) {
            $qb->andWhere('t.resource_type = :resourceTypeFilter')
                ->setParameter('resourceTypeFilter', ResourceTypeTransformer::toString($criteria->getResourceType()));
        }

        // Apply common criteria (ID, IDs, pagination, sorting)
        DBALCommonCriteriaBuilder::filterByCommonCriteria($criteria, $qb);

        return $qb;
    }

    /**
     * @return array<string, mixed>
     */
    private function fromAssignmentToArray(Assignment $assignment): array
    {
        return [
            'id' => $assignment->getId()->value(),
            'user_id' => $assignment->getUserId()->value(),
            'resource_type' => ResourceTypeTransformer::toString($assignment->getResource()->getType()),
            'resource_id' => $assignment->getResource()->getId()->value(),
            'created_at' => DBALDateTimeFormatter::format($assignment->getCreatedAt()),
            'updated_at' => null !== $assignment->getUpdatedAt()
                ? DBALDateTimeFormatter::format($assignment->getUpdatedAt())
                : null,
            'deleted_at' => null !== $assignment->getDeletedAt()
                ? DBALDateTimeFormatter::format($assignment->getDeletedAt())
                : null,
        ];
    }

    /**
     * @param array<string, mixed> $values
     */
    private function fromArrayToAssignment(array $values): Assignment
    {
        $resourceType = ResourceTypeTransformer::fromString($values['resource_type']);

        return new Assignment(
            id: new Uuid($values['id']),
            userId: new Id($values['user_id']),
            resource: new Resource(
                type: $resourceType,
                id: ResourceTypeTransformer::getIdentifier($resourceType, $values['resource_id'])
            ),
            createdAt: DBALDateTimeFormatter::parse($values['created_at']),
            updatedAt: isset($values['updated_at'])
                ? DBALDateTimeFormatter::parse($values['updated_at'])
                : null,
            deletedAt: isset($values['deleted_at'])
                ? DBALDateTimeFormatter::parse($values['deleted_at'])
                : null
        );
    }
}
