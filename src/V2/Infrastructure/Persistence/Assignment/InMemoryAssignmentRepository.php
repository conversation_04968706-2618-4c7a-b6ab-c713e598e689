<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Assignment;

use App\V2\Domain\Assignment\Assignment;
use App\V2\Domain\Assignment\AssignmentCollection;
use App\V2\Domain\Assignment\AssignmentCriteria;
use App\V2\Domain\Assignment\AssignmentRepository;
use App\V2\Domain\Assignment\Exception\AssignmentNotFoundException;
use App\V2\Domain\Assignment\Exception\AssignmentRepositoryException;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Infrastructure\Persistence\InMemoryCommonCriteriaBuilder;

class InMemoryAssignmentRepository implements AssignmentRepository
{
    private AssignmentCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new AssignmentCollection([]);
    }

    #[\Override]
    public function create(Assignment $assignment): void
    {
        try {
            $this->findOneBy(AssignmentCriteria::createById($assignment->getId()));
            throw AssignmentRepositoryException::duplicateAssignment($assignment);
        } catch (AssignmentNotFoundException) {
            // Assignment doesn't exist, safe to create
        }

        $assignments = $this->collection->allIndexedById();
        $assignments[$assignment->getId()->value()] = clone $assignment;
        $this->collection->replace($assignments);
    }

    #[\Override]
    public function findOneBy(AssignmentCriteria $criteria): Assignment
    {
        $result = $this->filterByCriteria($criteria);

        if ($result->isEmpty()) {
            throw new AssignmentNotFoundException();
        }

        return $result->first();
    }

    #[\Override]
    public function findBy(AssignmentCriteria $criteria): AssignmentCollection
    {
        return $this->filterByCriteria($criteria);
    }

    #[\Override]
    public function delete(Assignment $assignment): void
    {
        $existingAssignment = $this->findOneBy(
            AssignmentCriteria::createById($assignment->getId())
        );

        $existingAssignment->markAsDeleted();

        $assignments = $this->collection->allIndexedById();
        $assignments[$assignment->getId()->value()] = $existingAssignment;
        $this->collection->replace($assignments);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(AssignmentCriteria $criteria): AssignmentCollection
    {
        /** @var AssignmentCollection $collection */
        $collection = InMemoryCommonCriteriaBuilder::filterByCommonCriteria($criteria, $this->collection);

        // Apply all filters in a single pass to avoid multiple collection iterations
        $collection = $collection->filter(
            fn (Assignment $assignment): bool => (
                null === $assignment->getDeletedAt()
            ) && (
                null === $criteria->getUserId()
                || $assignment->getUserId()->equals($criteria->getUserId())
            ) && (
                null === $criteria->getResource()
                || $assignment->getResource()->equals($criteria->getResource())
            ) && (
                null === $criteria->getResourceType()
                || $assignment->getResource()->getType() === $criteria->getResourceType()
            )
        );

        return $collection->map(
            function (Assignment $assignment) {
                return new Assignment(
                    id: $assignment->getId(),
                    userId: $assignment->getUserId(),
                    resource: $assignment->getResource(),
                    createdAt: $assignment->getCreatedAt(),
                    updatedAt: $assignment->getUpdatedAt(),
                    deletedAt: $assignment->getDeletedAt(),
                );
            }
        );
    }
}
