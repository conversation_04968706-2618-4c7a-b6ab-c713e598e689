<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence;

use App\V2\Application\Persistence\TransactionManagerInterface;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;

readonly class DBALTransactionManager implements TransactionManagerInterface
{
    public function __construct(
        private Connection $connection,
    ) {
    }

    /**
     * @throws Exception
     */
    public function beginTransaction(): void
    {
        $this->connection->beginTransaction();
    }

    /**
     * @throws Exception
     */
    public function commit(): void
    {
        $this->connection->commit();
    }

    /**
     * @throws Exception
     */
    public function rollback(): void
    {
        $this->connection->rollback();
    }

    /**
     * @throws \Throwable
     * @throws Exception
     */
    public function transactional(callable $operation): mixed
    {
        $this->beginTransaction();

        try {
            $result = $operation();
            $this->commit();

            return $result;
        } catch (\Throwable $e) {
            $this->rollback();
            throw $e;
        }
    }
}
