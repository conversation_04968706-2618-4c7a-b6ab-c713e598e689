<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Exam;

use App\V2\Domain\Exam\Exam;
use App\V2\Domain\Exam\ExamCollection;
use App\V2\Domain\Exam\ExamCriteria;
use App\V2\Domain\Exam\ExamRepository;
use App\V2\Domain\Exam\Exception\ExamNotFoundException;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Infrastructure\Persistence\InMemoryCommonCriteriaBuilder;

class InMemoryExamRepository implements ExamRepository
{
    private ExamCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new ExamCollection([]);
    }

    public function put(Exam $exam): void
    {
        $examItems = $this->collection->allIndexedById();
        $examItems[$exam->getId()->value()] = clone $exam;
        $this->collection->replace($examItems);
    }

    /**
     * @throws ExamNotFoundException
     * @throws CollectionException
     */
    public function findOneBy(ExamCriteria $criteria): Exam
    {
        $result = $this->filterByCriteria($criteria);

        if ($result->isEmpty()) {
            throw new ExamNotFoundException();
        }

        return $result->first();
    }

    public function findBy(ExamCriteria $criteria): ExamCollection
    {
        return $this->filterByCriteria($criteria);
    }

    /**
     * @throws ExamNotFoundException
     * @throws CollectionException
     * @throws CriteriaException
     */
    public function delete(Exam $exam): void
    {
        $existingExam = $this->findOneBy(
            ExamCriteria::createEmpty()
                ->filterById($exam->getId())
        );

        $existingExam->markAsDeleted();

        $examItems = $this->collection->allIndexedById();
        $examItems[$exam->getId()->value()] = $existingExam;
        $this->collection->replace($examItems);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(ExamCriteria $criteria): ExamCollection
    {
        $collection = $this->collection->filter(
            fn (Exam $exam): bool => (
                null === $exam->getDeletedAt()
            ) && (
                null === $criteria->getSearch()
                    || str_contains($exam->getTitle(), $criteria->getSearch())
                    || str_contains($exam->getDescription(), $criteria->getSearch())
            )
        );

        /** @var ExamCollection $collection */
        $collection = InMemoryCommonCriteriaBuilder::filterByCommonCriteria($criteria, $collection);

        return $collection->map(
            fn (Exam $exam) => new Exam(
                id: $exam->getId(),
                title: $exam->getTitle(),
                description: $exam->getDescription(),
                createdAt: $exam->getCreatedAt(),
                updatedAt: $exam->getUpdatedAt(),
                deletedAt: $exam->getDeletedAt(),
            )
        );
    }
}
