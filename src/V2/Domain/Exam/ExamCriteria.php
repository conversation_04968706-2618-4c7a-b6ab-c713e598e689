<?php

declare(strict_types=1);

namespace App\V2\Domain\Exam;

use App\V2\Domain\Shared\Criteria\CriteriaWithUuid;

class ExamCriteria extends CriteriaWithUuid
{
    private ?string $search = null;

    public function isEmpty(): bool
    {
        return parent::isEmpty()
            && null === $this->search;
    }

    public function filterBySearch(string $search): self
    {
        $this->search = $search;

        return $this;
    }

    public function getSearch(): ?string
    {
        return $this->search;
    }
}
