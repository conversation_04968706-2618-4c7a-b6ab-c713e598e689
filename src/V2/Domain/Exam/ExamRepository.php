<?php

declare(strict_types=1);

namespace App\V2\Domain\Exam;

use App\V2\Domain\Exam\Exception\ExamNotFoundException;
use App\V2\Domain\Shared\Exception\InfrastructureException;

interface ExamRepository
{
    /**
     * @throws InfrastructureException
     */
    public function put(Exam $exam): void;

    /**
     * @throws ExamNotFoundException
     * @throws InfrastructureException
     */
    public function findOneBy(ExamCriteria $criteria): Exam;

    /**
     * @throws InfrastructureException
     */
    public function findBy(ExamCriteria $criteria): ExamCollection;

    /**
     * @throws ExamNotFoundException
     * @throws InfrastructureException
     */
    public function delete(Exam $exam): void;
}
