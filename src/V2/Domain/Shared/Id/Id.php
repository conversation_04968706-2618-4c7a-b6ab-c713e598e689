<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Id;

use App\V2\Domain\Shared\Exception\NullException;
use App\V2\Domain\Shared\Identifier;

readonly class Id implements Identifier
{
    public function __construct(private ?int $value)
    {
    }

    /**
     * @throws NullException when requesting the value, only if the flow is wrong it will throw the exception
     */
    public function value(): int
    {
        if (null === $this->value) {
            throw new NullException();
        }

        return $this->value;
    }

    public function valueOrNull(): ?int
    {
        return $this->value;
    }

    public function equals(Identifier $id): bool
    {
        return $id instanceof self && $this->value === $id->value();
    }

    public function __toString(): string
    {
        return (string) $this->value;
    }
}
