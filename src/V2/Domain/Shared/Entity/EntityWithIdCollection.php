<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Entity;

use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\Shared\Uuid\UuidCollection;

/**
 * @template T
 *
 * @extends Collection<T>
 */
abstract class EntityWithIdCollection extends Collection
{
    public function allIndexedById(): array
    {
        /**
         * @var EntityWithId[] $entities
         */
        $entities = $this->all();
        $indexedEntities = [];

        foreach ($entities as $entity) {
            $indexedEntities[$entity->getId()->value()] = $entity;
        }

        return $indexedEntities;
    }

    /**
     * @throws CollectionException
     * @throws EntityWithIdCollectionException
     */
    public function allIds(): IdCollection|UuidCollection
    {
        /**
         * @var EntityWithId[] $entities
         */
        $entities = $this->all();
        $ids = [];

        foreach ($entities as $entity) {
            $ids[] = $entity->getId();
        }

        if (empty($ids)) {
            throw EntityWithIdCollectionException::cannotGetIdsFromEmptyCollection(
                $this->getTypeName()
            );
        }

        if ($ids[0] instanceof Uuid) {
            return new UuidCollection($ids);
        }

        return new IdCollection($ids);
    }
}
