<?php

declare(strict_types=1);

namespace App\V2\Domain\Shared\Criteria;

use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Identifier;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\Shared\Uuid\UuidCollection;

/**
 * @extends Criteria<CriteriaWithUuid>
 *
 * @template T of CriteriaWithUuid
 */
abstract class CriteriaWithUuid extends Criteria implements CriteriaId
{
    private ?Uuid $id = null;
    private ?UuidCollection $ids = null;

    protected function isEmpty(): bool
    {
        return null === $this->id && null === $this->ids;
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    /**
     * @throws CriteriaException
     */
    public function filterById(Identifier $id): static
    {
        if (!$id instanceof Uuid) {
            throw CriteriaException::invalidParamType(
                expected: Uuid::class,
                actual: \get_class($id),
            );
        }
        $this->id = $id;

        return $this;
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     */
    public function filterByIds(Collection $ids): static
    {
        if (!$ids instanceof UuidCollection) {
            throw CollectionException::invalidCollectionType(
                expectedType: UuidCollection::class,
                actualType: \get_class($ids),
            );
        }

        if ($ids->isEmpty()) {
            throw CriteriaException::filterByEmptyIdsIsNotAllowed();
        }

        $this->ids = $ids;

        return $this;
    }

    public function getIds(): ?Collection
    {
        return $this->ids;
    }

    /**
     * @throws CriteriaException
     */
    public static function createById(Identifier $id): static
    {
        return self::createEmpty()->filterById($id);
    }

    /**
     * @throws CollectionException
     * @throws CriteriaException
     */
    public static function createByIds(Collection $ids): static
    {
        return self::createEmpty()->filterByIds($ids);
    }
}
