<?php

declare(strict_types=1);

namespace App\V2\Domain\Course\Creator;

use App\V2\Domain\Shared\Exception\InfrastructureException;

interface CourseCreatorRepository
{
    /**
     * @throws InfrastructureException
     */
    public function insert(CourseCreator $courseCreator): void;

    /**
     * @throws CourseCreatorNotFoundException
     * @throws InfrastructureException
     */
    public function findOneBy(CourseCreatorCriteria $criteria): CourseCreator;

    /**
     * @throws InfrastructureException
     */
    public function findBy(CourseCreatorCriteria $criteria): CourseCreatorCollection;

    /**
     * @throws InfrastructureException
     */
    public function delete(CourseCreator $courseCreator): void;
}
