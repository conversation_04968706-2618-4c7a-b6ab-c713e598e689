<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase\Exception;

use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Exception\LoggableException;

class PaymentManagerException extends InfrastructureException implements LoggableException
{
    public static function invalidPayload(): self
    {
        return new self('Invalid payment webhook payload');
    }

    public static function invalidSignature(): self
    {
        return new self('Invalid payment webhook signature');
    }

    public static function fromPrevious(\Throwable $previous): self
    {
        return new self($previous->getMessage(), $previous->getCode(), $previous);
    }
}
