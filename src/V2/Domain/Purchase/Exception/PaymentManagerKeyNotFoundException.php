<?php

declare(strict_types=1);

namespace App\V2\Domain\Purchase\Exception;

use App\V2\Domain\Shared\Exception\InfrastructureException;

class PaymentManagerKeyNotFoundException extends InfrastructureException
{
    public static function secretKeyNotFound(): self
    {
        return new self('Payment secret key not found');
    }

    public static function publicKeyNotFound(): self
    {
        return new self('Payment public key not found');
    }

    public static function webhookSecretNotFound(): self
    {
        return new self('Payment webhook secret not found');
    }
}
