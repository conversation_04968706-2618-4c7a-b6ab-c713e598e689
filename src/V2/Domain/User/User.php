<?php

declare(strict_types=1);

namespace App\V2\Domain\User;

use App\V2\Domain\Shared\Email\Email;
use App\V2\Domain\Shared\Entity\WritableEntityWithId;
use App\V2\Domain\Shared\Id\Id;

/**
 * @extends WritableEntityWithId<Id>
 */
class User extends WritableEntityWithId implements IUser
{
    /**
     * @param string[] $roles
     * @param string[] $remoteRoles
     */
    public function __construct(
        Id $id,
        private readonly Email $email,
        private readonly string $password,
        private readonly array $roles,
        private readonly array $remoteRoles,
        private readonly ?string $code,
        private readonly string $firstName,
        private readonly string $lastName,
        private readonly bool $isActive,
        private readonly bool $validated,
        private readonly bool $open,
        private readonly string $locale,
        private readonly string $localeCampus,
        private readonly string $timezone,
        private readonly Id $createdById,
        private readonly \DateTimeImmutable $createdAt,
        private readonly bool $starteam = false,
        private readonly ?Id $updatedById = null,
        private readonly ?\DateTimeImmutable $updatedAt = null,
        private readonly ?Id $deletedById = null,
        private readonly ?\DateTimeImmutable $deletedAt = null,
    ) {
        parent::__construct($id);
    }

    public function getEmail(): Email
    {
        return $this->email;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    /**
     * @return string[]
     */
    public function getRoles(): array
    {
        return $this->roles;
    }

    /**
     * @return string[]
     */
    public function getRemoteRoles(): array
    {
        return $this->remoteRoles;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function isValidated(): bool
    {
        return $this->validated;
    }

    public function isOpen(): bool
    {
        return $this->open;
    }

    public function getLocale(): string
    {
        return $this->locale;
    }

    public function getLocaleCampus(): string
    {
        return $this->localeCampus;
    }

    public function getCreatedById(): Id
    {
        return $this->createdById;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function isStarteam(): bool
    {
        return $this->starteam;
    }

    public function getTimezone(): string
    {
        return $this->timezone;
    }

    public function getUpdatedById(): ?Id
    {
        return $this->updatedById;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function getDeletedById(): ?Id
    {
        return $this->deletedById;
    }

    public function getDeletedAt(): ?\DateTimeImmutable
    {
        return $this->deletedAt;
    }
}
