<?php

declare(strict_types=1);

namespace App\V2\Domain\User;

use App\V2\Domain\Shared\Collection\PaginatedCollection;

class PaginatedUserCollection extends PaginatedCollection
{
    public function __construct(
        private readonly LegacyUserCollection $userCollection,
        int $totalItems
    ) {
        parent::__construct($totalItems);
    }

    public function getCollection(): LegacyUserCollection
    {
        return $this->userCollection;
    }
}
