<?php

namespace App\Behavior;

use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\HasLifecycleCallbacks()
 */
trait Timezone
{
    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $timezone;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $country;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $timezoneCreatedAt;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $timezoneUpdatedAt;

    // Getter and setter for $timezone
    public function getTimezone(): ?string
    {
        return $this->timezone;
    }

    public function setTimezone(?string $timezone): self
    {
        $this->timezone = $timezone;

        return $this;
    }

    // Getter and setter for $country
    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;

        return $this;
    }

    // Getter and setter for $timezoneCreatedAt
    public function getTimezoneCreatedAt(): ?\DateTimeInterface
    {
        return $this->timezoneCreatedAt;
    }

    /**
     * Set the creation timestamp.
     *
     * @param \DateTimeInterface $timezoneCreatedAt
     * @return $this
     */
    public function setTimezoneCreatedAt(\DateTimeInterface $timezoneCreatedAt): self
    {
        $this->timezoneCreatedAt = $timezoneCreatedAt;

        return $this;
    }

    // Optional: You may want to include a getter for $timezoneUpdatedAt
    public function getTimezoneUpdatedAt(): ?\DateTimeInterface
    {
        return $this->timezoneUpdatedAt;
    }

    // Optional: You may want to include a setter for $timezoneUpdatedAt
    public function setTimezoneUpdatedAt(\DateTimeInterface $timezoneUpdatedAt): self
    {
        $this->timezoneUpdatedAt = $timezoneUpdatedAt;

        return $this;
    }
}
