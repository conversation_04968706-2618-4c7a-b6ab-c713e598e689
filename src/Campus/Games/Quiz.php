<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\Answer;
use App\Entity\Chapter;
use App\Entity\Question;
use App\Enum\Games as EnumGameFormula;
use App\Enum\Games as GamesEnum;

class Quiz extends Game
{
    public function getQuestions($userCourseChapter): array
    {
        $repository = $this->em->getRepository(Question::class);

        $quizQuestions = $repository->findBy([
            'chapter' => $userCourseChapter->getChapter()->getId(),
        ]);

        $maxQuestion = $userCourseChapter->getChapter()->getMaxQuestion();
        if (!empty($maxQuestion)) {
            shuffle($quizQuestions);
            $output = [];
            $i = 0;
            foreach ($quizQuestions as $item) {
                ++$i;
                array_push($output, $item);
                if ($i >= $maxQuestion) {
                    break;
                }
            }
            $quizQuestions = $output;
        }

        $questions = $this->getFormattedQuestions($quizQuestions);
        $time = $this->getTotalTime($questions);

        return [
            'questions' => $questions,
            'time' => $time,
        ];
    }

    public function check($userCourseChapter, $answers): array
    {
        $answer = isset($answers->id) ? $answers->id : null;

        if (null === $answer) {
            return [
                'correct' => false,
            ];
        }

        $repository = $this->em->getRepository(Answer::class);
        $answer = $repository->find($answers->id);
        $correct = $answer->getCorrect();

        return [
            'correct' => $correct,
        ];
    }

    private function getFormattedQuestions($data): array
    {
        $questions = [];
        foreach ($data as $question) {
            $time = $this->calculateReadingAverageTime($question->getQuestion());
            $answers = $this->getFormattedAnswers($question->getAnswers());

            $questions[] = [
                'id' => $question->getId(),
                'question' => $question->getQuestion(),
                'imageUrl' => $question->getImageUrl(),
                'random' => $question->getRandom(),
                'answers' => $answers,
                'feedback' => $question->getFeedback(),
                'time' => $question->getTime() + GamesEnum::QUIZ_AVERAGE_TIME_TO_ANSWER,
            ];
        }

        return $questions;
    }

    private function getFormattedAnswers($quizAnswers): array
    {
        $answers = [];
        foreach ($quizAnswers as $answer) {
            $answers[] = [
                'id' => $answer->getId(),
                'answer' => $answer->getAnswer(),
            ];
        }

        return $answers;
    }

    private function getTotalTime($questions)
    {
        $time = 0;
        foreach ($questions as $question) {
            $time += $question['time'];
        }

        return $time;
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (
            !isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal'])
        ) {
            return 0;
        }

        $chapterType = $args->getType();
        $answers = $data['answers'];
        $nQuestions = \count($args->getQuestions()) ?? 0;
        $nQuestions = null !== $args->getMaxQuestion()
            && $nQuestions > $args->getMaxQuestion()
            ? $args->getMaxQuestion() : $nQuestions;
        $rightAnswers = 0;
        $time = 0;
        $maxTime = $data['timeTotal'];
        $nAttempts = \array_key_exists('attempts', $data) && \is_array($data['attempts'])
            ? \count($data['attempts']) : 1;

        if ($nQuestions > 0) {
            foreach ($answers as $answer) {
                if (isset($answer['correct']) && $answer['correct']) {
                    ++$rightAnswers;
                }
                $time += $answer['time'];
            }
        }

        $completionPercentage = $nQuestions > 0 ? ($rightAnswers / $nQuestions) : 0;
        if ($completionPercentage < $chapterType->getPercentageCompleted()) {
            return 0;
        }
        $remainingTime = ($maxTime - $time) > 0 ? ($maxTime - $time) : 1;
        $basePercentage = EnumGameFormula::BASE_HALF
            + (EnumGameFormula::BASE_HALF * (($rightAnswers / $nQuestions) * ($remainingTime / ($maxTime * $nQuestions))));
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

        return $attemptsCorrectionPercentage;
    }
}
