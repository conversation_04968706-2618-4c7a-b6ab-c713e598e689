<?php

namespace App\Campus\Service\User;

use App\Campus\Service\Base\BaseService;
use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\TypeCourse;
use App\Entity\UserCourse;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Geolocation\GeolocationService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Security;

class CertificateService extends BaseService
{
	private GeolocationService $geolocationService;
	private AnnouncementConfigurationsService $announcementConfigurationsService;
	private TokenExtractorService $tokenExtractorService;

	public function __construct(
		EntityManagerInterface $em,
		SettingsService $settings,
		Security $security,
		GeolocationService $geolocationService,
		AnnouncementConfigurationsService $announcementConfigurationsService,
		TokenExtractorService $tokenExtractorService
	) {
		$this->geolocationService = $geolocationService;
		$this->announcementConfigurationsService = $announcementConfigurationsService;
		$this->tokenExtractorService = $tokenExtractorService;
		parent::__construct($em, $settings, $security);
	}


	public function obtainCertificatesUser(): array
	{
		$payloadToken = $this->tokenExtractorService->getPayloadToken();

		if (!$payloadToken) {
			return $this->obtainAllCertificatesUser();
		}

		return $this->obtainCertificateAnnouncementByPayloadToken($payloadToken);
	}

	/**
	 * Obtain all certificates of user
	 *
	 * @return array
	 */
	private function obtainAllCertificatesUser(): array
	{
		$certificates = $this->obtainCertificatesOfCoursesByAnnouncement();
		$certificates = array_merge($certificates, $this->obtainCertificatesOfCourseOnline());

		return $certificates;
	}

	private function obtainCertificateAnnouncementByPayloadToken($payloadToken): ?array
	{
		if (empty($payloadToken['announcement'])) {
			return $this->obtainAllCertificatesUser();
		}

		$announcement = $this->em->getRepository(Announcement::class)->find($payloadToken['announcement']);
		if (!$announcement) {
			return null;
		}

		$announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
			'announcement' => $announcement,
			'user' => $this->getUser()
		]);

		if (!$announcementUser) {
			return null;
		}

		return $this->processAnnouncement($announcementUser);
	}

	/**
	 * Obtain certificates than user completed by announcement.
	 * Generated through a call for proposals
	 *
	 * @return array|null 
	 * @throws \Exception 
	 */
	public function obtainCertificatesOfCoursesByAnnouncement(): ?array
	{
		try {

			$typesCourse = [
				TypeCourse::TYPE_TELEFORMACION,
				TypeCourse::TYPE_PRESENCIAL,
				TypeCourse::TYPE_MIXTO,
				TypeCourse::TYPE_AULA_VIRTUAL
			];

			return $this->fetchAnnouncementByTypeCourse($typesCourse);
		} catch (\Exception $e) {
			throw new \Exception($e->getMessage());
		}
	}

	private function fetchAnnouncementByTypeCourse(array $typesCourse): ?array
	{
		$announcements = $this->em->getRepository(Announcement::class)
			->findAnnouncementsByTypeCourse(
				$this->getUser(),
				$typesCourse
			);

		if (empty($announcements)) {
			return [];
		}

		$data = [];
		foreach ($announcements as $announcementUser) {
			$result = $this->processAnnouncement($announcementUser);
			if ($result) $data[] = $result;
		}

		return $data;
	}

	private function processAnnouncement(AnnouncementUser $announcementUser): ?array
	{
		if (!$this->shouldIncludeAnnouncement($announcementUser)) {
			return null;
		}

		$timeZoneAnnouncement = $this->settings->get('app.default_timezone');

		/* $timeZoneAnnouncement = $announcementUser->getAnnouncement()->getTimezone()
			?? $this->settings->get('app.default_timezone'); */

		$timeZoneActual = $this->geolocationService->getTimeZoneConnection();

		return [
			'id' => $announcementUser->getId(),
			'notified' => $announcementUser->getNotified(),
			'announcement' => $announcementUser->getAnnouncement(),
			'user' => $announcementUser->getUser(),
			'course' => $announcementUser->getAnnouncement()->getCourse(),
			'timeZoneActual' => $timeZoneActual,

			 'startedAt' => $this->geolocationService->convertTimeZone(
				$announcementUser->getAnnouncement()->getStartAt(),
				$timeZoneAnnouncement,
				$timeZoneActual
			),

			'finishedAt' => $this->geolocationService->convertTimeZone(
				$announcementUser->getAnnouncement()->getFinishAt(),
				$timeZoneAnnouncement,
				$timeZoneActual
			), 
			'startedAtNotFormatted' => $announcementUser->getAnnouncement()->getStartAt()->format('c'),
			'finishedAtNotFormatted' => $announcementUser->getAnnouncement()->getFinishAt()->format('c')
		];
	}

	private function shouldIncludeAnnouncement(AnnouncementUser $announcementUser): bool
	{
		$hasDiploma = $this->announcementConfigurationsService->hasDiploma($announcementUser->getAnnouncement());
		$hasSurvey = $this->announcementConfigurationsService->hasSurvey($announcementUser->getAnnouncement());

		if (!$hasDiploma) {
			return false;
		}

		if ($hasSurvey && !$announcementUser->getValuedCourseAt()) {
			return false;
		}

		return true;
	}

	/**
	 * Obtain certificates for online courses.
	 * Generated through the user's individual progress
	 *
	 * @return array 
	 */
	public function obtainCertificatesOfCourseOnline(): array
	{
		$userCourses = $this->getFinishedUserCourses();

		if (empty($userCourses)) {
			return [];
		}

		return array_map(function ($userCourse) {
			return $this->processCertificateUserCourse($userCourse);
		}, $userCourses);
	}

	private function processCertificateUserCourse(UserCourse $userCourse): array
	{
		if (empty($userCourse)) {
			return [];
		}

		if (!$this->shouldGenerateDiploma($userCourse)) {
			return [];
		}

		$timeZoneActual = $this->geolocationService->getTimeZoneConnection();
		$timezoneServer = $this->settings->get('app.default_timezone');

		return [
			'user' => $userCourse->getUser(),
			'course' => $userCourse->getCourse(),
			'startedAt' => $this->geolocationService->convertTimeZone($userCourse->getStartedAt(), $timezoneServer, $timeZoneActual),
			'finishedAt' => $this->geolocationService->convertTimeZone($userCourse->getFinishedAt(), $timezoneServer, $timeZoneActual),
			'startedAtNotFormatted' => $userCourse->getStartedAt()->format('c'),
			'finishedAtNotFormatted' => $userCourse->getFinishedAt()->format('c')
		];
	}

	private function getFinishedUserCourses(): array
	{
		$userCourseRepository = $this->em->getRepository(UserCourse::class);
		$isOpinionsPlatform = $this->settings->get('app.opinions.platform');
		return $userCourseRepository->findFinishedUserCourses($this->getUser(), $isOpinionsPlatform);
	}

	private function shouldGenerateDiploma(UserCourse $userCourse): bool
	{
		if (!$this->hasAnnouncement($userCourse)) {
			return true;
		}

		return $this->isApprovedAnnouncementUser($userCourse);
	}

	private function isAnnouncementBeforeFundae($idAnnouncement): bool
	{
		$date = $this->settings->get('app.date.before.fundae');
		$announcement = $this->em->getRepository(Announcement::class)->isAnnouncementBeforeFundae($idAnnouncement, $date);
		return $announcement  ? true : false;
	}

	private function hasAnnouncement(UserCourse $userCourse): bool
	{
		return $userCourse->getAnnouncement() !== null;
	}

	private function isApprovedAnnouncementUser(UserCourse $userCourse): bool
	{
		$annoucementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
			'announcement' => $userCourse->getAnnouncement(),
			'user' => $this->getUser()
		]);

		if (!$annoucementUser) return false;

		if ($this->isAnnouncementBeforeFundae($userCourse->getAnnouncement()->getId())) return true;

		$hasDiploma = $this->announcementConfigurationsService->hasDiploma($userCourse->getAnnouncement());
		$hasSurvey = $this->announcementConfigurationsService->hasSurvey($userCourse->getAnnouncement());

		if ($hasSurvey) {
			return $hasDiploma  && $annoucementUser->isAproved() && $annoucementUser->getValuedCourseAt();
		}

		return $hasDiploma && $annoucementUser->isAproved();
	}
}
