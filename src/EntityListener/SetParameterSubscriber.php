<?php
namespace App\EntityListener;

use Doctrine\Common\EventSubscriber;
use Doctrine\Common\Util\ClassUtils;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class SetParameterSubscriber implements EventSubscriber
{
    protected $params;


    public function __construct(ParameterBagInterface $params)
    {
        $this->params = $params;
    }


    public function getSubscribedEvents (): array
    {
        return [
            'postLoad',
        ];
    }


    public function postLoad (LifecycleEventArgs $args)
    {
        $entity = $args->getObject();

        $entityClass = ClassUtils::getClass($entity);
        $shortName = (new \ReflectionClass($entityClass))->getShortName();

        if ($this->doesClassUseTrait($entityClass, \App\Behavior\Uplodeable::class)
            or $this->doesClassUseTrait($entityClass, \App\Entity\Uplodeable::class)
        )
        {
            $key = 'app.' . strtolower($shortName) .'_uploads_path';

            if(!$this->params->has($key))
            {
                $key = strtolower($shortName) .'_uploads_path';
            }

            if(!$this->params->has($key))
            {
                $key = 'app.game' . $shortName .'_uploads_path';
            }

            if($this->params->has($key) and $this->params->get($key))
               {
                   $folder = $this->params->get('kernel.project_dir')
                       . DIRECTORY_SEPARATOR
                       . 'public'
                       . DIRECTORY_SEPARATOR
                       . $this->params->get($key);

                   $entity->setUploadsFolder($folder);
               }
        }
    }


    private function doesClassUseTrait($class, $traitName): bool
    {
        $reflection = new \ReflectionClass($class);
        $traits = $reflection->getTraits();

        foreach ($traits as $trait) {
            if ($trait->name === $traitName) {
                return true;
            }

            $traitNames = $this->getTraitHierarchy($trait);
            if (in_array($traitName, $traitNames)) {
                return true;
            }
        }

        return false;
    }

    private function getTraitHierarchy($trait): array
    {
        $traitNames = [];
        $traitNames[] = $trait->name;

        foreach ($trait->getTraits() as $nestedTrait) {
            $nestedTraitNames = $this->getTraitHierarchy($nestedTrait);
            $traitNames = array_merge($traitNames, $nestedTraitNames);
        }

        return $traitNames;
    }
}
