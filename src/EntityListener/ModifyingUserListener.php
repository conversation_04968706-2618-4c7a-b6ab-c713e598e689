<?php

namespace App\EntityListener;

use Doctrine\ORM\Event\LifecycleEventArgs;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;


class ModifyingUserListener
{
    private $storage;
    private $logger;

    /**
     * CourseModifyingUserListener constructor.
     * @param $storage
     */
    public function __construct(TokenStorageInterface $storage, LoggerInterface $logger)
    {
        $this->storage = $storage;
        $this->logger = $logger;
    }

    public function prePersist(LifecycleEventArgs $event)
    {
        $this->logger->error("Pues si que entramos");

        $object = $event->getObject();

        if($this->storage->getToken()){
            if(method_exists($object, 'setModifyingUserByAction')){
                $object->setModifyingUserByAction($this->storage, 'create');
                $object->setModifyingUserByAction($this->storage, 'update');
            }
        }
    }

    public function preUpdate(LifecycleEventArgs $event)
    {
        $object = $event->getObject();

        if(method_exists($object, 'setModifyingUserByAction')){
            if(!$object->getDeletedBy()) $object->setModifyingUserByAction($this->storage, 'update');
        }
    }

    public function preRemove(LifecycleEventArgs $event)
    {
        $object = $event->getObject();

        if(method_exists($object, 'setModifyingUserByAction')){
            $object->setModifyingUserByAction($this->storage, 'delete');
            $object->getEntityManager()->flush();
        }
    }
}
