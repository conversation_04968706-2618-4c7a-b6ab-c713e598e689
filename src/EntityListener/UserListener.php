<?php

namespace App\EntityListener;


use App\Entity\User;
use App\Entity\UserManage;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Doctrine\ORM\Event\PreFlushEventArgs;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class UserListener
{
    protected ParameterBagInterface$params;
    protected EntityManagerInterface $em;


    public function __construct (ParameterBagInterface $params, EntityManagerInterface $entityManager)
    {
        $this->params = $params;
        $this->em     = $entityManager;
    }


    public function preFlush (User $user,  PreFlushEventArgs $event): void
    {
        $this->createUserManage($user);
    }


    public function createUserManage (User $user)
    {
        if ($user->isManager())
        {
            if (!$user->getManage())
            {
                $userManage = new UserManage();
                $user->setManage($userManage);
            }

        }
    }
}