<?php

namespace App\EntityListener;

use App\Entity\Chapter;
use App\Entity\Imageable;
use App\Entity\Uplodeable;
use Doctrine\Common\EventSubscriber;
use Doctrine\Common\Util\ClassUtils;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class NotificationSubscriber implements EventSubscriber
{

    private TranslatorInterface $translator;


    public function __construct (TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }


    public function getSubscribedEvents (): array
    {
        return [
            'postLoad',
        ];
    }


    public function postLoad (LifecycleEventArgs $args)
    {
        $entity = $args->getEntity();

        if (is_a($entity, 'App\Entity\Notification'))
        {
            $data = $entity->getData() ?? [];
            $translatedMessage = $this->translator->trans($entity->getMessage(), $data);
            $entity->setTranslatedMessage($translatedMessage);
        }
    }
}
