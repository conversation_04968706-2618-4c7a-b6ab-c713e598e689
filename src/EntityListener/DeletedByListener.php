<?php

namespace App\EntityListener;

use Doctrine\ORM\Event\LifecycleEventArgs;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;


class DeletedByListener
{
    private $storage;
    private $logger;


    /**
     * CourseModifyingUserListener constructor.
     * @param TokenStorageInterface $storage
     * @param LoggerInterface $logger
     */
    public function __construct (TokenStorageInterface $storage, LoggerInterface $logger)
    {
        $this->storage = $storage;
        $this->logger  = $logger;
    }


    public function preSoftDelete (LifecycleEventArgs $event)
    {
        // Get needed objects
        $object = $event->getObject();
        $em     = $event->getEntityManager();
        $uow    = $event->getEntityManager()->getUnitOfWork();

        // Get old field value
        $meta     = $em->getClassMetadata(get_class($object));
        $reflProp = $meta->getReflectionProperty('deletedBy');
        $oldValue = $reflProp->getValue($object);
    
        // Update the value
        $token = $this->storage->getToken();
        $user = $token ? $token->getUser() : null;
    
        if (!$user) {
            $this->logger->error('No user found in DeletedByListener, setting deletedBy to null.');
            return;
        }
    
        $reflProp->setValue($object, $user);
        $this->logger->error('Objeto a borrar: ' . get_class($object) . ' (ID: ' . (method_exists($object, 'getId') ? $object->getId() : 'N/A') . ') por ' . $user->getId());
    
        // Make sure the unit of works knows about this
        $uow->propertyChanged($object, 'deletedBy', $oldValue, $user);
        $uow->scheduleExtraUpdate($object, ['deletedBy' => [$oldValue, $user]]);
    }

}