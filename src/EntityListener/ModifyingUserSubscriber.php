<?php

namespace App\EntityListener;

use App\Entity\User;
use Doctrine\Common\EventSubscriber;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Doctrine\ORM\Events;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class
ModifyingUserSubscriber implements EventSubscriber
{
    private $storage;
    private $logger;

    /**
     * CourseModifyingUserListener constructor.
     * @param $storage
     */
    public function __construct(TokenStorageInterface $storage, LoggerInterface $logger)
    {
        $this->storage = $storage;
        $this->logger = $logger;
    }

    public function getSubscribedEvents(): array
    {
        return [
            Events::prePersist,
            Events::preUpdate,
        ];
    }

    public function prePersist(LifecycleEventArgs $event)
    {
        $object = $event->getObject();

        if(!is_null($this->storage->getToken()) && $this->storage->getToken()->getUser() instanceof User) {
            if (method_exists($object, 'setModifyingUserByAction')) {
                $object->setModifyingUserByAction($this->storage, 'create');
                $object->setModifyingUserByAction($this->storage, 'update');
            }
        }
    }

    public function preUpdate(LifecycleEventArgs $event)
    {
        $object = $event->getObject();

        if(method_exists($object, 'setModifyingUserByAction')){
            if(!is_null($this->storage->getToken()) && $this->storage->getToken()->getUser() instanceof User)
            {
                $user = $this->storage->getToken()->getUser()->getUsername();
                $this->logger->debug("Objeto a updatear: " . get_class($object) . " (ID: " . (method_exists($object, 'getId') ? $object->getId() : 'N/A') . ") por " . $user);
                if(!$object->getDeletedBy()) $object->setModifyingUserByAction($this->storage, 'update');
            }
        }
    }
}
