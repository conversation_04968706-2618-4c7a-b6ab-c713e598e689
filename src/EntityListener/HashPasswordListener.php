<?php

namespace App\EntityListener;

use App\Entity\User;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Psr\Log\LoggerInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class HashPasswordListener
{
    private UserPasswordHasherInterface $encoder;
    private $logger;

    /**
     * UserPasswordHasherInterface constructor.
     * @param UserPasswordHasherInterface $encoder
     * @param LoggerInterface $logger
     */
    public function __construct(UserPasswordHasherInterface $encoder, LoggerInterface $logger)
    {
        $this->encoder = $encoder;
        $this->logger = $logger;
    }

    public function prePersist(User $user, LifecycleEventArgs $event)
    {
        $this->logger->debug("Creando el usuario: " . $user->getEmail());
        $user->hashPassword($this->encoder);
    }


    public function preUpdate(User $user, LifecycleEventArgs $event)
    {
        $this->logger->debug("Actualizando el usuario: " . $user->getEmail());
    }
}
