<?php

declare(strict_types=1);

namespace App\EntityListener;

use App\Entity\Chapter;
use App\Entity\RoleplayProject;
use App\Entity\VcmsProject;
use App\Enum\ChapterContent as EnumChapterType;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\LifecycleEventArgs;

class ChapterListener
{
    private EntityManagerInterface $em;

    /**
     * CourseModifyingUserListener constructor.
     */
    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }

    public function prePersist(Chapter $chapter, LifecycleEventArgs $event)
    {
        if (EnumChapterType::VCMS_CODE == $chapter->getType()->getCode() && null === $chapter->getVcmsProject()) {
            $vcmsProject = (new VcmsProject())
                ->setTitle("{$chapter->getTitle()}")
                ->setView('vertical');

            $this->em->persist($vcmsProject);
            $chapter->setVcmsProject($vcmsProject);
        }

        if (EnumChapterType::ROLE_PLAY_CODE == $chapter->getType()->getCode() && null === $chapter->getRoleplayProject()) {
            $vcmsProject = (new RoleplayProject())
                ->setTitle($chapter->getTitle());

            $this->em->persist($vcmsProject);
            $chapter->setRoleplayProject($vcmsProject);
        }
    }
}
