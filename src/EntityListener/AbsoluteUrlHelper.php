<?php

namespace App\EntityListener;


use App\Entity\Question;
use Doctrine\Common\EventSubscriber;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Doctrine\ORM\Events;

class AbsoluteUrlHelper implements EventSubscriber
{
    private $questionUploadsPath;

    public function __construct($questionUploadsPath)
    {
        $this->questionUploadsPath = $questionUploadsPath;
    }

    public function getSubscribedEvents(): array
    {
        return [
            Events::postLoad,
        ];
    }

    public function postLoad(LifecycleEventArgs $args)
    {
        $entity = $args->getEntity();

        if($entity instanceof Question) {
            if(!is_null($entity->getImage())) $entity->setImageUrl('/' . $this->questionUploadsPath . '/' . $entity->getImage());
        }
    }
}
