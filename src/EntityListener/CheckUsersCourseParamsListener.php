<?php

namespace App\EntityListener;

use App\Entity\UserCourse;
use Doctrine\Common\EventSubscriber;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class CheckUsersCourseParamsListener implements EventSubscriber
{
    protected $params;

    public function __construct(ParameterBagInterface $params)
    {
        $this->params = $params;
    }

    public function getSubscribedEvents(): array
    {
        return [
            'postLoad',
        ];
    }

    public function postLoad (LifecycleEventArgs $args)
    {
        $entity = $args->getEntity();

        if ($entity instanceof UserCourse)
        {
            $entity->setUserCourseIfIsCurrentlyViewed($this->params->get('app.opinions.platform'));
            $entity->setUserCourseIfIsFinished($this->params->get('app.opinions.platform'));
        }

    }
}
