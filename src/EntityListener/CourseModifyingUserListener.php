<?php


namespace App\EntityListener;

use App\Entity\Course;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;


class CourseModifyingUserListener
{
    private $storage;
    private $logger;

    /**
     * CourseModifyingUserListener constructor.
     * @param $storage
     */
    public function __construct(TokenStorageInterface $storage, LoggerInterface $logger)
    {
        $this->storage = $storage;
        $this->logger = $logger;
    }

    public function prePersist(Course $course, LifecycleEventArgs $event)
    {
        $course->setModifyingUserByAction($this->storage, 'create');
        $course->setModifyingUserByAction($this->storage, 'update');
    }

    public function preUpdate(Course $course, LifecycleEventArgs $event)
    {
        $this->logger->debug("Modifico " . $course->getDeletedBy());
        if(!$course->getDeletedBy()) $course->setModifyingUserByAction($this->storage, 'update');
    }

    public function preRemove(Course $course, LifecycleEventArgs $event)
    {
        $this->logger->debug("Pre remove " . $event->getObject());

        $course->setModifyingUserByAction($this->storage, 'delete');

        $event->getEntityManager()->flush();
    }

    public function postSoftDelete(Course $course, LifecycleEventArgs $event)
    {
        $this->logger->error("Post remove " . $event->getObject());

        $course->setModifyingUserByAction($this->storage, 'delete');

        $event->getEntityManager()->flush();
    }
}