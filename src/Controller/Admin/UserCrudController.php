<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Admin\Field\UserFieldsFundaeField;
use App\Admin\Filter\FilterFilter;
use App\Admin\Filter\UserExtraGenderFilter;
use App\Admin\Filter\UserRoleFilter;
use App\Admin\Traits\FilterCategoriesTrait;
use App\Admin\Traits\HelpEntityTrait;
use App\Admin\Traits\ImpersonateUser;
use App\Admin\Traits\LanguagesTrait;
use App\Admin\Traits\SerializerTrait;
use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\FilterCategoryTranslation;
use App\Entity\FilterTranslation;
use App\Entity\Itinerary;
use App\Entity\Message;
use App\Entity\ProfessionalCategory;
use App\Entity\RecoveryCode;
use App\Entity\TranslationsAdminTranslation;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\UserCompany;
use App\Entity\UserCourse;
use App\Entity\UserExtra;
use App\Entity\UserFieldsFundae;
use App\Entity\UserProfessionalCategory;
use App\Entity\UserStudyLevel;
use App\Entity\UserWorkCenter;
use App\Entity\UserWorkDepartment;
use App\Enum\AnnouncementState;
use App\Enum\Games;
use App\Repository\CourseRepository;
use App\Repository\ExportRepository;
use App\Repository\MessageRepository;
use App\Repository\UserCourseChapterRepository;
use App\Repository\UserCourseRepository;
use App\Repository\UserLoginRepository;
use App\Service\Annoucement\Email\AnnouncementUserEmailService;
use App\Service\SettingsService;
use App\Service\StatsUser\StatsUserService;
use App\Service\Task\TaskService;
use Doctrine\Common\Annotations\AnnotationReader;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityNotFoundException;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FieldCollection;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FilterCollection;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\SearchDto;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\CollectionField;
use EasyCorp\Bundle\EasyAdminBundle\Field\EmailField;
use EasyCorp\Bundle\EasyAdminBundle\Field\Field;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\HiddenField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ImageField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\LocaleField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Orm\EntityRepository;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Log\LoggerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Intl\Locales;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory;
use Symfony\Component\Serializer\Mapping\Loader\AnnotationLoader;
use Symfony\Component\Serializer\NameConverter\MetadataAwareNameConverter;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use Symfony\Contracts\Translation\TranslatorInterface;

class UserCrudController extends AbstractCrudController
{
    use SerializerTrait;
    use HelpEntityTrait;
    use FilterCategoriesTrait;
    use LanguagesTrait;
    use ImpersonateUser;

    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private RequestStack $requestStack;
    private JWTManager $jwt;
    protected TranslatorInterface $translator;
    protected MailerInterface $mailer;
    private AdminContextProvider $adminContextProvider;
    private AdminUrlGenerator $adminUrlGenerator;
    private SettingsService $settings;
    private AnnouncementUserEmailService $announcementUserEmailService;
    private StatsUserService $statsUserService;
    private TaskService $taskService;

    public function __construct(
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        JWTManager $jwt,
        TranslatorInterface $translator,
        MailerInterface $mailer,
        AdminContextProvider $adminContextProvider,
        AdminUrlGenerator $adminUrlGenerator,
        SettingsService $settingsService,
        AnnouncementUserEmailService $announcementUserEmailService,
        StatsUserService $statsUserService,
        TaskService $taskService
    ) {
        $this->em = $em;
        $this->logger = $logger;
        $this->requestStack = $requestStack;
        $this->jwt = $jwt;
        $this->translator = $translator;
        $this->mailer = $mailer;
        $this->adminContextProvider = $adminContextProvider;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->settings = $settingsService;
        $this->announcementUserEmailService = $announcementUserEmailService;
        $this->statsUserService = $statsUserService;
        $this->taskService = $taskService;
    }

    public static function getEntityFqcn(): string
    {
        return User::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular($this->translator->trans('user.label_in_singular', [], 'messages', $this->getUser()->getLocale()))
            ->setEntityLabelInPlural($this->translator->trans('user.label_in_plural', [], 'messages', $this->getUser()->getLocale()))
            ->setPageTitle(Crud::PAGE_EDIT, $this->translator->trans('user.configureFields.edit_user', [], 'messages', $this->getUser()->getLocale()))
            ->setSearchFields(['id', 'email', 'roles', 'firstName', 'lastName'])
            ->overrideTemplate('crud/detail', 'admin/user/detail.html.twig')
            ->overrideTemplate('crud/index', 'admin/user/index.html.twig')
            ->overrideTemplate('crud/new', 'admin/user/new.html.twig')
            ->overrideTemplate('crud/edit', 'admin/user/edit.html.twig');
    }

    public function configureActions(Actions $actions): Actions
    {
        $impersonate = Action::new(
            'impersonate',
            $this->translator->trans(
                'user.actions.impersonate',
                [],
                'messages',
                $this->getUser()->getLocale()
            )
        )
            ->linkToUrl(function (User $user) {
                if ($user->justUserRole()) {
                    return '#' . $user->getId();
                }

                return
                    $this->adminUrlGenerator
                        ->unsetAll()
                        ->set('_switch_user', $user->getEmail())
                        ->setController(DashboardController::class)
                        ->generateUrl();
            })
            ->addCssClass('btn btn-primary action-impersonate')
            ->setIcon('fa fa-user');

        $actions
            ->add(Crud::PAGE_INDEX, $impersonate)
            ->add(Crud::PAGE_DETAIL, $impersonate);

        $actions->add(Crud::PAGE_INDEX, Action::DETAIL);

        $validatedUser = Action::new(
            'validated',
            $this->translator->trans(
                'user.configureFields.button_validate',
                [],
                'messages',
                $this->getUser()->getLocale()
            )
        )
            ->linkToCrudAction('validatedAction')
            ->displayIf(static function ($entity) {
                return !$entity->getValidated();
            })
            ->addCssClass('text-warning')
            ->setIcon('fas fa-envelope');

        if ($this->settings->get('app.free.user.registration')) {
            $actions->add(Crud::PAGE_INDEX, $validatedUser);
        }

        $actions->remove(Crud::PAGE_INDEX, Action::NEW);

        $actions->update(Crud::PAGE_INDEX, Action::BATCH_DELETE, function (Action $action) {
            return $action->setIcon('fa fa-trash');
        });

        // Define the permissions needed to execute these actions listed in the voter
        $actions
            ->setPermission(Action::EDIT, 'EDIT_USER')
            ->setPermission(Action::DELETE, 'DELETE_USER')
            ->setPermission('impersonate', 'IMPERSONATE_USER');

        return $actions;
    }

    public function configureFilters(Filters $filters): Filters
    {
        $filters->add('isActive')
            ->add(UserExtraGenderFilter::new('userExtraGender', $this->translator->trans('user.configureFields.gender', [], 'messages', $this->getUser()->getLocale())));

        if ($this->settings->get('app.user.useFilters')) {
            $filters
                ->add(FilterFilter::new('filter', 'Filtros'));
        }

        if ($this->getUser()->isAdmin()) {
            $filters
                ->add(
                    UserRoleFilter::new('roles')
                        ->setChoices(array_flip(User::ROLES))
                        ->setLabel('Roles')
                );
        }

        return $filters;
    }

    public function configureFields(string $pageName): iterable
    {
        $companies = $this->em->getRepository(UserCompany::class)->findBy(['state' => true]);
        $panelBasicInfo = FormField::addPanel($this->translator->trans('common_areas.basic_information', [], 'messages', $this->getUser()->getLocale()));
        $email = EmailField::new('email', $this->translator->trans('user.email', [], 'messages', $this->getUser()->getLocale()))
            ->setTemplatePath('bundles/EasyAdminBundle/field-linked.html.twig');

        $active = Field::new('isActive');
        $panelPermissions = FormField::addPanel($this->translator->trans('user.configureFields.permissions', [], 'messages', $this->getUser()->getLocale()));

        $rolesChoices = User::ROLES;
        unset($rolesChoices[User::ROLE_SUBSIDIZER]);
        unset($rolesChoices[User::ROLE_INSPECTOR]);
        unset($rolesChoices[User::ROLE_TEAM_MANAGER]);

        if (!\in_array('ROLE_SUPER_ADMIN', $this->getUser()->getRoles())) {
            unset($rolesChoices[User::ROLE_SUPER_ADMIN]);
            unset($rolesChoices[User::ROLE_DEVELOPER]);
            unset($rolesChoices[User::ROLE_SUPPORT]);
        }

        if (!\in_array('ROLE_ADMIN', $this->getUser()->getRoles())) {
            unset($rolesChoices[User::ROLE_ADMIN]);
            unset($rolesChoices[User::ROLE_MANAGER]);
            unset($rolesChoices[User::ROLE_MANAGER_EDITOR]);
            unset($rolesChoices[User::ROLE_TEAM_MANAGER]);
        }

        if ($this->settings->get('app.permissions.manager.canPublish')) {
            unset($rolesChoices[User::ROLE_MANAGER_EDITOR]);
        }

        $adminDefaultLanguage = $this->settings->get('app.adminDefaultLanguage');
        $defaultLanguage = $this->settings->get('app.defaultLanguage');

        $rolesChoices = $this->evalRols($rolesChoices, $pageName);

        $roles = ChoiceField::new('roles')
            ->setChoices(array_flip($rolesChoices))
            ->allowMultipleChoices(true);

        $locale = LocaleField::new('locale')
            ->setLabel($this->translator->trans('user.configureFields.locale', [], 'messages', $this->getUser()->getLocale()))
            ->setFormTypeOption('required', true)
            ->setFormTypeOption('choice_loader', null)
            ->setFormTypeOption('choices', $this->getLocalesLans($this->getLanguagesAdmin()));

        if (Crud::PAGE_DETAIL === $pageName) {
            $locale->setFormTypeOption('data', $adminDefaultLanguage);
        }

        $localeCampus = LocaleField::new('localeCampus')
            ->setLabel($this->translator->trans('user.configureFields.localeCampus', [], 'messages', $this->getUser()->getLocale()))
            ->setFormTypeOption('required', true)
            ->setFormTypeOption('choice_loader', null)
            ->setFormTypeOption('choices', $this->getLocalesLans($this->getLanguages()));
        if (Crud::PAGE_DETAIL === $pageName) {
            $localeCampus->setFormTypeOption('data', $defaultLanguage);
        }

        $panelPassword = FormField::addPanel($this->translator->trans('user.configureFields.password', [], 'messages', $this->getUser()->getLocale()));
        $password = TextField::new('password', $this->translator->trans('user.configureFields.password', [], 'messages', $this->getUser()->getLocale()));

        $code = TextField::new('code', $this->translator->trans('user.configureFields.code', [], 'messages', $this->getUser()->getLocale()));

        $registerKey = TextField::new('registerKey', $this->translator->trans('user.configureFields.dni', [], 'messages', $this->getUser()->getLocale()))
            ->setFormTypeOptions(['required' => true]);

        //        $panel4        = FormField::addPanel($this->translator->trans('common_areas.basic_information', [], 'messages', $this->getUser()->getLocale()));
        //        $panel5        = FormField::addPanel('Permisos');
        //        $panel6        = FormField::addPanel($this->translator->trans('user.configureFields.change_password', [], 'messages', $this->getUser()->getLocale()));
        $newPassword = Field::new('newPassword', $this->translator->trans('user.configureFields.new_password', [], 'messages', $this->getUser()->getLocale()));

        $id = IntegerField::new('id', 'ID');
        $firstName = TextField::new('firstName', $this->translator->trans('user.configureFields.first_name', [], 'messages', $this->getUser()->getLocale()));
        $lastName = TextField::new('lastName', $this->translator->trans('user.configureFields.last_name', [], 'messages', $this->getUser()->getLocale()));
        $extra = AssociationField::new('extra', $this->translator->trans('user.configureFields.extra', [], 'messages', $this->getUser()->getLocale()));
        $announcements = AssociationField::new('announcements', $this->translator->trans('user.configureFields.announcements', [], 'messages', $this->getUser()->getLocale()));
        $courses = AssociationField::new('courses', $this->translator->trans('user.configureFields.courses', [], 'messages', $this->getUser()->getLocale()));
        $avatar = ImageField::new('avatarImage')->setBasePath($this->settings->get('app.avatar_uploads_path'));
        $points = IntegerField::new('points');

        $userFieldsFundae = UserFieldsFundaeField::new('userFieldsFundae', $this->translator->trans('user_fields_fundae.title', []))
            ->setFormTypeOption('attr.class', 'form-userFieldsFundae');

        $userCompanyFields = CollectionField::new('userFieldsFundae')
            ->setFieldFqcn(UserFieldsFundae::class)
            ->setFormType('App\Form\UserCompanyFieldsType')
            ->setEntryIsComplex(true)
            ->showEntryLabel(false)
            ->setFormTypeOptions([
                'label' => false,
            ])
            ->addCssClass('user-extra-fields');

        $appTimezones = $this->settings->get('app.timezones');
        $TimezoneDefault = $this->settings->get('app.default_timezone');

        $timezones = \is_array($appTimezones) ? $appTimezones : json_decode($appTimezones, true) ?? [];

        $tzs = [];
        foreach ($timezones as $tz) {
            $tzs[$tz] = $tz;
        }

        if (Crud::PAGE_EDIT === $pageName) {
            $timezone = ChoiceField::new('timezone')
                ->setLabel($this->translator->trans('messages.configureFields.timezone', [], 'messages', $this->getUser()->getLocale()))
                ->setFormTypeOption('required', false)
                ->setFormTypeOption('empty_data', $TimezoneDefault)
                ->setChoices($tzs ? array_flip($tzs) : array_flip([$TimezoneDefault]));
        }

        if (Crud::PAGE_NEW === $pageName) {
            $timezone = LocaleField::new('timezone')
                ->setLabel($this->translator->trans('messages.configureFields.timezone', [], 'messages', $this->getUser()->getLocale()))
                ->setFormTypeOption('required', true)
                ->setFormTypeOption('choice_loader', null)
                ->setFormTypeOption('choices', $tzs ? array_flip($tzs) : array_flip([$TimezoneDefault]))
                ->setFormTypeOption('empty_data', $TimezoneDefault);
        }

        if ($this->settings->get('app.openCourse')) {
            $openUser = BooleanField::new('open', $this->translator->trans('user.configureFields.open', [], 'messages', $this->getUser()->getLocale()));
        }

        if (Crud::PAGE_INDEX === $pageName) {
            $fields = [$avatar, $email, $firstName, $lastName, $active];

            if (true == $this->settings->get('app.user.useExtraFields')) {
                array_splice($fields, 5, 0, [$extra]);
            }

            $fields[] = $points;
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            return [$id, $email, $roles, $password, $firstName, $lastName, $extra, $announcements, $courses];
        } elseif (\in_array($pageName, [Crud::PAGE_NEW, Crud::PAGE_EDIT])) {
            $entityId = $this->adminContextProvider->getContext()->getEntity()->getPrimaryKeyValue();
            /*              * @var $user User
             */
            /* $user = $this->em->getRepository(User::class)->findOneBy(['id' => $entityId]);
            if ($user) {
                if (in_array('ROLE_SUPER_ADMIN', $user->getRoles()) || $user->getId() == $this->getUser()->getId()) {
                    $newPassword->setDisabled(true);
                }
            }*/

            $panelBasicInfo->addCssClass('col-xs-12 col-md-12');
            $email->setColumns('col-xs-12 col-md-3');
            $firstName->setColumns('col-xs-12 col-md-3');
            $lastName->setColumns('col-xs-12 col-md-3');
            $locale->setColumns('col-xs-12 col-md-3');
            $timezone->setColumns('col-xs-12 col-md-3');
            $userCompanyFields->setColumns('col-xs-12 col-md-3');
            $localeCampus->setColumns('col-xs-12 col-md-3');
            $panelPermissions->addCssClass('col-xs-12 col-md-4');
            $roles->setColumns(12);
            $panelPassword->addCssClass('col-xs-12 col-md-4');
            $password->setColumns(12);
            $newPassword->setColumns(12);
            $fields = [
                $panelBasicInfo,
                $email,
                $firstName,
                $lastName,
                $locale,
                $localeCampus,
                $timezone,
                $panelPermissions,
                $roles,
                $panelPassword,
                Crud::PAGE_NEW == $pageName ? $password : $newPassword,
                HiddenField::new('filtersJson'),
            ];

            if ($this->settings->get('app.fundae')) {
                $fields[] = FormField::addPanel('');
                $fields[] = $userFieldsFundae;
            }

            if (isset($openUser)) {
                $openUser->setColumns(12);
                array_splice($fields, 7, 0, [$openUser]);
            }

            if (\count($companies) > 0 && !$this->settings->get('app.fundae')) {
                array_splice($fields, 6, 0, [$userCompanyFields]);
            }

            if ($this->settings->get('app.user.editCode')) {
                $code
                    ->setColumns('col-xs-12 col-md-4')
                    ->setFormTypeOptions(['required' => false]);
                array_splice($fields, 6, 0, [$code]);
                //   $fields[] = $code;
            }

            if ($this->settings->get('app.user.dni')) {
                $registerKey->setColumns('col-xs-12 col-md-6');
                $fields[] = $registerKey;
            }

            /*   if ($this->settings->get('app.user.useExtraFields')) {
                $panelExtraFields->addCssClass('panel-user-extra-fields');
                $fields = array_merge($fields, [$panelExtraFields, $extraForm]);
            } */
        }

        return $fields;
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function createIndexQueryBuilder(SearchDto $searchDto, EntityDto $entityDto, FieldCollection $fields, FilterCollection $filters): QueryBuilder
    {
        $qb = $this->container->get(EntityRepository::class)->createQueryBuilder($searchDto, $entityDto, $fields, $filters);
        if (!array_intersect([User::ROLE_ADMIN, User::ROLE_SUPER_ADMIN], $this->getUser()->getRoles())) {
            /**
             * @var $user User
             */
            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
            $filters = $user->getFilters();
            $filters_id = [];
            $subquery = $qb->getEntityManager()->createQueryBuilder();
            $subquery->select('distinct(u.id)')
                ->from('App\Entity\User', 'u')
                ->andWhere('u.createdBy = ' . $user->getId());
            foreach ($filters as $filter) {
                $filters_id[$filter->getFilterCategory()->getId()][] = $filter->getId();
            }
            if (!empty($filters_id)) {
                $thirdquery = $qb->getEntityManager()->createQueryBuilder();
                $thirdquery->select('distinct(u2.id)')
                    ->from('App\Entity\User', 'u2');

                foreach ($filters_id as $category_id => $filters) {
                    $thirdquery->innerJoin('u2.filter', 'f' . $category_id);
                    $thirdquery->andWhere($subquery->expr()->in('f' . $category_id . '.id', $filters));
                }
                $subquery->orWhere($subquery->expr()->in('u.id', $thirdquery->getDQL()));
            }
            $qb->andWhere($qb->expr()->in('entity.id', $subquery->getDQL()));
        }
        if (!\in_array(User::ROLE_SUPER_ADMIN, $this->getUser()->getRoles())) {
            $qb->andWhere('JSON_CONTAINS(entity.roles, :rol) = 0')
                ->setParameter('rol', '"' . User::ROLE_SUPER_ADMIN . '"');
        }

        $qb->orderBy('entity.firstName', 'ASC');

        return $qb;
    }

    public function createEntity(string $entityFqc)
    {
        if ($this->requestStack->getCurrentRequest()->get('User')) {
            $email = trim(strtolower($this->requestStack->getCurrentRequest()->get('User')['email']));
            $this->em->getFilters()->disable('softdeleteable');
            if ($this->em->getRepository(User::class)->findOneBy(['email' => $email])) {
                $message = $this->translator->trans('email.messages_to_user.error_register', [], 'email', $this->getUser()->getLocale()) . ' ' .
                    $this->translator->trans('email.email', [], 'email', $this->getUser()->getLocale()) . ' ' .
                    $this->translator->trans('error.register.exist', [], 'register_help', $this->getUser()->getLocale());

                return $this->addFlash('danger', $message);
            }
            $this->em->getFilters()->enable('softdeleteable');
        }
        $user = new User();
        $userFieldsFundae = new UserFieldsFundae();
        $userFieldsFundae->setUser($user);
        $user->setUserFieldsFundae($userFieldsFundae);

        $user->setValidated(true);
        $user->setOpen(false);
        $user->setIsActive(true);
        $user->setLocale($this->settings->get('app.adminDefaultLanguage'));
        $user->setLocaleCampus($this->settings->get('app.defaultLanguage'));

        return $user;
    }

    public function persistEntity(EntityManagerInterface $entityManager, $entityInstance): void
    {
        $this->checkSupportRolePermission($entityInstance);
        parent::persistEntity($entityManager, $entityInstance);
    }

    public function updateEntity(EntityManagerInterface $entityManager, $entityInstance): void
    {
        $this->checkSupportRolePermission($entityInstance);
        parent::updateEntity($entityManager, $entityInstance);
    }

    private function checkSupportRolePermission($entityInstance): void
    {
        if (!$entityInstance instanceof User) {
            throw new \LogicException('The entity instance must be an instance of User.');
        }

        $isSupportRoleAssigned = \in_array(User::ROLE_SUPPORT, $entityInstance->getRoles(), true);
        $isSuperAdmin = $this->getUser()->isSuperAdmin();

        if ($isSupportRoleAssigned && !$isSuperAdmin) {
            throw new AccessDeniedException(
                'You do not have permission to create or edit users with the role "' . User::ROLE_SUPPORT . '".'
            );
        }
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function detail(AdminContext $context)
    {
        $responseParameters = parent::detail($context);
        $typeCoursesIntern = $this->em->getRepository(TypeCourse::class)->getActiveTranslatedListAndConfigurations($this->getUser()->getLocale());

        $user = $context->getEntity()->getInstance();
        $excludedFilters = [];
        $customFilters = $user->getCustomFilters();

        if (!empty($customFilters['excluded'])) {
            foreach ($customFilters['excluded'] as $filterId) {
                $excludedFilters[] = $this->em->getRepository(Filter::class)->find($filterId);
            }
        }

        $dynamicTitles = [];
        $TranslationsAdminTranslations = $this->em->getRepository(TranslationsAdminTranslation::class)->findBy(['locale' => $this->getUser()->getLocale()]);
        if ($TranslationsAdminTranslations) {
            foreach ($TranslationsAdminTranslations as $TranslationsAdminTranslation) {
                $dynamicTitles[$TranslationsAdminTranslation->getName()] = $TranslationsAdminTranslation->getDescription();
            }
        }

        $responseParameters->set('roleNames', User::ROLES);
        $responseParameters->set('showFilters', $this->settings->get('app.user.useFilters'));
        $responseParameters->set('excludedFilters', $excludedFilters);
        $responseParameters->set('courseVoluntary', $this->getTotalCoursesVoluntary($user));
        $responseParameters->set('timeSpentFormation', $this->userTimeSpentFormation($user));
        $responseParameters->set('typeCoursesIntern', $typeCoursesIntern);
        $responseParameters->set('dynamicTitles', $dynamicTitles);

        return $responseParameters;
    }

    private function getTotalCoursesVoluntary(User $user)
    {
        $coursesVoluntary = $this->statsUserService->getVoluntaryTrainingUser($user);

        $startedCoursesVoluntary = 0;
        $finishedCoursesVoluntary = 0;
        $notStartedCoursesVoluntary = 0;

        foreach ($coursesVoluntary as $course) {
            if (AnnouncementState::STATE_FINISHED == $course['state']) {
                ++$finishedCoursesVoluntary;
            } elseif (AnnouncementState::STATE_IN_PROGRESS == $course['state']) {
                ++$startedCoursesVoluntary;
            }
        }
        $notStartedCoursesVoluntary = \count($coursesVoluntary) - $startedCoursesVoluntary - $finishedCoursesVoluntary;

        $series = [
            [
                'name' => $this->translator->trans('user.configureFields.courses_stats.finished', [], 'messages'),
                'y' => (int) $finishedCoursesVoluntary,
            ],
            [
                'name' => $this->translator->trans('user.configureFields.courses_stats.started', [], 'messages'),
                'y' => (int) $startedCoursesVoluntary,
            ],
            [
                'name' => $this->translator->trans('user.configureFields.courses_stats.notstarted', [], 'messages'),
                'y' => (int) $notStartedCoursesVoluntary,
            ],
        ];

        return [
            $series,
            [
                'name' => $this->translator->trans('user.configureFields.courses_stats.available', [], 'messages'),
                'y' => (int) \count($coursesVoluntary),
            ],
        ];
    }

    private function userTimeSpentFormation(User $user)
    {
        $niceTime = function ($seconds) {
            if (0 === $seconds) {
                return '-';
            }

            $hours = floor($seconds / 3600);
            $mins = floor(($seconds - $hours * 3600) / 60);

            $return = [];
            if ($hours > 0) {
                $return[] = $hours . ' ' . (
                    1 == $hours
                        ? $this->translator->trans('course.stats.hour', [], 'messages')
                        : $this->translator->trans('course.stats.hours', [], 'messages')
                );
            }
            if ($mins > 0) {
                $return[] = $mins . ' ' . (
                    1 == $mins
                        ? $this->translator->trans('course.stats.minute', [], 'messages')
                        : $this->translator->trans('course.stats.minutes', [], 'messages')
                );
            }

            if (empty($return)) {
                $return[] = $seconds . ' ' . (
                    1 == $seconds
                        ? $this->translator->trans('course.stats.second', [], 'messages')
                        : $this->translator->trans('course.stats.seconds', [], 'messages')
                );
            }

            return implode(' ', $return);
        };

        $timeTotal = $user->getTimeSpent();

        return $niceTime($timeTotal);
    }

    /**
     * @Route ("/admin/user/{id}/courses", name="admin_user_courses")
     *
     * @return Response
     */
    public function userCourses(User $user, CourseRepository $courseRepository, UserCourseRepository $userCourseRepository)
    {
        $itineraries = $this->statsUserService->getItinerariesUser($user);
        $announcements = $this->statsUserService->getAnnouncementUser($user);
        $filters = $this->statsUserService->getCoursesByFilter($user);

        $coursesMerge = array_merge($itineraries, $announcements, $filters);

        $notStartedCourses = 0;
        $startedCourses = 0;
        $finishedCourses = 0;
        $totalCourses = \count($coursesMerge);

        foreach ($coursesMerge as $course) {
            if (AnnouncementState::STATE_FINISHED == $course['state']) {
                ++$finishedCourses;
            } elseif (AnnouncementState::STATE_IN_PROGRESS == $course['state']) {
                ++$startedCourses;
            }
        }
        $notStartedCourses = $totalCourses - $finishedCourses - $startedCourses;

        // $total = (int)$availableCourses + (int)$startedCourses + (int)$finishedCourses;
        $series = [
            [
                'name' => $this->translator->trans('user.configureFields.courses_stats.finished', [], 'messages'),
                'y' => (int) $finishedCourses,
            ],
            [
                'name' => $this->translator->trans('user.configureFields.courses_stats.started', [], 'messages'),
                'y' => (int) $startedCourses,
            ],
            [
                'name' => $this->translator->trans('user.configureFields.courses_stats.notstarted', [], 'messages'),
                'y' => (int) $notStartedCourses,
            ],
        ];

        $courses = [
            $series,
            [
                'total' => \count($coursesMerge),
            ],
        ];

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'courses' => $courses,
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/user/{id}/chapters", name="admin_user_chapters")
     *
     * @return Response
     */
    public function userChapters(User $user, CourseRepository $courseRepository, UserCourseChapterRepository $userCourseChapterRepository)
    {
        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'chapters' => $this->getTotalCoursesVoluntary($user),
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/user/{id}/messages", name="admin_user_messages")
     *
     * @return Response
     */
    public function userMessages(User $user, MessageRepository $messageRepository)
    {
        $messages = [
            [
                'name' => $this->translator->trans('user.configureFields.courses_stats.sent_messages', [], 'messages'),
                'y' => (int) $messageRepository->countUserSent($user),
            ],
            [
                'name' => $this->translator->trans('user.configureFields.courses_stats.received_messages', [], 'messages'),
                'y' => (int) $messageRepository->countUserReceived($user),
            ],
        ];

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'messages' => $messages,
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/user/{id}/courses-list", name="admin_user_courses_list")
     *
     * @return Response
     */
    public function userCoursesList(
        User $user,
        CourseRepository $courseRepository,
        UserCourseRepository $userCourseRepository,
        AdminUrlGenerator $adminUrlGenerator
    ) {
        $courses = [];

        foreach ($user->getCourses() as $userCourse) {
            try {
                $courses[] = [
                    'id' => $userCourse->getId(),
                    'name' => $userCourse->getCourse()->getName(),
                    'started' => $userCourse->getStartedAt()->getTimestamp(),
                    'finished' => $userCourse->getFinishedAt() ? $userCourse->getFinishedAt()->getTimestamp() : '',
                    'timeSpent' => $userCourse->getTimeSpent(),
                    'type' => null !== $userCourse->getAnnouncement() ? 'announcement' : 'professional',
                    'url' => $adminUrlGenerator
                        ->unsetAll()
                        ->setController(UserCrudController::class)
                        ->setAction('userCourse')
                        ->set('user_course_id', $userCourse->getId())
                        ->addSignature()
                        ->generateUrl(),
                ];
            } catch (EntityNotFoundException $notFoundException) {
            }
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'courses' => $courses,
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/user/{id}/logins", name="admin_user_logins")
     *
     * @return Response
     */
    public function userLogins(User $user, UserLoginRepository $userLoginRepository)
    {
        $logins = $userLoginRepository->getDailyLoginsByUser($user);

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'logins' => $logins,
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/user/{id}/time-spent", name="admin_user_time_spent")
     *
     * @return Response
     */
    public function userTimeSpent(User $user, UserCourseChapterRepository $userCourseChapterRepository)
    {
        // $times = $userCourseChapterRepository->getTimeSpentByTypeAndUser($user);

        $itineraries = $this->statsUserService->getItinerariesUser($user);
        $announcements = $this->statsUserService->getAnnouncementUser($user);
        $filters = $this->statsUserService->getCoursesByFilter($user);
        $coursesVoluntary = $this->statsUserService->getVoluntaryTrainingUser($user);

        $coursesMerge = array_merge($itineraries, $announcements, $filters, $coursesVoluntary);
        $times = [];

        foreach ($coursesMerge as $course) {
            // $course['timeSpent'] lo recibo asi 00:00:00, quiero su valor en segundos

            $seconds = strtotime($course['timeSpent']) - strtotime('00:00:00');
            $times[] = [
                'name' => $course['name'],
                'time' => $seconds,
            ];
        }

        $niceTime = function ($seconds) {
            if (0 === $seconds) {
                return '-';
            }

            $hours = floor($seconds / 3600);
            $mins = floor(($seconds - $hours * 3600) / 60);

            $return = [];
            if ($hours > 0) {
                $return[] = $hours . ' ' . (
                    1 == $hours
                        ? $this->translator->trans('course.stats.hour', [], 'messages')
                        : $this->translator->trans('course.stats.hours', [], 'messages')
                );
            }
            if ($mins > 0) {
                $return[] = $mins . ' ' . (
                    1 == $mins
                        ? $this->translator->trans('course.stats.minute', [], 'messages')
                        : $this->translator->trans('course.stats.minutes', [], 'messages')
                );
            }

            if (empty($return)) {
                $return[] = $seconds . ' ' . (
                    1 == $seconds
                        ? $this->translator->trans('course.stats.second', [], 'messages')
                        : $this->translator->trans('course.stats.seconds', [], 'messages')
                );
            }

            return implode(' ', $return);
        };

        $timeSpent = [];
        foreach ($times as $time) {
            $timeSpent[] = [
                'name' => $time['name'] ?? $this->translator->trans('user.configureFields.courses_stats.others', [], 'messages'),
                'y' => (int) $time['time'],
                'time' => $niceTime($time['time']),
            ];
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'time' => $timeSpent,
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @return Response
     */
    public function userCourse(AdminContext $context)
    {
        $userCourseRepository = $this->em->getRepository(UserCourse::class);
        $messageRepository = $this->em->getRepository(Message::class);
        $announcementUserRepository = $this->em->getRepository(AnnouncementUser::class);

        $userCourse = $userCourseRepository->find($this->requestStack->getCurrentRequest()->get('user_course_id'));

        $userChapters = [];
        if ($userCourse) {
            foreach ($userCourse->getChapters() as $chapter) {
                $userChapters[$chapter->getChapter()->getId()] = $chapter;
            }
        }

        $messages = [];
        $announcementUser = null;
        if ($userCourse->getAnnouncement()) {
            $announcementUser = $announcementUserRepository->findOneBy([
                'user' => $userCourse->getUser(),
                'announcement' => $userCourse->getAnnouncement(),
            ]);

            $messages = $messageRepository->getBetweenTwo($userCourse->getUser(), $userCourse->getAnnouncement()->getTutor());
        }

        return $this->render('admin/user/user-course.html.twig', [
            'userCourse' => $userCourse,
            'userChapters' => $userChapters,
            'messages' => $messages,
            'resultsTypes' => [Games::QUIZ_TYPE, Games::WHEEL_TYPE, Games::PUZZLE_TYPE, Games::LETTERS_WHEEL_TYPE],
            'announcementUser' => $announcementUser,
        ]);
    }

    public function validatedAction(AdminContext $context)
    {
        // $id = $context->getRequest()->query->get('entityId');
        $entity = $context->getEntity();
        $userRepository = $this->em->getRepository(User::class);
        $user = $userRepository->find($entity->getPrimaryKeyValue());
        $locale = (isset($user) && null != $user->getLocale()) ? $user->getLocale() : $this->settings->get('app.defaultLanguage');

        $user->setValidated(1);
        $this->em->persist($user);
        $this->em->flush();

        $this->sendNotification($user->getEmail(), $locale);

        return $this->redirect($this->get(AdminUrlGenerator::class)->setAction(Action::INDEX)->generateUrl());
    }

    public function sendNotification($email, $locale)
    {
        $userRepository = $this->em->getRepository(User::class);
        $user = $userRepository->findOneBy(['email' => $email]);
        $codigo = date('His');

        $recovery = new RecoveryCode();
        $recovery->setUser($user);
        $recovery->setEmail($email);
        $recovery->setDateRecovery(new \DateTime());
        $recovery->setCodeActivation(md5($codigo));
        $recovery->setState(0);

        $this->em->persist($recovery);
        $this->em->flush();

        $sendEmail = (new TemplatedEmail())
            ->from(new Address($this->settings->get('app.fromEmail'), $this->settings->get('app.fromName')))
            ->to(new Address($email))
            ->subject($this->translator->trans('email.template_email.active_account', [], 'email', $locale))
            ->text('Activar cuenta')

            ->htmlTemplate('template_email/register-user-free.html.twig')

            ->context([
                'user' => $user,
                'codigo' => md5($codigo),
                'locale' => $locale,
                'appFromName' => $this->settings->get('app.fromName'),
            ]);

        $this->mailer->send($sendEmail);
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        switch ($responseParameters->get('pageName')) {
            case Crud::PAGE_INDEX:
                $this->getHelp($responseParameters, 'User');
                $filters = $this->getFilterCategories();

                $responseParameters->set('filters', $filters);
                break;
            case Crud::PAGE_EDIT:
                if ($this->settings->get('app.user.useFilters')) {
                    $entityId = $this->adminContextProvider->getContext()->getEntity()->getPrimaryKeyValue();
                    $filtersQueryResult = $this->em->getRepository(User::class)->createQueryBuilder('u')
                        ->select('f.id, f.name, cat.id as category_id')
                        ->join('u.filter', 'f')
                        ->join('f.filterCategory', 'cat')
                        ->andWhere('u.id = :userId')
                        ->setParameter('userId', $entityId)
                        ->getQuery()
                        ->getResult();

                    $categories = $this->em->getRepository(FilterCategory::class)->findAll();
                    $categoryFilters = [];
                    /** @var FilterCategory $cat */
                    foreach ($categories as $cat) {
                        $categoryFilters[] = [
                            'id' => $cat->getId(),
                            'selected' => [],
                        ];
                    }

                    foreach ($filtersQueryResult as $result) {
                        $found = false;
                        $foundIndex = null;

                        foreach ($categoryFilters as $index => $category) {
                            $this->logger->error('categoryError', [$result]);
                            if ($category['id'] == $result['category_id']) {
                                $foundIndex = $index;
                                $found = true;
                            }
                        }

                        if ($found) {
                            $categoryFilters[$foundIndex]['selected'][] = [
                                'id' => $result['id'],
                                'name' => $result['name'],
                            ];
                        } else {
                            $categoryFilters[] = [
                                'id' => $result['id'],
                                'selected' => [
                                    [
                                        'id' => $result['id'],
                                        'name' => $result['name'],
                                    ],
                                ],
                            ];
                        }
                    }

                    $responseParameters->set('userFilters', $categoryFilters);
                }
                break;
            case Crud::PAGE_DETAIL:
                $responseParameters->set('userLocale', $this->getUser()->getLocale());
                $filter_categories = $this->getFiltersByCategories();
                $responseParameters->set('filter_categories', $filter_categories);
                $responseParameters->set('defaultLocale', $this->settings->get('app.defaultLanguage'));
                break;
        }

        $responseParameters->set('enableUserFilters', (bool) $this->settings->get('app.user.useFilters'));
        $responseParameters->set('user_use_filters', (bool) $this->settings->get('app.user.useFilters'));

        return $responseParameters;
    }

    private function getFiltersByCategories(): array
    {
        $locale = $this->getUser()->getLocale();
        $entityId = $this->adminContextProvider->getContext()->getEntity()->getPrimaryKeyValue();
        $userFilters = $this->em->getRepository(User::class)->find($entityId)->getFilter();
        $filterCategories = [];
        if (!empty($userFilters)) {
            foreach ($userFilters as $filter) {
                $categoryFilterId = $this->em->getRepository(FilterCategory::class)->findByFilterOrderBySort($filter->getFilterCategory()->getId());
                if (!\in_array($categoryFilterId, $filterCategories)) {
                    $filterCategories[] = $categoryFilterId;
                }
            }
        }

        $data = [];

        if (!empty($filterCategories)) {
            foreach ($filterCategories as $category) {
                $filterTranslation = $this->em->getRepository(FilterCategoryTranslation::class)->findOneBy(['translatable' => $category, 'locale' => $locale]);
                if ($category->getFilters()->count() > 0) {
                    $data[] = [
                        'id' => $category->getId(),
                        'name' => $filterTranslation ? $filterTranslation->getName() : $category->getName(),
                        'filters' => $this->getFiltersByCategory($category, $userFilters),
                    ];
                }
            }
        }

        return $data;
    }

    private function getFiltersByCategory(FilterCategory $category, $userFilters = null): array
    {
        $locale = $this->getUser()->getLocale();
        $filters = [];
        $filtersByCategory = [];
        foreach ($userFilters as $filter) {
            if ($filter->getFilterCategory()->getId() == $category->getId()) {
                $filtersByCategory[] = $this->em->getRepository(Filter::class)->findOneBy(['filterCategory' => $category, 'id' => $filter->getId()], ['sort' => 'ASC']);
            }
        }

        foreach ($filtersByCategory as $filter) {
            $name = $this->em->getRepository(FilterTranslation::class)->getNameTranslation($filter->getId(), $locale);
            $filters[] = [
                'id' => $filter->getId(),
                'name' => !empty($name) ? $name : $filter->getName(),
            ];
        }

        return $filters;
    }

    /**
     * @Route("/filter/{id}", name="filter", methods={"GET"})
     *
     * @param \App\Controller\Admin\FilterCategory $filterCategory
     *
     * @return Response
     */
    public function filter(FilterCategory $filterCategory)
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;

            $filter = $this->em->getRepository(Filter::class)->findBy([
                'filterCategory' => $filterCategory,
            ]);

            $response = [
                'status' => $code,
                'error' => $error,
                'data' => $filter,
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the user - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['details']]);
    }

    /**
     * @Route("/filters", name="filters", methods={"GET","POST"})
     *
     * @return Response
     */
    public function filters(Request $request)
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;

            $filters[] = json_decode($request->get('filtro'));

            foreach ($filters as $fil) {
                $filter = $this->em->getRepository(Filter::class)->findBy([
                    'id' => $fil,
                ]);
                foreach ($filter as $fl) {
                    $array[] = $fl->getFilterCategory()->getId();

                    $uniques = array_unique($array);
                    $count_values = array_count_values($array);
                }

                foreach ($uniques as $uni) {
                    $filterCategory = $this->em->getRepository(FilterCategory::class)->find([
                        'id' => $uni,
                    ]);
                    $data[] = [
                        'id' => $uni,
                        'name' => $filterCategory->getName(),
                        'duplicated' => $count_values[$uni],
                    ];
                }
            }

            $response = [
                'status' => $code,
                'error' => $error,
                'data' => $filter,
                'count_values' => $count_values,
                'uniques' => $uniques,
                'filter' => $this->orderBy($data, 'duplicated', 'asc'),
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the user - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['details']]);
    }

    public function orderBy($items, $attr, $order)
    {
        $sortedItems = [];
        foreach ($items as $item) {
            $key = \is_object($item) ? $item->{$attr} : $item[$attr];
            $sortedItems[$key] = $item;
        }
        if ('desc' === $order) {
            krsort($sortedItems);
        } else {
            ksort($sortedItems);
        }

        return array_values($sortedItems);
    }

    /**
     * @Route("/admin/user_data_export", name="download_users_data", methods={"POST"})
     */
    public function downloadExcelUser(Request $request, ExportRepository $exportRepository, UserCourseChapterRepository $userCourseChapterRepository, UserCourseRepository $userCourseRepository)
    {
        if ($request->isMethod('post')) {
            $conditions['startDate'] = $request->get('start');
            $conditions['endDate'] = $request->get('end');

            $conditions['customFilters'] = $request->get('custom');
            $filename = !empty($request->get('filenameuser')) ? $request->get('filenameuser') : 'stats-export';

            if ($this->settings->get('app.export.active_cron_exports')) {
                $this->logger->error(serialize($conditions));
                $this->addFlash('success', $this->translator->trans('stats.export.export_success', [], 'messages', $this->getUser()->getLocale()) .
                    '  ' . $this->translator->trans('stats.export.export_dir', [], 'messages', $this->getUser()->getLocale()));

                $result = $this->taskService->enqueueTask(
                    $this->getUser(),
                    'export-file',
                    $conditions,
                    'user-stats-export',
                    $filename
                );

                return new JsonResponse($result);
            }

            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
            $userDataItems = $userCourseChapterRepository->getStatsUser($conditions, $user);

            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('Stats Data - Export');
            $writer = new Xlsx($spreadsheet);

            $filterCategory = $this->em->getRepository(FilterCategory::class)->findAll();
            $categories = [];

            foreach ($filterCategory as $filCat) {
                $categories[] = $filCat->getName();
            }

            $headExcel1 = ['User id', 'Nombre', 'Apellidos', 'Email', 'Tiempo en la plataforma', 'Puntos'];
            $headExcel1_gender = ['User id', 'Nombre', 'Apellidos', 'Email', 'Género', 'Tiempo en la plataforma'];
            $headExcel2 = ['Cursos Finalizados', 'Cursos empezados'];

            $headExcel = $this->settings->get('app.export.gender_excel')
                ? array_merge($headExcel1_gender, $categories, $headExcel2) :
                array_merge($headExcel1, $categories, $headExcel2);

            $sheet->fromArray([$headExcel]);

            $row = 1;
            foreach ($userDataItems as $userDataItem) {
                $userId = $userDataItem['id'];
                $userFind = $this->em->getRepository(User::class)->find($userId);
                $finishedCourses = $userCourseRepository->countByUser($userFind, true);
                $startedCourses = $userCourseRepository->countByUser($userFind, true) + $userCourseRepository->countByUser($userFind, false);
                /*  $roles = $userFind->getRoles();
                $userDataItem['roles'] =  implode(',', $roles); */

                foreach ($filterCategory as $filCat) {
                    $filtersUser = $this->em->getRepository(Filter::class)->fetchFiltersUsers($userFind->getId(), $filCat->getId());
                    $userDataItem['filtro_' . $filCat->getId()] = '' != $filtersUser ? $filtersUser['name'] : '';
                }

                $userDataItem['total'] = $this->timeUserInPlatform($userDataItem['total']);
                $userDataItem['coursesFinished'] = (int) $finishedCourses;
                $userDataItem['coursesStarted'] = (int) $startedCourses;
                if (!$this->settings->get('app.export.gender_excel')) {
                    unset($userDataItem['gender']);
                }

                $sheet->fromArray([$userDataItem], null, 'A' . ++$row);
            }

            foreach (range('A', 'L') as $columnID) {
                $sheet->getColumnDimension($columnID)->setAutoSize(true);
            }

            $fileName = "{$filename}.xlsx";
            $tempFile = tempnam(sys_get_temp_dir(), $fileName);
            $writer->save($tempFile);

            return $this->file($tempFile, $fileName, ResponseHeaderBag::DISPOSITION_INLINE);
        }

        return new Response('Bad Request: Only POST method is allowed', Response::HTTP_BAD_REQUEST);
    }

    private function timeUserInPlatform($time)
    {
        $horas = floor($time / 3600);
        $minutos = floor(($time - ($horas * 3600)) / 60);
        $segundos = $time - ($horas * 3600) - ($minutos * 60);

        return $horas . ':' . $minutos . ':' . $segundos;
    }

    /**
     * @Route ("/admin/user/{id}/itinerary", name="admin_user_itinerary")
     */
    public function usersItinerary(User $user, AdminUrlGenerator $adminUrlGenerator): Response
    {
        $itineraries = [];
        foreach ($this->em->getRepository(User::class)->getItineraryStats($user) as $itinerary) {
            $itineraries[] = [
                'itinerary_id' => $itinerary['itinerary_id'],
                'name' => $itinerary['name'],
                'started' => $itinerary['started'],
                'completed' => $itinerary['completed'],
                'total' => $itinerary['total'],
                'url' => $adminUrlGenerator->unsetAll()
                    ->setController(ItineraryCrudController::class)
                    ->setAction('detail')
                    ->set('entityId', $itinerary['itinerary_id'])
                    ->addSignature()
                    ->generateUrl(),
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => true,
            'data' => [
                'itineraries' => $itineraries,
                'tableTitlesTranslations' => [
                    'name' => $this->translator->trans('itinerary.name', [], 'messages', $this->getUser()->getLocale()),
                    'total' => $this->translator->trans('common_areas.total', [], 'messages', $this->getUser()->getLocale()),
                    'started' => $this->translator->trans('stats.accumulative.courses_started', [], 'messages', $this->getUser()->getLocale()),
                    'completed' => $this->translator->trans('stats.accumulative.courses_finished', [], 'messages', $this->getUser()->getLocale()),
                ],
            ],
        ]);
    }

    /**
     * @Route("/user/set-password", name="user-set-password")
     */
    public function userSetPassword(Request $request)
    {
        /**
         * @var $users User[]
         */
        $users = $this->em->getRepository(User::class)->findBy([
            'password' => '',
        ], ['id' => 'ASC'], 400, 0);

        foreach ($users as $user) {
            $this->logger->error($user->getEmail());

            if ('' != $user->getCode()) {
                $user->setPassword($user->getCode());
            } else {
                $user->setPassword($user->getEmail());
            }
            $user->setRegisterKey($user->getCode());
            $user->setIsActive(true);
        }

        $this->em->flush();

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/user/impersonate", name="admin_user_impersonate", methods={"POST"})
     *
     * @return JsonResponse|Response
     */
    public function getToken(Request $request, JWTTokenManagerInterface $JWTManager)
    {
        $content = json_decode($request->getContent(), true);

        $user = $this->em->find(User::class, $content['user']);

        if ($user) {
            $result = $this->impersonateAsInspector(
                $this->em,
                $user->getId(),
                []
            );

            $requestUri = $request->getRequestUri();
            $uri = $request->getUri();
            $baseUrl = str_replace($requestUri, '', $uri);

            $classMetadataFactory = new ClassMetadataFactory(new AnnotationLoader(new AnnotationReader()));
            $metadataAwareNameConverter = new MetadataAwareNameConverter($classMetadataFactory);
            $normalizer = new ObjectNormalizer($classMetadataFactory, $metadataAwareNameConverter);
            $serializer = new Serializer([new DateTimeNormalizer(['datetime_format' => 'd-m-Y H:i:s e']), $normalizer], [new JsonEncoder()]);

            return new JsonResponse([
                'token' => $JWTManager->create($user),
                'url' => "$baseUrl" . $result,
                'user' => $serializer->serialize($user, 'json', [
                    'groups' => ['user_area'],
                    'circular_reference_handler' => function ($object) {
                        return $object->getId();
                    },
                ]),
            ]);
        }

        $data = [
            'status' => Response::HTTP_NOT_FOUND,
            'error' => true,
            'data' => null,
        ];

        return $this->sendResponse($data);
    }

    /**
     * @Rest\Post("/admin/user/{id}/user-cv", name="admin_user_upload_cv", requirements={"id"="\d+"})
     */
    public function uploadUserCV(Request $request, User $user): Response
    {
        try {
            /** @var UploadedFile|null $file */
            $file = $request->files->get('file');
            if (!$file) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'No file has been provided',
                ]);
            }

            $userExtra = $this->em->getRepository(UserExtra::class)->findOneBy(['user' => $user]);
            if (!$userExtra) {
                $userExtra = new UserExtra();
                /** @var ProfessionalCategory $category */
                $category = $this->em->getRepository(ProfessionalCategory::class)->createQueryBuilder('c')
                    ->select('c')
                    ->orderBy('c.id', 'ASC')
                    ->setMaxResults(1)
                    ->getQuery()
                    ->getOneOrNullResult();
                $userExtra->setUser($user)
                    ->setCategory($category);
            }
            $userExtra->setPdfCv($file->getClientOriginalName())
                ->setPdfCvFile($file);
            $this->em->persist($userExtra);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false,
                'data' => 'CREATED',
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => false,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Delete("/admin/user/{id}/user-cv", name="admin_user_delete_cv", requirements={"id"="\d+"})
     */
    public function removeUserCV(User $user): Response
    {
        try {
            $extra = $this->em->getRepository(UserExtra::class)->findOneBy(['user' => $user]);
            if (!$extra) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'NOT_FOUND',
                ]);
            }

            $extra->setPdfCv(null)
                ->setPdfCvFile(null);

            $this->em->flush($extra);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'DELETED',
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    // Upload cv user
    /**
     * @Route("/admin/cv-user/{user}/announcement/{announcement}", name="cv_user", methods={"GET","POST"})
     */
    public function uploadCvUser(Request $request, User $user, Announcement $announcement)
    {
        try {
            $cv = $request->files->get('cv');
            $userExtra = $this->em->getRepository(UserExtra::class)->findOneBy(['user' => $user]);
            if ($userExtra) {
                $userExtra->setPdfCv($cv->getClientOriginalName());
                $userExtra->setPdfCvFile($cv);
            } else {
                $userExtra = new UserExtra();
                $userExtra->setUser($user);
                $userExtra->setPdfCv($cv->getClientOriginalName());
                $userExtra->setPdfCvFile($cv);
            }
            $this->em->persist($userExtra);
            $this->em->flush();

            $this->addFlash('success', $this->translator->trans('save_success'));
        } catch (\Exception $e) {
            $this->addFlash('danger', $this->translator->trans('chapter.message_pdf_error') . $e);
        }

        return $this->redirectToAnnouncement($announcement);
    }

    /**
     * @Route("/admin/cv-user/{user}/announcement/{announcement}/delete", name="cv_user_delete", methods={"GET","POST"})
     */
    public function deleteCvUser(Request $request, User $user, Announcement $announcement)
    {
        try {
            $userExtra = $this->em->getRepository(UserExtra::class)->findOneBy(['user' => $user]);
            if ($userExtra) {
                $this->deleteFile($userExtra->getPdfCv());
                $userExtra->setPdfCv(null);
                $userExtra->setPdfCvFile(null);
            }
            $this->em->persist($userExtra);
            $this->em->flush();
        } catch (\Exception $e) {
            $this->addFlash('danger', $this->translator->trans('chapter.message_pdf_error') . $e);
        }

        return $this->redirectToAnnouncement($announcement);
    }

    private function deleteFile($file)
    {
        $filename = $this->settings->get('app.pdf_cv_file_uploads_path') . $file;

        if (file_exists($filename)) {
            $success = unlink($filename);

            if (!$success) {
                throw $this->createNotFoundException("Cannot delete $filename");
            }
        }
    }

    private function redirectToAnnouncement($announcement)
    {
        return $this->redirect(
            $this->adminUrlGenerator
                ->unsetAll()
                ->setController(AnnouncementCrudController::class)
                ->setAction('detail')
                ->set('tab', 'tutor')
                ->setEntityId($announcement->getId())
                ->generateUrl()
        );
    }

    /**
     * @Route("/admin/user/{user}/redirect", name="admin_user_redirect", methods={"GET"})
     */
    public function redirectToUser(User $user): RedirectResponse
    {
        return $this->redirect(
            $this->adminUrlGenerator
                ->unsetAll()
                ->setController(UserCrudController::class)
                ->setAction('detail')
                ->setEntityId($user->getId())
                ->generateUrl()
        );
    }

    /**
     * @Rest\Delete("/admin/user/{user}/delete")
     */
    public function deleteUser(User $user): Response
    {
        $this->em->remove($user);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'url' => $this->adminUrlGenerator
                ->unsetAll()
                ->setController(UserCrudController::class)
                ->setAction('index')
                ->generateUrl(),
        ]);
    }

    /**
     * @Rest\Get("/admin/user/{id}/user-fields-fundae")
     */
    public function getUserFieldsFundae(User $user): Response
    {
        $fieldFundae = $user->getUserFieldsFundae();
        if (!$fieldFundae) {
            $fieldFundae = new UserFieldsFundae();
            $fieldFundae->setUser($user);
            $user->setUserFieldsFundae($fieldFundae);
            $this->em->persist($user);
            $this->em->flush();
        }

        $userCompany = $fieldFundae->getUserCompany();
        $userProfessionalCategory = $fieldFundae->getUserProfessionalCategory();
        $userWorkCenter = $fieldFundae->getUserWorkCenter();
        $userWorkDepartment = $fieldFundae->getUserWorkDepartment();
        $userStudyLevel = $fieldFundae->getUserStudyLevel();
        $birthdate = $fieldFundae->getBirthdate();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'socialSecurityNumber' => $fieldFundae->getSocialSecurityNumber(),
                'gender' => $fieldFundae->getGender(),
                'emailWork' => $fieldFundae->getEmailWork(),
                'birthdate' => $birthdate ? substr($birthdate->format('c'), 0, 10) : null,
                'dni' => $fieldFundae->getDni(),
                'contributionAccount' => $fieldFundae->getContributionAccount(),
                'userCompany' => $userCompany ? $userCompany->getId() : null,
                'userProfessionalCategory' => $userProfessionalCategory ? $userProfessionalCategory->getId() : null,
                'userWorkCenter' => $userWorkCenter ? $userWorkCenter->getId() : null,
                'userWorkDepartment' => $userWorkDepartment ? $userWorkDepartment->getId() : null,
                'userStudyLevel' => $userStudyLevel ? $userStudyLevel->getId() : null,
            ],
        ]);
    }

    /**
     * @Rest\Post("/admin/user/{id}/user-fields-fundae")
     */
    public function saveUserFieldsFundae(User $user, Request $request): Response
    {
        $socialSecurityNumber = $request->get('socialSecurityNumber');
        $gender = $request->get('gender');
        $emailWork = $request->get('emailWork');
        $birthdate = $request->get('birthdate');
        $dni = $request->get('dni');
        $contributionAccount = $request->get('contributionAccount');
        $userCompanyId = $request->get('userCompany');
        $userProfessionalCategoryId = $request->get('userProfessionalCategory');
        $userWorkCenterId = $request->get('userWorkCenter');
        $userWorkDepartmentId = $request->get('userWorkDepartment');
        $userStudyLevelId = $request->get('userStudyLevel');
        $dtBirthDate = !empty($birthdate) ? new \DateTime($birthdate) : null;

        $userCompany = empty($userCompanyId) ? null : $this->em->getRepository(UserCompany::class)->find($userCompanyId);

        $userProfessionalCategory = empty($userProfessionalCategoryId) ? null :
            $this->em->getRepository(UserProfessionalCategory::class)->find($userProfessionalCategoryId);
        $userWorkCenter = empty($userWorkCenterId) ? null :
            $this->em->getRepository(UserWorkCenter::class)->find($userWorkCenterId);
        $userWorkDepartment = empty($userWorkDepartmentId) ? null :
            $this->em->getRepository(UserWorkDepartment::class)->find($userWorkDepartmentId);
        $userStudyLevel = empty($userStudyLevelId) ? null :
            $this->em->getRepository(UserStudyLevel::class)->find($userStudyLevelId);

        $fieldFundae = $user->getUserFieldsFundae();
        if (!$fieldFundae) {
            $fieldFundae = new UserFieldsFundae();
            $fieldFundae->setUser($user);
        }

        $fieldFundae->setSocialSecurityNumber($socialSecurityNumber)
            ->setGender($gender)
            ->setEmailWork($emailWork)
            ->setBirthdate($dtBirthDate)
            ->setDni($dni)
            ->setContributionAccount($contributionAccount)
            ->setUserCompany($userCompany)
            ->setUserProfessionalCategory($userProfessionalCategory)
            ->setUserWorkCenter($userWorkCenter)
            ->setUserWorkDepartment($userWorkDepartment)
            ->setUserStudyLevel($userStudyLevel);

        $this->em->persist($fieldFundae);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'SAVED',
        ]);
    }

    public function createExcelAnnouncement($content)
    {
        $headExcel1 = [
            $this->translator->trans('excel.userAnnouncement.sheet1.colum1', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet1.colum2', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet1.colum3', [], 'messages', $this->getUser()->getLocale()),
        ];
        $headExcel2 = [
            $this->translator->trans('excel.userAnnouncement.sheet2.colum1', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum2', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum3', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum4', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum5', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum6', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum7', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum8', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum9', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum10', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet2.colum11', [], 'messages', $this->getUser()->getLocale()),
        ];
        $headExcel3 = [
            $this->translator->trans('excel.userAnnouncement.sheet3.colum1', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet3.colum2', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet3.colum3', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet3.colum4', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet3.colum5', [], 'messages', $this->getUser()->getLocale()),
            $this->translator->trans('excel.userAnnouncement.sheet3.colum6', [], 'messages', $this->getUser()->getLocale()),
        ];

        // first sheet ======================
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle($this->translator->trans('excel.userAnnouncement.sheet1.title', [], 'messages', $this->getUser()->getLocale()));
        $writer = new Xlsx($spreadsheet);
        $sheet->fromArray([$headExcel1]);

        foreach (range('A', 'C') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        // add values first sheet
        $row = 1;
        $row1Sheet1 = \count($this->em->getRepository(Itinerary::class)->totalItineraryForExcel($content));
        $totalCourses = 0;
        $totalUsers = 0;

        // add values seconds sheet and 2 last column from first sheet
        $data = $this->em->getRepository(Itinerary::class)->detailItineraryForExcelAnnouncement($content);
        foreach ($data as &$valor) {
            $itineraryObject = $this->em->getRepository(Itinerary::class)->find($valor['id']);
            $totalCourses += $valor['cursos_asignados'];

            $result = $this->em->getRepository(Itinerary::class)->getUsersPaginated(
                $itineraryObject,
                []
            );

            $resultFilter = $this->em->getRepository(Itinerary::class)->getUsersByItineraryFilters(
                $itineraryObject
            );
            if (\is_null($resultFilter)) {
                $resultFilter = [];
            }

            foreach ($result as $item) {
                if ($item['completed'] == $valor['cursos_asignados']) {
                    ++$valor['personas_completo_itineraro'];
                }

                if (($item['completed'] > 0 && $item['completed'] < $valor['cursos_asignados']) || $item['started'] > 0) {
                    ++$valor['personas_en_proceso'];
                }
            }

            // delete duplicated users
            $finalFilterArray = [];
            foreach ($resultFilter as $item) {
                $find = false;
                foreach ($result as $subItem) {
                    if ($item['id'] == $subItem['id']) {
                        $find = true;
                    }
                }
                if (false === $find) {
                    array_push($finalFilterArray, $item);
                }
            }

            foreach ($finalFilterArray as $item) {
                if ($item['completed'] == $valor['cursos_asignados']) {
                    ++$valor['personas_completo_itineraro'];
                }

                if (($item['completed'] > 0 && $item['completed'] < $valor['cursos_asignados']) || $item['started'] > 0) {
                    ++$valor['personas_en_proceso'];
                }
            }
            $valor['personas_sin_empezar'] = (\count($result) + \count($finalFilterArray)) -
                ($valor['personas_completo_itineraro'] + $valor['personas_en_proceso']);

            $valor['personas_asignadas'] = (\count($result) + \count($finalFilterArray));
            $totalUsers += $valor['personas_asignadas'];

            $valor['total_tiempo'] = round(\floatval($this->em->getRepository(Itinerary::class)->getTimeSpentInCourses(
                $itineraryObject
            )['total_time']), 0);

            $personas_asignadas = 0 == $valor['personas_asignadas'] ? 1 : $valor['personas_asignadas'];
            $valor['tiempo_medio_persona'] = round(
                $valor['total_tiempo'] /
                $personas_asignadas
            );

            $valor['total_tiempo'] = $this->timeUserInTheCourse($valor['total_tiempo']);
            $valor['tiempo_medio_persona'] = $this->timeUserInTheCourse($valor['tiempo_medio_persona']);
        }

        // write first sheet
        $dataSheet1 = [
            $row1Sheet1,
            $totalUsers,
            $totalCourses,
        ];
        $sheet->fromArray([$dataSheet1], null, 'A' . ++$row);

        // create and write second sheet
        $sheet = $spreadsheet->createSheet(2);
        $sheet = $spreadsheet->setActiveSheetIndex(1);
        $sheet->setTitle($this->translator->trans('excel.userAnnouncement.sheet2.title', [], 'messages', $this->getUser()->getLocale()));
        $sheet->fromArray([$headExcel2]);

        foreach (range('A', 'K') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        $row = 1;
        foreach ($data as $item) {
            $sheet->fromArray([$item], null, 'A' . ++$row);
        }

        // add sheet  ======================
        $sheet = $spreadsheet->createSheet(3);
        $sheet = $spreadsheet->setActiveSheetIndex(2);
        $sheet->setTitle($this->translator->trans('excel.userAnnouncement.sheet3.title', [], 'messages', $this->getUser()->getLocale()));
        $sheet->fromArray([$headExcel3]);

        // Data sheet #3 =====================
        $dataSheet3 = $this->em->getRepository(Itinerary::class)->detailCoursesForExcelAnnouncement($content);
        foreach ($dataSheet3 as &$valor) {
            $itineraryObject = $this->em->getRepository(Itinerary::class)->find($valor['id']);

            $result = $this->em->getRepository(Itinerary::class)->getUsersPaginatedByCourse(
                itinerary: $itineraryObject,
                courseId: $valor['courseId']
            );

            $resultFilter = $this->em->getRepository(Itinerary::class)->getUsersByItineraryFiltersByCourse(
                itinerary: $itineraryObject,
                coursesId: $valor['courseId']
            );
            if (\is_null($resultFilter)) {
                $resultFilter = [];
            }

            // delete duplicated users
            $finalFilterArray = [];
            foreach ($resultFilter as $item) {
                $find = false;
                foreach ($result as $subItem) {
                    if ($item['id'] == $subItem['id']) {
                        $find = true;
                    }
                }
                if (false === $find) {
                    array_push($finalFilterArray, $item);
                }
            }

            foreach ($finalFilterArray as $item) {
                if (0 !== $item['completed']) {
                    ++$valor['personas_completo_itineraro'];
                }

                if (0 !== $item['started']) {
                    ++$valor['personas_en_proceso'];
                }
            }
            $valor['personas_sin_empezar'] = abs((\count($result) + \count($finalFilterArray)) -
                ($valor['personas_completo_itineraro'] + $valor['personas_en_proceso']));
        }

        $row = 1;
        unset($dataSheet3['courseId']);
        foreach ($dataSheet3 as $item) {
            $sheet->fromArray(
                [
                    'id' => $item['id'],
                    'name' => $item['name'],
                    'curso' => $item['curso'],
                    'personas_completo_itineraro' => $item['personas_completo_itineraro'],
                    'personas_en_proceso' => $item['personas_en_proceso'],
                    'personas_sin_empezar' => $item['personas_sin_empezar'],
                ],
                null,
                'A' . ++$row
            );
        }
        foreach (range('A', 'K') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        // write file
        $filename = '';
        $fileName = "{$filename}.xlsx";
        $tempFile = tempnam(sys_get_temp_dir(), $fileName);
        $writer->save($tempFile);

        return $this->file($tempFile, $fileName, ResponseHeaderBag::DISPOSITION_INLINE);
    }

    // DERELICT CODE - TODO
    /**
     * @Route("/admin/user_announcement_export", name="download_users_announcement_data", methods={"POST"})
     *
     * @param input json example: { "courseId": 43, "tags": [1,2,3] }
     */
    public function downloadExcelUserAnnouncement(Request $request, ExportRepository $exportRepository)
    {
        $content = json_decode($request->getContent(), true);
        $conditions['courseId'] = empty($content['courseId']) ? '' : $content['courseId'];
        $conditions['tags'] = empty($content['tags']) ? '' : $content['tags'];

        // convertir array de tags a string
        $conditions['tags'] = json_encode($conditions['tags']);

        if ($this->settings->get('app.export.active_cron_exports')) {
            $filename = 'announcement-stats-export-' . rand();
            $this->logger->error(serialize($conditions));
            $this->addFlash('success', $this->translator->trans('stats.export.export_success', [], 'messages', $this->getUser()->getLocale()) .
                '  ' . $this->translator->trans('stats.export.export_dir', [], 'messages', $this->getUser()->getLocale()));

            return $this->taskService->enqueueTask(
                $this->getUser(),
                'export-file',
                $conditions,
                'user-announcement-export',
                $filename
            );
        }

        return $this->createExcelAnnouncement($content);
    }

    /**
     * @Security("is_granted('ROLE_SUPER_ADMIN') or is_granted('ROLE_ADMIN') or is_granted('ROLE_MANAGER')")
     *
     * @Route("/admin/announcement_participants_export", name="download_announcement_participants_data", methods={"POST"})
     *
     * @param input json example: { "courseId": 43, "tags": [1,2,3] }
     */
    public function downloadExcelAnnouncementParticipants(Request $request)
    {
        try {
            $content = json_decode($request->getContent(), true);
            $conditions['courseId'] = empty($content['courseId']) ? '' : $content['courseId'];

            $conditions['startDate'] = empty($content['startDate']) ? null : $content['startDate'];
            $conditions['endDate'] = empty($content['endDate']) ? null : $content['endDate'];
            $conditions['status'] = empty($content['status']) ? null : $content['status'];

            $conditions['extra'] = empty($content['extras']) ? null : $content['extras'];

            $conditions['tags'] = empty($content['tags']) ? '' : $content['tags'];
            $conditions['tags'] = json_encode($conditions['tags']);
            $conditions['lang'] = $this->getUser()->getLocale();

            if ($this->settings->get('app.export.active_cron_exports')) {
                $filename = 'announcement-participants-export-' . rand();

                $this->taskService->enqueueTask(
                    $this->getUser(),
                    'export-stats-file',
                    $conditions,
                    'announcement-participants-export',
                    $filename
                );

                return $this->sendResponse([
                    'status' => Response::HTTP_OK,
                    'error' => false,
                    'data' => $this->translator->trans('stats.task.queued', [], 'messages', $this->getUser()->getLocale()),
                ]);
            }

            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => true,
                'data' => '',
            ]);
        } catch (\Exception $e) {
            $statusCode = method_exists($e, 'getStatusCode')
                ? $e->getStatusCode()
                : Response::HTTP_INTERNAL_SERVER_ERROR;

            return $this->sendResponse([
                'status' => $statusCode,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    private function timeUserInTheCourse($time)
    {
        $horas = floor($time / 3600);
        $minutos = floor(($time - ($horas * 3600)) / 60);
        $segundos = $time - ($horas * 3600) - ($minutos * 60);

        return $horas . ':' . $minutos . ':' . $segundos;
    }

    /**
     * @Route("/admin/user/{id}/send-email-for-validation", name="admin_send-email-for-validation", methods={"GET"})
     *
     * @return Response
     */
    public function sendEmailForValidation(User $user)
    {
        try {
            $this->announcementUserEmailService->sendNotificationForRecoverPassword($user);

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'message' => 'Email enviado',
                'data' => 'Email enviado',
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => 'No se pudo enviar el email',
                'data' => 'An error has occurred trying to send the email - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/user/change-language-local", name="admin-change-user-locale",methods={"POST"})
     */
    public function changeLanguageLocale(Request $request, EntityManagerInterface $em): Response
    {
        $user = $this->getUser();
        if (!$user) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'error' => true,
                'data' => 'Unauthorized',
            ]);
        }

        try {
            $requestData = json_decode($request->getContent(), true);
            $language = $requestData['language'] ?? $this->getUser()->getLocale() ?? $this->settings->get('app.adminDefaultLanguage');

            $user = $this->getUser();
            $locale = $user->getLocale() ?? $this->settings->get('app.adminDefaultLanguage');

            $user->setLocale($language);
            $em->persist($user);
            $em->flush();

            $this->requestStack->getSession()->set('_locale', $language);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'message' => $this->translator->trans('email.messages_to_user.modified_information', [], 'email', $locale),
                    'user' => [
                        'locale' => $user->getLocale(),
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $this->translator->trans('email.messages_to_user.error_change_information', [], 'email', $locale) . ' {' . $e->getMessage() . '}',
            ]);
        }
    }

    /**
     * @Route("/admin/user/change-language-local-campus", name="admin-change-user-locale-campus",methods={"PUT"})
     */
    public function changeLanguageLocaleCampus(Request $request, EntityManagerInterface $em): Response
    {
        $user = $this->getUser();
        if (!$user) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'error' => true,
                'data' => 'Unauthorized',
            ]);
        }

        try {
            $requestData = json_decode($request->getContent(), true);
            $language = $requestData['language'];
            $user = $this->em->getRepository(User::class)->find($this->getUser()->getId());

            $user->setLocaleCampus($language);
            $em->persist($user);
            $em->flush();

            /* $this->requestStack->getSession()->set('_locale', $language); */

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'user' => [
                        'locale' => $user->getLocale(),
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
            ]);
        }
    }

    private function evalRols(array $rolesChoices, string $pageName): array
    {
        if (Crud::PAGE_EDIT === $pageName) {
            $entityId = $this->adminContextProvider->getContext()->getEntity()->getPrimaryKeyValue();
            if (!$entityId) {
                return $rolesChoices;
            }

            /** @var $user User */
            $user = $this->em->getRepository(User::class)->findOneBy(['id' => $entityId]);
            if (!$user || $user->getId() !== $this->getUser()->getId()) {
                return $rolesChoices;
            }

            foreach ($user->getRoles() as $role) {
                $rolesChoices[$role] = User::ROLES[$role];
            }
        }

        return $rolesChoices;
    }

    private function getLocalesLans(array $lenguajes): array
    {
        $flipped = array_flip($lenguajes);
        foreach ($flipped as $key => $value) {
            $name = ucfirst(Locales::getName($key, $key));
            unset($lenguajes[$value]);
            $lenguajes[$name] = $key;
        }

        return $lenguajes;
    }
}
