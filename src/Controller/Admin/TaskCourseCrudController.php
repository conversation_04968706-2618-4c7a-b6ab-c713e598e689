<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Announcement;
use App\Entity\CommentTask;
use App\Entity\Course;
use App\Entity\FilesHistoryTask;
use App\Entity\FilesTask;
use App\Entity\HistoryDeliveryTask;
use App\Entity\TaskCourse;
use App\Entity\TaskCourseGroup;
use App\Entity\TaskUser;
use App\Entity\User;
use App\Service\FileService;
use App\Service\SettingsService;
use App\Service\Vimeo\VimeoService;
use App\Utils\TimeZoneConverter\TimeZoneConverter;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextEditorField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Vich\UploaderBundle\Handler\DownloadHandler;
use Vimeo\Vimeo;

class TaskCourseCrudController extends AbstractCrudController
{
    use SerializerTrait;

    private $em;
    private $context;
    private $logger;
    private $jwt;
    protected $translator;
    private $router;
    private $requestStack;
    private $adminUrlGenerator;
    protected SettingsService $settings;
    protected FileService $fileService;

    public function __construct(
        EntityManagerInterface $em,
        RouterInterface $router,
        RequestStack $requestStack,
        AdminContextProvider $context,
        LoggerInterface $logger,
        JWTManager $jwt,
        TranslatorInterface $translator,
        AdminUrlGenerator $adminUrlGenerator,
        SettingsService $settings,
        FileService $fileService,
        private readonly AnnouncementAuthorizationService $announcementAuthorizationService,
    ) {
        $this->em = $em;
        $this->context = $context;
        $this->logger = $logger;
        $this->jwt = $jwt;
        $this->translator = $translator;
        $this->router = $router;
        $this->requestStack = $requestStack;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->settings = $settings;
        $this->fileService = $fileService;
    }

    public static function getEntityFqcn(): string
    {
        return TaskCourse::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular($this->translator->trans('taskCourse.labelInSingular'))
            ->setEntityLabelInPlural($this->translator->trans('taskCourse.labelInPlural'))
            ->overrideTemplate('crud/detail', 'admin/course/templates/task/detail.html.twig')
            ->setSearchFields(['name']);
    }

    public function configureFields(string $pageName): iterable
    {
        $title = TextField::new('title')
            ->setLabel($this->translator->trans('content.configureFields.title'))
            ->setColumns('col-md-6 col-xl-6');

        $description = TextEditorField::new('description')
            ->setLabel($this->translator->trans('Description'))
            ->setColumns('col-md-12 col-xl-12');

        if ($this->requestStack->getCurrentRequest()->get('announcementId')) {
            $announcement = $this->em->getRepository(Announcement::class)->find($this->requestStack->getCurrentRequest()->get('announcementId'));
            $startAnnouncement = $announcement->getStartAt();
            $finishAnnouncement = $announcement->getFinishAt();

            $startDate = DateTimeField::new('startDate', $this->translator->trans('taskCourse.configureFields.startDate'))
                ->setColumns('col-xl-2 col-lg-2 col-md-2')
                ->setFormTypeOptions([
                    'attr' => [
                        'min' => date_format($startAnnouncement, 'Y-m-d') . 'T00:00',
                        'max' => date_format($finishAnnouncement, 'Y-m-d') . 'T00:00',
                        'required' => true,
                    ],
                ]);

            $dateDeliveryAnnouncement = DateTimeField::new('dateDeliveryAnnouncement', $this->translator->trans('taskCourse.configureFields.dateDelivery'))
                ->setColumns('col-xl-2 col-lg-2 col-md-2')
                ->setFormTypeOptions([
                    'attr' => [
                        'min' => date_format($startAnnouncement, 'Y-m-d') . 'T00:00',
                        'max' => date_format($finishAnnouncement, 'Y-m-d') . 'T00:00',
                        'required' => true,
                    ],
                ]);

            $visible = BooleanField::new('isVisible')
                ->setLabel($this->translator->trans('taskCourse.configureFields.visible'))
                ->setColumns('col-md-2 col-xl-2');
        }

        $field = [];

        if (Crud::PAGE_INDEX === $pageName) {
            $field = [$description];
        } elseif (\in_array($pageName, [Crud::PAGE_NEW, Crud::PAGE_EDIT])) {
            if ($this->requestStack->getCurrentRequest()->get('announcementId')) {
                $field = [$title, $startDate, $dateDeliveryAnnouncement, $visible, $description];
            } else {
                $field = [$title, $description];
            }
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            $field = [$description];
        }

        return $field;
    }

    public function createEntity(string $entityFqc)
    {
        if ($this->requestStack->getCurrentRequest()->get('courseId')) {
            $taskCourse = new TaskCourse();
            $courseRepository = $this->em->getRepository(Course::class);
            $course = $courseRepository->find($this->requestStack->getCurrentRequest()->get('courseId'));
            $taskCourse->setCourse($course);

            return $taskCourse;
        } elseif ($this->requestStack->getCurrentRequest()->get('announcementId')) {
            $taskCourse = new TaskCourse();
            $announcementRepository = $this->em->getRepository(Announcement::class);
            $announcement = $announcementRepository->find($this->requestStack->getCurrentRequest()->get('announcementId'));
            $taskCourse->setCourse($announcement->getCourse());
            $taskCourse->setAnnouncement($announcement);

            return $taskCourse;
        }
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName')) {
            $taskRepository = $this->em->getRepository(TaskCourse::class);
            $entity = $this->context->getContext()->getEntity();
            $task = $taskRepository->find($entity->getPrimaryKeyValue());
            $responseParameters->set('task', $task);
        }

        return $responseParameters;
    }

    public function configureActions(Actions $actions): Actions
    {
        $actions->remove(Crud::PAGE_DETAIL, Action::INDEX);

        return $actions;
    }

    public function delete(AdminContext $context)
    {
        $entity = $context->getEntity();

        $taskCourseRepository = $this->em->getRepository(TaskCourse::class);
        $taskCourse = $taskCourseRepository->find($entity->getPrimaryKeyValue());

        $filesTask = $this->em->getRepository(FilesTask::class)->findBy(['taskCourse' => $taskCourse]);

        if ($filesTask) {
            foreach ($filesTask as $file) {
                $this->deleteMaterialFile($file->getFilename());
                $this->em->remove($file);
            }

            $this->em->flush();
        }

        $responseParameters = parent::delete($context);

        return $responseParameters;
    }

    /**
     * @Route("/admin/new/task-course", name="new-file-task",methods={"POST"})
     *
     * @return Response
     */
    public function newFileTask(Request $request)
    {
        try {
            $idTask = $request->get('idTask');
            $typeMaterial = $request->get('typeMaterial');
            $count_files = $request->get('count-files');

            $taskCourse = $this->em->getRepository(TaskCourse::class)->find($idTask);

            if (2 != $typeMaterial) {
                for ($i = 0; $i < $count_files; ++$i) {
                    $fileTaskCourse = new FilesTask();
                    $file = $request->files->get('file' . $i);
                    $fileTaskCourse->setFilename($file->getClientOriginalName());
                    $fileTaskCourse->setFilenameFile($file);
                    $fileTaskCourse->setTaskCourse($taskCourse);
                    $fileTaskCourse->setTypeMaterial($typeMaterial);
                    $this->em->persist($fileTaskCourse);
                }
            }

            $this->em->flush();

            if (2 == $typeMaterial) {
                $file = $request->files->get('file' . 0);
                $this->uploadVideoToVimeo($file, $taskCourse);
            }

            $filesTask = $this->em->getRepository(FilesTask::class)->findBy(['taskCourse' => $taskCourse]);
            $route = $this->redirectAfterAddMaterialFromCourse($taskCourse);

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'message' => $this->translator->trans('material_course.configureFields.save'),
                    'route' => $route,
                    'filesTask' => $filesTask,
                ],
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the video - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['fileable', 'detail']]);
    }

    private function uploadVideoToVimeo($file, TaskCourse $taskCourse)
    {
        try {
            $client = $this->dataClientVimeo();

            $response_video = '';

            if ($file) {
                $response_video = $client->upload($file, [
                    'name' => $file->getClientOriginalName(),
                    'privacy' => [
                        'embed' => 'whitelist',
                    ],
                ]);

                $linkVimeo = $client->request($response_video . '?fields=link');
                $user_id = $this->settings->get('app.userIdVimeo');
                $project_id = $this->settings->get('app.projectIdTaskCourse'); // Es el identificador de la carpeta
                $video_id = substr($response_video, 7); // Id del video que se esta subiendo

                $client->request("/users/$user_id/projects/$project_id/videos/$video_id", [], 'PUT');
            }

            if ($response_video) {
                $fileTask = new FilesTask();
                $fileTask->setFilename($file->getClientOriginalName());
                $fileTask->setOriginalName($file->getClientOriginalName());
                $fileTask->setTaskCourse($taskCourse);
                $fileTask->setMimeType($file->getClientmimeType());
                $fileTask->setTypeMaterial(2);
                $fileTask->setUrlMaterial($linkVimeo['body']['link']);

                $this->em->persist($fileTask);
            }

            $this->em->flush();

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $this->translator->trans('material_course.configureFields.save'),
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the video - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }

    private function dataClientVimeo()
    {
        $client_id = $this->settings->get('app.clientIdVimeo');
        $client_secret = $this->settings->get('app.clientSecretVimeo');
        $access_token = $this->settings->get('app.accessTokenVimeo');

        $client = new Vimeo($client_id, $client_secret, $access_token);

        return $client;
    }

    private function redirectAfterAddMaterialFromCourse(TaskCourse $taskCourse)
    {
        return $this->adminUrlGenerator
            ->unsetAll()
            ->setController(TaskCourseCrudController::class)
            ->setAction('detail')
            ->setEntityId($taskCourse->getId())
            ->generateUrl();
    }

    /**
     * @Route("/admin/fetch-files-task/{taskCourse}", name="fetch-file-task",methods={"GET"})
     *
     * @return Response
     */
    public function fetchFilesTask(TaskCourse $taskCourse)
    {
        try {
            $filesTask = $this->em->getRepository(FilesTask::class)->findBy(['taskCourse' => $taskCourse]);

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $filesTask,
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the video - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['fileable', 'detail']]);
    }

    /**
     * @Route("/admin/update-file-task", name="update-file-task",methods={"POST"})
     *
     * @return Response
     */
    public function allowDownloadTaskFile(Request $request)
    {
        try {
            $requesData = json_decode($request->getContent(), true);
            $idFile = $requesData['idFile'];
            $isDownload = $requesData['isDownload'];
            $fileTask = $this->em->getRepository(FilesTask::class)->find($idFile);

            if ($fileTask) {
                $fileTask->setIsDownload($isDownload);
                $this->em->persist($fileTask);
            }
            $this->em->flush();

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'message' => $this->translator->trans('material_course.configureFields.save'),
                    'fileTask' => $fileTask,
                ],
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to upate the material course - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['fileable', 'detail']]);
    }

    /**
     * @Route("/admin/delete-file-task", name="delete-file-task",methods={"POST"})
     *
     * @return Response
     */
    public function deleteMaterialCourse(Request $request)
    {
        try {
            $requesData = json_decode($request->getContent(), true);
            $idFile = $requesData['idFile'];
            $idTask = $requesData['idTask'];

            $TaskFileCourse = $this->em->getRepository(FilesTask::class)->find($idFile);
            $taskCourse = $this->em->getRepository(TaskCourse::class)->find($idTask);

            if ('2' != $TaskFileCourse->getTypeMaterial()) {
                $this->deleteMaterialFile($TaskFileCourse->getFilename());
                $this->em->remove($TaskFileCourse);
                $this->em->flush();
            }

            if (2 == $TaskFileCourse->getTypeMaterial()) {
                $this->deleteVideoFromVimeo($TaskFileCourse);
            }

            $route = $this->redirectAfterAddMaterialFromCourse($taskCourse);

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'message' => $this->translator->trans('material_course.configureFields.save'),
                    'route' => $route,
                ],
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the video - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['material', 'fileable', 'detail']]);
    }

    private function deleteVideoFromVimeo(FilesTask $fileTask)
    {
        try {
            $client = $this->dataClientVimeo();

            $user_id = $this->settings->get('app.userIdVimeo');
            $project_id = $this->settings->get('app.projectIdTaskCourse'); // Es el identificador de la carpeta

            $identifierVideo = $fileTask->getIdentifierVideo();
            $client->request("/users/$user_id/projects/$project_id/videos/$identifierVideo", [], 'DELETE');

            $this->em->remove($fileTask);
            $this->em->flush();

            $response = [
                'status' => 200,
                'error' => false,
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to delete the video - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }

    private function deleteMaterialFile($file)
    {
        $filename = $this->settings->get('app.task_course_path') . $file;

        if (file_exists($filename)) {
            $success = unlink($filename);

            if (!$success) {
                throw $this->createNotFoundException("Cannot delete $filename");
            }
        }
    }

    /**
     * @Route("/admin/history-task/{taskCourse}", name="history-task",methods={"GET"})
     *
     * @return Response
     */
    public function historyTask(TaskCourse $taskCourse)
    {
        try {
            $taskUser = $this->em->getRepository(TaskUser::class)->findBy(['task' => $taskCourse]);
            $history = [];
            foreach ($taskUser as $task) {
                $taskByUser = $this->em->getRepository(TaskUser::class)->findOneBy(['task' => $taskCourse, 'user' => $task->getUser()]);
                $historyTask = $this->em->getRepository(HistoryDeliveryTask::class)->getHistoryDeliveryTask($taskByUser);

                if ($historyTask) {
                    $history[] = [
                        'id' => $task->getId(),
                        'idUser' => $task->getUser()->getId(),
                        'fullName' => $task->getUser()->getFullName(),
                        'detailUser' => $this->getLinkUser($task->getUser()->getId()),
                        'avatar' => $task->getUser()->getAvatarImage(),
                        'created' => $task->getCreatedAt(),
                        'updated' => $task->getUpdatedAt(),
                        'state' => $historyTask ? $historyTask[0]->getState() : 0,
                        'historyDelivery' => $historyTask,
                    ];
                }
            }

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $history,
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to get history task - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['detail', 'fileable', 'messages', 'user_area']]);
    }

    private function getLinkUser($idUser)
    {
        return $this->adminUrlGenerator
            ->unsetAll()
            ->setController(UserCrudController::class)
            ->setAction('detail')
            ->setEntityId($idUser)
            ->generateUrl();
    }

    /**
     * @Route("/admin/file-taskuser/{id}", name="admin-taskuser",methods={"GET"})
     *
     * @return Response
     */
    public function downloadFileTaskUser(FilesHistoryTask $fileTask, DownloadHandler $downloadHandler)
    {
        $status = Response::HTTP_NOT_FOUND;
        $error = true;
        $data = 'Attachment not allowed';

        if ($fileTask) {
            return $downloadHandler->downloadObject($fileTask, 'filenameFile', null, $fileTask->getFilename());
        }

        $response = [
            'status' => $status,
            'error' => $error,
            'message' => $data,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/comment-task", name="admin-comment-task",methods={"POST"})
     *
     * @return Response
     */
    public function respondTaskTeacher(Request $request)
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;
            $message = $this->translator->trans('taskCourse.configureFields.senTask');
            $requesData = json_decode($request->getContent(), true);

            $idHistory = $requesData['idHistory'];
            $historyDeliveryTask = $this->em->getRepository(HistoryDeliveryTask::class)->find($idHistory);

            if ($historyDeliveryTask) {
                if (isset($requesData['comment'])) {
                    $commentTask = new CommentTask();
                    $commentTask->setHistoryDeliveryTask($historyDeliveryTask);
                    $commentTask->setComment($requesData['comment']);
                    $this->em->persist($commentTask);
                    $message = $this->translator->trans('taskCourse.configureFields.sendComment');
                } elseif (isset($requesData['state'])) {
                    $historyDeliveryTask->setState($requesData['state']);
                    $this->em->persist($historyDeliveryTask);
                    $message = $this->translator->trans('taskCourse.configureFields.stateTask');
                }
            }

            $this->em->flush();
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = "An error has occurred trying to save task user: {$e->getMessage()}";
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => [
                'message' => $message,
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/task-by-user/{user}/announcement/{announcement}", name="task-by-user",methods={"GET"})
     *
     * @return Response
     */
    public function taskByUser(User $user, Announcement $announcement)
    {
        try {
            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $this->fetchTaskAnnouncement($user->getId(), $announcement),
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to get history task - Error: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['detail', 'fileable', 'messages', 'user_area']]);
    }

    private function fetchTaskAnnouncement($user, Announcement $announcement)
    {
        $taskAnnouncement = $this->em->getRepository(TaskCourse::class)
            ->findBy(
                [
                    'announcement' => $announcement->getId(),
                    'isVisible' => true,
                ],
                ['dateDeliveryAnnouncement' => 'asc']
            );

        $taskCourse = $this->em->getRepository(TaskCourse::class)->findBy([
            'course' => $announcement->getCourse(), 'announcement' => null,
            'isVisible' => true,
        ]);

        $task = array_merge($taskCourse, $taskAnnouncement);
        $taskAll = [];

        foreach ($task as $t) {
            $taskUser = $this->em->getRepository(TaskUser::class)->findBy(['task' => $t, 'user' => $user]);
            $historyTask = $this->em->getRepository(HistoryDeliveryTask::class)->findOneBy(['taskUser' => $taskUser]);

            $taskAll[] =
                [
                    'task' => $t,
                    'historyTask' => $taskUser,
                    'lastHistory' => [
                        'id' => $historyTask ? \intval($historyTask->getId()) : '',
                        'state' => $historyTask ? \intval($historyTask->getState()) : 0,
                        'updatedAt' => $historyTask ? $historyTask->getUpdatedAt() : '',
                    ],
                ];
        }

        return $taskAll;
    }

    public function detailTaskSubsidizer(AdminContext $context)
    {
        $taskRepository = $this->em->getRepository(TaskCourse::class);
        $task = $taskRepository->find($this->requestStack->getCurrentRequest()->get('task_id'));

        return $this->render('admin/subsidizer/task/detail.html.twig', [
            'task' => $task,
        ]);
    }

    /**
     * New Methods.
     */

    /**
     * @Rest\Get("/admin/task-course/history-delivery-states")
     */
    public function getHistoryDeliveryTaskStates(): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                1 => 'HISTORY_DELIVERY_TASK.DELIVERED',
                2 => 'HISTORY_DELIVERY_TASK.REVISION',
                3 => 'HISTORY_DELIVERY_TASK.REJECTED',
                4 => 'HISTORY_DELIVERY_TASK.APPROVED',
            ],
        ]);
    }

    /**
     * @Rest\Get("/admin/task-courses/allowed-file-types", name="get_task_course_allowed_file_tupes")
     */
    public function getTaskCourseAllowedFileTypes(): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                [
                    'type' => FilesTask::FILE_TYPE_PDF,
                    'name' => 'PDF',
                    'multiple' => true,
                    'accept' => 'application/pdf',
                ],
                [
                    'type' => FilesTask::FILE_TYPE_VIDEO,
                    'name' => 'Video',
                    'multiple' => false,
                    'accept' => 'video/*',
                ],
                [
                    'type' => FilesTask::FILE_TYPE_COMPRESSED,
                    'name' => 'Compressed',
                    'multiple' => true,
                    'accept' => '.zip, .rar',
                ],
                [
                    'type' => FilesTask::FILE_TYPE_IMAGE,
                    'name' => 'Image',
                    'multiple' => true,
                    'accept' => 'image/*',
                ],
                [
                    'type' => FilesTask::FILE_TYPE_OFFICE_SUITE,
                    'name' => 'Office Suite',
                    'multiple' => true,
                    'accept' => '.doc, .docx, .docx, .docm, .dot, .dotx, .odt, dotm, .cvs, .dbf, .xla, .xls, .xlsb, .xlsm, .xlsx, .pptx, .pptm, .ppt, .mpt, .mpx, .mpp, .vsdx, .vsdm, .vssx, .vssm, .vstx, .vstm, .accdb',
                ],
                [
                    'type' => FilesTask::FILE_TYPE_TXT,
                    'name' => 'TXT',
                    'multiple' => true,
                    'accept' => '.txt',
                ],
            ],
        ]);
    }

    /**
     * @Rest\Put("/admin/history-delivery-task/{id}/state/{state}", requirements={"state"="\d+"})
     */
    public function updateHistoryDeliveryTaskState(HistoryDeliveryTask $historyDeliveryTask, int $state): Response
    {
        $historyDeliveryTask->setState($state);
        $this->em->flush($historyDeliveryTask);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'Updated',
        ]);
    }

    /**
     * @Rest\Get("/admin/task-course/{id}")
     */
    public function getTaskCourse(TaskCourse $task): Response
    {
        // TODO: Implement
        $groups = [];
        $groupsTask = $this->em->getRepository(TaskCourseGroup::class)->findBy(['taskCourse' => $task]);
        $announcement = $task->getAnnouncement();

        foreach ($groupsTask as $group) {
            $groupName = $this->generateGroupName($group->getNumGroup());
            $groups[] = [
                'id' => $group->getAnnouncementGroup()->getId(),
                'name' => $groupName,
                'numGroup' => $group->getNumGroup(),
            ];
        }

        $startDate = $task->getStartDate();
        $dateDelivery = $task->getDateDelivery();
        $data = [
            'title' => $task->getTitle(),
            'description' => $task->getDescription(),
            'startAt' => $startDate ? $startDate->format('c') : null,
            'deadline' => $dateDelivery ? $dateDelivery->format('c') : null,
            'visible' => $task->isIsVisible(),
            'groups' => $groups,
            'timezone' => $announcement ? $announcement->getTimezone() : null,
        ];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => TimeZoneConverter::checkTimezone($data),
        ]);
    }

    private function generateGroupName(int $numGroup): string
    {
        return $this->translator->trans('announcements.common.group', [], 'messages') . ' ' . $numGroup;
    }

    /**
     * @Rest\Get("/admin/task-course/{id}/files")
     */
    public function getTaskCourseFiles(TaskCourse $taskCourse): Response
    {
        $taskFiles = $this->em->getRepository(FilesTask::class)
            ->createQueryBuilder('ft')
            ->select('ft.id', 'ft.filename', 'ft.typeMaterial as type', 'ft.originalName', 'ft.mimeType', 'ft.createdAt', 'ft.urlMaterial as url', 'ft.isDownload')
            ->where('ft.taskCourse = :taskCourse')
            ->setParameter('taskCourse', $taskCourse)
            ->getQuery()
            ->getResult();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $taskFiles,
            'basePath' => $this->settings->get('app.task_course_path'),
        ]);
    }

    /**
     * @Rest\Get("/admin/task-course/{id}/history/{page}", requirements={"page"="\d+"})
     */
    public function getTaskCourseHistory(TaskCourse $taskCourse, int $page = 1): Response
    {
        $historyDeliveryTasks = $this->em->getRepository(TaskUser::class)
            ->createQueryBuilder('tu')
            ->join('tu.historyDeliveryTasks', 'hdt')
            ->join('tu.user', 'u')
            ->where('tu.task =:task')
            ->setParameter('task', $taskCourse);

        $pageSize = 10;
        $countQuery = clone $historyDeliveryTasks;
        $totalItems = $countQuery->select('COUNT(hdt.id) as total')->getQuery()->getSingleScalarResult();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'items' => $historyDeliveryTasks
                    ->select(
                        'u.id as userId',
                        'u.firstName',
                        'u.lastName',
                        'u.avatar',
                        'tu.id AS taskUserId',
                        'tu.createdAt',
                        'IFNULL(hdt.state, 0) AS state',
                        'hdt.id as historyDeliveryTaskId'
                    )
                    ->setFirstResult(($page - 1) * $pageSize)
                    ->setMaxResults($pageSize)
                    ->getQuery()
                    ->getResult(),
                'total-items' => (int) $totalItems,
                'pageSize' => $pageSize,
            ],
        ]);
    }

    /**
     * @Rest\Get("/admin/task-course/history-delivery/{id}/files")
     */
    public function getTaskCourseHistoryDeliveryFiles(HistoryDeliveryTask $historyDeliveryTask): Response
    {
        $historyFiles = $this->em->getRepository(FilesHistoryTask::class)
            ->createQueryBuilder('fht')
            ->select('fht.id', 'fht.originalName', 'fht.filename', 'fht.mimeType', 'fht.createdAt')
            ->where('fht.historyDeliveryTask =:task')
            ->setParameter('task', $historyDeliveryTask)
            ->getQuery()->getResult();
        $files = $historyDeliveryTask->getFilesHistoryTasks();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $historyFiles,
        ]);
    }

    /**
     * @Rest\Get("/admin/task-course/history-delivery/{id}/comments")
     */
    public function getTaskCourseHistoryDeliveryComments(HistoryDeliveryTask $historyDeliveryTask): Response
    {
        $commentTasks = $this->em->getRepository(CommentTask::class)
            ->createQueryBuilder('ct')
            ->select('u.id AS user_id', 'p.id as parent_id', 'u.firstName', 'u.lastName', 'u.avatar', 'u.email', 'ct.id', 'ct.comment as message', 'ct.createdAt')
            ->join('ct.createdBy', 'u')
            ->leftJoin('ct.parent', 'p')
            ->where('ct.historyDeliveryTask =:task')
            ->setParameter('task', $historyDeliveryTask)
            ->orderBy('ct.createdAt', 'ASC')
            ->getQuery()
            ->getResult();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $commentTasks,
        ]);
    }

    /**
     * @Rest\Post("/admin/task-course/history-delivery/{id}/comment")
     */
    public function sendMessageHistoryDeliveryTask(Request $request, HistoryDeliveryTask $historyDeliveryTask): Response
    {
        try {
            $comment = new CommentTask();
            $message = $request->get('message');
            $replyTo = $request->get('replyTo');
            if (empty($message)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'No message provided',
                ]);
            }
            $comment->setHistoryDeliveryTask($historyDeliveryTask)
                ->setComment($message);
            if (!empty($replyTo)) {
                $comment->setParent($this->em->getRepository(CommentTask::class)->find($replyTo));
            }

            $this->em->persist($comment);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false,
                'data' => $this->em->getRepository(CommentTask::class)
                    ->createQueryBuilder('ct')
                    ->select('u.id AS user_id', 'p.id as parent_id', 'u.firstName', 'u.lastName', 'u.avatar', 'u.email', 'ct.id', 'ct.comment as message', 'ct.createdAt')
                    ->join('ct.createdBy', 'u')
                    ->leftJoin('ct.parent', 'p')
                    ->where('ct.id =:id')
                    ->setParameter('id', $comment->getId())
                    ->orderBy('ct.createdAt', 'ASC')
                    ->getQuery()
                    ->getSingleResult(),
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Post("/admin/task-course/{id}/upload-files")
     */
    public function uploadTaskCourseFiles(Request $request, TaskCourse $taskCourse, VimeoService $uploadToVimeo): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageTask(user: $user, taskCourse: $taskCourse);

        try {
            $filesLength = $request->get('filesLength'); // When multiple files are uploaded, add this property
            $type = $request->get('type');
            if (2 === $type) {
                // Is a video file
                /** @var UploadedFile|null $file */
                $file = $request->files->get('file');
                if (!$file) {
                    return $this->sendResponse([
                        'status' => Response::HTTP_OK,
                        'error' => true,
                        'data' => 'FILE_UPLOAD.FILE.NOT_PROVIDED',
                    ]);
                }

                $result = $uploadToVimeo->upload($file);
                if (Response::HTTP_OK !== $result['status']) {
                    return $this->sendResponse(array_merge($result, [
                        'data' => 'FILE_UPLOAD.VIDEO.FAILED',
                    ]));
                }

                $fileTask = new FilesTask();
                $fileTask->setFilename($file->getClientOriginalName())
                    ->setFilenameFile($file)
                    ->setTaskCourse($taskCourse)
                    ->setTypeMaterial($type)
                    ->setUrlMaterial($result['link']);
                $this->em->persist($fileTask);
            } else {
                if (!empty($filesLength) && $filesLength > 1) {
                    for ($i = 0; $i < $filesLength; ++$i) {
                        $file = $request->files->get("file_$i");
                        $this->saveFilesTaskFile($taskCourse, $file, $type);
                    }
                } else {
                    $file = $request->files->get('file');
                    if (!$file) {
                        return $this->sendResponse([
                            'status' => Response::HTTP_OK,
                            'error' => true,
                            'data' => 'FILE_UPLOAD.FILE.NOT_PROVIDED',
                        ]);
                    }
                    $this->saveFilesTaskFile($taskCourse, $file, $type);
                }
            }

            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'Uploaded',
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @return void
     */
    private function saveFilesTaskFile(TaskCourse $taskCourse, UploadedFile $file, $type)
    {
        $fileTask = new FilesTask();
        $fileTask->setFilename($file->getClientOriginalName())
            ->setFilenameFile($file)
            ->setTaskCourse($taskCourse)
            ->setTypeMaterial($type);
        $this->em->persist($fileTask);
    }

    /**
     * @Rest\Delete("/admin/file-task/{id}", requirements={"id"="\d+"})
     */
    public function deleteFilesTask(FilesTask $filesTask, VimeoService $vimeoService): Response
    {
        try {
            /** @var User $user */
            $user = $this->getUser();
            $taskCourse = $filesTask->getTaskCourse();
            $this->announcementAuthorizationService->ensureUserCanManageTask(user: $user, taskCourse: $taskCourse);
            if (2 === (int) $filesTask->getTypeMaterial() && !empty($filesTask->getIdentifierVideo())) {
                $vimeoService->deleteFromVimeo($filesTask->getIdentifierVideo());
            } else {
                $fullPath = $this->settings->get('app.task_course_path') . '/' . $filesTask->getFilename();
                if (!file_exists($fullPath)) {
                    return $this->sendResponse([
                        'status' => Response::HTTP_NOT_FOUND,
                        'error' => true,
                        'data' => 'File not exist',
                    ]);
                }
                if (!$this->fileService->deleteFile($fullPath)) {
                    return $this->sendResponse([
                        'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                        'error' => true,
                        'data' => 'Failed to delete file',
                    ]);
                }
            }
            $this->em->remove($filesTask);
            $this->em->flush();

            return new JsonResponse(null, Response::HTTP_NO_CONTENT);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Delete("/admin/task-course/{id}", requirements={"id"="\d+"})
     */
    public function deleteTaskCourse(TaskCourse $taskCourse): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageTask(user: $user, taskCourse: $taskCourse);

        $this->em->remove($taskCourse);
        $this->em->flush();

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * @Rest\Patch("/admin/file-task/{id}/downloadable", requirements={"id"="\d+"})
     */
    public function setFileTaskDownloadable(Request $request, FilesTask $file): Response
    {
        try {
            /** @var User $user */
            $user = $this->getUser();
            $taskCourse = $file->getTaskCourse();
            $this->announcementAuthorizationService->ensureUserCanManageTask(user: $user, taskCourse: $taskCourse);
            $content = json_decode($request->getContent(), true);
            $file->setIsDownload($content['downloadable'] ?? false);
            $this->em->persist($file);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Patch("/admin/task-course/{task}/visible", name="admin_api_set_task_course_visible", requirements={"task"="\d+"})
     */
    public function activateTaskCourse(Request $request, TaskCourse $task): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageTask(user: $user, taskCourse: $task);

        try {
            $content = json_decode($request->getContent(), true);
            $task->setIsVisible($content['visible'] ?? false);
            $this->em->persist($task);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }
}
