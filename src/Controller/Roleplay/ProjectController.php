<?php

declare(strict_types=1);

namespace App\Controller\Roleplay;

use App\Entity\RoleplayBeginning;
use App\Entity\RoleplayEnding;
use App\Entity\RoleplayProject;
use App\Entity\RoleplayScene;
use App\Entity\RoleplaySequence;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ProjectController extends BaseController
{
    /**
     * @Route("/admin/roleplay/project/{id}", name="roleplay_project_detail", methods={"GET"})
     */
    public function detail($id): Response
    {
        $project = $this->em->find(RoleplayProject::class, $id);

        $locale = null;
        if ($this->getUser() && $this->getUser()->getLocale()) {
            $locale = $this->getUser()->getLocale();
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'locale' => $locale,
                'project' => $project,
            ],
        ];

        return $this->sendResponse($response, [
            'groups' => ['project:detail'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
        ]);
    }

    /**
     * @Route("/admin/roleplay/project/{id}/save", name="roleplay_project_save", methods={"POST"})
     */
    public function save(Request $request): Response
    {
        try {
            $content = json_decode($request->getContent(), true);

            if (!$content || !isset($content['project'])) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'message' => 'Datos del proyecto no válidos',
                ]);
            }

            $projectData = $content['project'];

            // Validar y procesar beginnings
            if (isset($projectData['beginnings']) && \is_array($projectData['beginnings'])) {
                foreach ($projectData['beginnings'] as $beginningData) {
                    if (!isset($beginningData['id'])) {
                        continue;
                    }

                    /**
                     * @var RoleplayBeginning $beginning
                     */
                    $beginning = $this->em->find(RoleplayBeginning::class, $beginningData['id']);
                    if ($beginning) {
                        $beginning
                            ->setBackground($beginningData['background'] ?? null)
                            ->setDescription($beginningData['description'] ?? null)
                            ->setOrder($beginningData['order'] ?? 0)
                            ->setType($beginningData['type'] ?? null)
                            ->setAttached($beginningData['attached'] ?? null)
                            ->setAttachedTag($beginningData['attachedTag'] ?? null);
                    }
                }
            }

            // Validar y procesar sequences
            if (isset($projectData['sequences']) && \is_array($projectData['sequences'])) {
                foreach ($projectData['sequences'] as $sequenceData) {
                    if (!isset($sequenceData['id'])) {
                        continue;
                    }

                    $sequence = $this->em->find(RoleplaySequence::class, $sequenceData['id']);
                    if ($sequence) {
                        $sequence
                            ->setOrder($sequenceData['order'] ?? 0)
                            ->setColor($sequenceData['color'] ?? null);

                        // Validar y procesar scenes
                        if (isset($sequenceData['scenes']) && \is_array($sequenceData['scenes'])) {
                            foreach ($sequenceData['scenes'] as $sceneData) {
                                if (!isset($sceneData['id'])) {
                                    continue;
                                }

                                /**
                                 * @var RoleplayScene $scene
                                 */
                                $scene = $this->em->find(RoleplayScene::class, $sceneData['id']);
                                if ($scene) {
                                    $scene
                                        ->setOrder($sceneData['order'] ?? 0)
                                        ->setAnswers($sceneData['answers'] ?? null)
                                        ->setAttached(!empty($sceneData['attached']) ? $sceneData['attached'] : null)
                                        ->setAttachedTag(!empty($sceneData['attachedTag']) ? $sceneData['attachedTag'] : null)
                                        ->setAvatar(\array_key_exists('avatar', $sceneData) ? $sceneData['avatar'] : null)
                                        ->setBackground(\array_key_exists('background', $sceneData) ? $sceneData['background'] : null)
                                        ->setStandby(!empty($sceneData['standby']) ? $sceneData['standby'] : null)
                                        ->setStatement(\array_key_exists('statement', $sceneData) ? $sceneData['statement'] : null)
                                        ->setType($sceneData['type'] ?? null)
                                        ->setVideo(!empty($sceneData['video']) ? $sceneData['video'] : null);
                                }
                            }
                        }
                    }
                }
            }

            // Validar y procesar endings
            if (isset($projectData['endings']) && \is_array($projectData['endings'])) {
                foreach ($projectData['endings'] as $endingData) {
                    if (!isset($endingData['id'])) {
                        continue;
                    }

                    /**
                     * @var RoleplayEnding $ending
                     */
                    $ending = $this->em->find(RoleplayEnding::class, $endingData['id']);
                    if ($ending) {
                        $ending
                            ->setBackground($endingData['background'] ?? null)
                            ->setDescription(!empty($endingData['description']) ? $endingData['description'] : null)
                            ->setSuspendedDescription(!empty($endingData['suspendedDescription']) ? $endingData['suspendedDescription'] : null)
                            ->setOrder($endingData['order'] ?? 0)
                            ->setType($endingData['type'] ?? null)
                            ->setApprovalByPoints(!empty($endingData['approvalByPoints']) ? $endingData['approvalByPoints'] : null)
                            ->setApprovalByHistory($endingData['approvalByHistory'] ?? false)
                            ->setApprovalType($endingData['approvalType'] ?? null)
                            ->setStatement(!empty($endingData['statement']) ? $endingData['statement'] : null);
                    }
                }
            }

            $this->em->flush();

            if (!isset($projectData['id'])) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'message' => 'ID del proyecto no válido',
                ]);
            }

            $project = $this->em->find(RoleplayProject::class, $projectData['id']);

            $locale = null;
            if ($this->getUser() && $this->getUser()->getLocale()) {
                $locale = $this->getUser()->getLocale();
            }

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'locale' => $locale,
                    'project' => $project,
                ],
            ];

            return $this->sendResponse($response, [
                'groups' => ['RoleplayProject:detail'],
                'circular_reference_handler' => function ($object) {
                    return $object->getId();
                },
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => 'Error al guardar el proyecto: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route("/api/roleplay/project/{id}/visor", name="roleplay_project_visor", methods={"GET"})
     */
    public function visor($id): Response
    {
        $project = $this->em->find(RoleplayProject::class, $id);

        $locale = null;
        if ($this->getUser() && $this->getUser()->getLocale()) {
            $locale = $this->getUser()->getLocale();
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'locale' => $locale,
                'project' => $project,
            ],
        ];

        return $this->sendResponse($response, [
            'groups' => ['project:visor'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
        ]);
    }
}
