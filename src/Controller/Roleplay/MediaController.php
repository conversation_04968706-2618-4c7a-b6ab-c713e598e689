<?php

declare(strict_types=1);

namespace App\Controller\Roleplay;

use App\Service\SettingsService;
use App\Service\Vimeo\VimeoService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class MediaController extends BaseController
{
    public function __construct(
        EntityManagerInterface $entityManager,
        RequestStack $requestStack,
        readonly private VimeoService $vimeoService,
        readonly private SettingsService $settingsService
    ) {
        parent::__construct($entityManager, $requestStack);
    }

    /**
     * @Route("/admin/roleplay/images-background", name="roleplay_images_background", methods={"GET"})
     */
    public function imagesBackground(Request $request): Response
    {
        $baseUrl = $request->getScheme() . '://' . $request->getHttpHost() . $request->getBasePath();

        $path = $this->getParameter('kernel.project_dir')
            . DIRECTORY_SEPARATOR
            . 'public' . DIRECTORY_SEPARATOR;

        $cartoonFolder = $this->getParameter('roleplay_images_background_path')
            . 'cartoon';
        $photographicFolder = $this->getParameter('roleplay_images_background_path')
            . 'photographic';

        $cartoonPath = $path . $cartoonFolder;
        $photographicPath = $path . $photographicFolder;

        $cartoonFiles = [];
        $photographicFiles = [];

        if (is_dir($cartoonPath)) {
            $cartoonFiles = array_values(array_diff(scandir($cartoonPath), ['..', '.']));
            $cartoonFiles = array_map(function ($file) use ($cartoonFolder, $baseUrl) {
                return $baseUrl . '/' . $cartoonFolder . '/' . $file;
            }, $cartoonFiles);
        }

        if (is_dir($photographicPath)) {
            $photographicFiles = array_values(array_diff(scandir($photographicPath), ['..', '.']));
            $photographicFiles = array_map(function ($file) use ($photographicFolder, $baseUrl) {
                return $baseUrl . '/' . $photographicFolder . '/' . $file;
            }, $photographicFiles);
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'images' => [
                    'cartoon' => $cartoonFiles,
                    'photographic' => $photographicFiles,
                ],
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/roleplay/images-avatar", name="roleplay_images_avatar", methods={"GET"})
     */
    public function imagesAvatar(Request $request): Response
    {
        $path = $this->getParameter('kernel.project_dir')
            . DIRECTORY_SEPARATOR
            . 'public' . DIRECTORY_SEPARATOR;

        $cartoonFolder = $this->getParameter('roleplay_images_avatar_path')
            . 'cartoon';
        $photographicFolder = $this->getParameter('roleplay_images_avatar_path')
            . 'photographic';

        $cartoonPath = $path . $cartoonFolder;
        $photographicPath = $path . $photographicFolder;

        $cartoonFiles = [];
        $photographicFiles = [];

        if (is_dir($cartoonPath)) {
            $cartoonFiles = array_values(array_diff(scandir($cartoonPath), ['..', '.']));
            $cartoonFiles = array_map(function ($file) use ($cartoonFolder, $request) {
                return $request->getUriForPath(DIRECTORY_SEPARATOR . $cartoonFolder . DIRECTORY_SEPARATOR . $file);
            }, $cartoonFiles);
        }

        if (is_dir($photographicPath)) {
            $photographicFiles = array_values(array_diff(scandir($photographicPath), ['..', '.']));
            $photographicFiles = array_map(function ($file) use ($photographicFolder, $request) {
                return $request->getUriForPath(DIRECTORY_SEPARATOR . $photographicFolder . DIRECTORY_SEPARATOR . $file);
            }, $photographicFiles);
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'images' => [
                    'cartoon' => $cartoonFiles,
                    'photographic' => $photographicFiles,
                ],
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/roleplay/project/{id}/save-scene-file", name="roleplay_project_save_scene_file", methods={"POST"})
     */
    public function saveSceneFile(Request $request): Response
    {
        $year = date('Y');
        $month = date('m');

        $folder = $this->getParameter('roleplay_uploads_path')
            . $year . DIRECTORY_SEPARATOR
            . $month . DIRECTORY_SEPARATOR;

        $file = $request->files->get('file');

        $url = $this->saveFile($file, $folder);

        $response = [
            'status' => Response::HTTP_OK,
            'error' => !$url,
            'data' => [
                'url' => $url,
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/roleplay/project/{id}/save-resource", name="roleplay_project_save_resource", methods={"POST"})
     */
    public function saveResource(Request $request): Response
    {
        $year = date('Y');
        $month = date('m');
        $file = $request->files->get('file');

        if ($this->isImage($file)) {
            $folder = $this->getParameter('roleplay_uploads_path')
                . $year . DIRECTORY_SEPARATOR
                . $month . DIRECTORY_SEPARATOR;
            $url = $this->saveFile($file, $folder);

            $response = [
                'status' => Response::HTTP_OK,
                'error' => !$url,
                'data' => [
                    'url' => $url,
                ],
            ];
        } elseif ($this->isVideo($file)) {
            $response = $this->uploadVideoVimeo($file);
        } else {
            $response = [
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'message' => 'Unsupported file type',
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/roleplay/project/{id}/remove-file", name="roleplay_project_remove_file", methods={"DELETE"})
     */
    public function removeFile(Request $request): Response
    {
        $requestData = json_decode($request->getContent(), true);
        $url = $requestData['resourcePath'];

        $parsedUrl = parse_url($url);
        $path = $parsedUrl['path'];
        $pathWithoutFirstSlash = ltrim($path, '/');

        try {
            if ($this->isVimeoUrl($url)) {
                $this->removeVideoVimeo($url);
            } elseif ($this->isValidLocalUrl($url)) {
                $this->removeLocalFile($pathWithoutFirstSlash);
            } else {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => 'not valid url',
                ]);
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'data' => 'Error deleting file: ' . $e->getMessage(),
            ]);
        }
    }

    private function isVimeoUrl($url): bool
    {
        $patron = '/^(https?:\/\/)?(www\.)?vimeo\.com\/(\d+)/i';

        return (bool) preg_match($patron, $url);
    }

    private function extractVimeoVideoId($url): ?string
    {
        $patron = '/vimeo\.com\/(\d+)/';
        preg_match($patron, $url, $matches);

        return $matches[1] ?? null;
    }

    private function removeVideoVimeo($url): void
    {
        $videoId = $this->extractVimeoVideoId($url);
        $idFolderVimeo = $this->settingsService->get('app.projectIdRoleplay');
        $this->vimeoService->deleteFromVimeo($videoId, $idFolderVimeo);
    }

    private function isValidLocalUrl($url): bool
    {
        $pathRoleplay = $this->settingsService->get('roleplay_uploads_path');
        $pathRoleplayPattern = preg_quote($pathRoleplay, '/');
        $pattern = '/^https?:\/\/.*' . $pathRoleplayPattern . '/i';

        return (bool) preg_match($pattern, $url);
    }

    private function removeLocalFile($path): void
    {
        if (file_exists($path)) {
            $success = unlink($path);

            if (!$success) {
                throw $this->createNotFoundException("Cannot delete $path");
            }
        }
    }

    protected function saveFile($file, $folder): ?string
    {
        $folderPath = $this->getParameter('kernel.project_dir')
            . DIRECTORY_SEPARATOR
            . 'public'
            . DIRECTORY_SEPARATOR
            . $folder;

        $filesystem = new Filesystem();
        if (!$filesystem->exists($folderPath)) {
            $filesystem->mkdir($folderPath);
        }

        $url = null;
        if ($file) {
            $originalExtension = strtolower(pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION));
            $fileName = str_replace('.', '', uniqid('', true)) . '.' . $originalExtension;
            $file->move($folderPath, $fileName);

            $url = $this->request->getUriForPath(DIRECTORY_SEPARATOR . $folder . $fileName);
        }

        return $url;
    }

    private function isImage($file): bool
    {
        if (!$file) {
            return false;
        }

        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'];
        $extension = strtolower(pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION));

        return \in_array($extension, $imageExtensions);
    }

    private function isVideo($file): bool
    {
        if (!$file) {
            return false;
        }

        $videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'];
        $extension = strtolower(pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION));

        return \in_array($extension, $videoExtensions);
    }

    private function uploadVideoVimeo($file): array
    {
        $folderInVimeo = $this->getParameter('app.projectIdRoleplay');

        $result = $this->vimeoService->upload($file, $folderInVimeo);

        if (Response::HTTP_OK !== $result['status']) {
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => $result['message'],
            ];
        }

        return [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'url' => $result['link'],
            ],
        ];
    }
}
