<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Create assignment table for V2 direct user-to-course assignment tracking.
 */
final class Version20250916095951 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create assignment table for V2 direct user-to-course assignment tracking';
    }

    public function up(Schema $schema): void
    {
        // Create assignment table for V2 direct user-to-course assignment tracking
        $this->addSql(<<<'SQL'
            CREATE TABLE assignment (
                id CHAR(36) NOT NULL,
                user_id INT NOT NULL,
                resource_type VARCHAR(50) NOT NULL,
                resource_id CHAR(36) NOT NULL,
                created_at DATETIME NOT NULL,
                updated_at DATETIME DEFAULT NULL,
                deleted_at DATETIME DEFAULT NULL,
                INDEX idx_assignment_user_id (user_id),
                INDEX idx_assignment_resource (resource_type, resource_id),
                INDEX idx_assignment_user_resource (user_id, resource_type, resource_id),
                PRIMARY KEY(id)
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
    }

    public function down(Schema $schema): void
    {
        // Drop assignment table
        $this->addSql(<<<'SQL'
            DROP TABLE assignment
        SQL);
    }
}
