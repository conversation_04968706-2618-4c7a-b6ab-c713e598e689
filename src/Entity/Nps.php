<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\NpsRepository;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 *
 * @ORM\Entity(repositoryClass=NpsRepository::class)
 */
class Nps
{
    use AtAndBy;

    /**
     * @ORM\Id()
     *
     * @ORM\GeneratedValue()
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=NpsQuestion::class, inversedBy="nps")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $question;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups({"valoration"})
     */
    private $value;

    /**
     * @ORM\ManyToOne(targetEntity=UserCourse::class, inversedBy="nps")
     *
     * @ORM\JoinColumn(nullable=true)
     *
     * @Groups({"passport"})
     */
    private $course;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="nps")
     *
     * @ORM\JoinColumn(nullable=false)
     */
    private $user;

    /**
     * @ORM\Column(type="boolean")
     */
    private $toPost;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $main;

    /**
     * @ORM\Column(type="string", length=50, nullable=true)
     */
    private $type;

    /**
     * @ORM\ManyToOne(targetEntity=Announcement::class, inversedBy="nps")
     */
    private $announcement;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $highlight;

    public function __construct()
    {
        $this->toPost = false;
        $this->highlight = false;
    }

    public function __toString()
    {
        // TODO: Implement __toString() method.
        return $this->getQuestion()->getQuestion();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getQuestion(): ?NpsQuestion
    {
        return $this->question;
    }

    public function setQuestion(?NpsQuestion $question): self
    {
        $this->question = $question;

        return $this;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(?string $value): self
    {
        $this->value = $value;

        return $this;
    }

    public function getCourse(): ?UserCourse
    {
        return $this->course;
    }

    public function setCourse(?UserCourse $course): self
    {
        $this->course = $course;

        return $this;
    }

    public function getUser(): ?user
    {
        return $this->user;
    }

    public function setUser(?user $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getToPost(): ?bool
    {
        return $this->toPost;
    }

    public function setToPost(bool $toPost): self
    {
        $this->toPost = $toPost;

        return $this;
    }

    public function getMain(): ?bool
    {
        return $this->main;
    }

    public function setMain(?bool $main): self
    {
        $this->main = $main;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getAvatarImage()
    {
        return $this->user->getAvatar() ? $this->user->getAvatar() : 'default.svg';
    }

    public function getAnnouncement(): ?Announcement
    {
        return $this->announcement;
    }

    public function setAnnouncement(?Announcement $announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }

    public function isHighlight(): ?bool
    {
        return $this->highlight ?? false;
    }

    public function setHighlight(?bool $highlight): self
    {
        $this->highlight = $highlight;

        return $this;
    }
}
