<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\NpsQuestionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 *
 * @ORM\Entity(repositoryClass=NpsQuestionRepository::class)
 */
class NpsQuestion implements TranslatableInterface
{
    use AtAndBy;
    use TranslatableTrait;

    public const TYPE_NPS = 'nps';
    public const TYPE_TEXT = 'text';
    public const TYPE_CHECKBOX = 'checkbox'; 
    public const TYPE_RADIO = 'radio';
    public const TYPE_SWITCH = 'switch';
    public const TYPE_TEST = 'test';

    public const TYPE_QUESTION = [
        'nps' => 'nps',
        'text' => 'text',
        'checkbox' => 'checkbox',
        'radio' => 'radio',
        'switch' => 'switch',
        self::TYPE_TEST => 'test',
    ];

    /**
     * @ORM\Id()
     *
     * @ORM\GeneratedValue()
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"nps_questions"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"nps_questions"})
     */
    private $type;

    /**
     * @ORM\Column(type="boolean")
     */
    private $main;

    /**
     * @ORM\Column(type="text")
     *
     * @Groups({"nps_questions"})
     */
    private $question;

    /**
     * @ORM\OneToMany(targetEntity=Nps::class, mappedBy="question")
     */
    private $nps;

    /**
     * @ORM\Column(type="integer")
     *
     * @Groups({"nps_questions"})
     */
    private $position;

    /**
     * @ORM\ManyToMany(targetEntity=Course::class, inversedBy="npsQuestions")
     */
    private $course;

    /**
     * @ORM\Column(type="integer")
     */
    private $source;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $entitySubsidizer;

    /**
     * @ORM\ManyToMany(targetEntity=Announcement::class, inversedBy="npsQuestions")
     */
    private $announcement;

    /**
     * @ORM\ManyToOne(targetEntity=Survey::class, inversedBy="npsQuestions")
     */
    private $survey;

    /**
     * @ORM\Column(type="boolean")
     */
    private $active = false;

    /**
     * @ORM\OneToMany(targetEntity=NpsQuestionDetail::class, mappedBy="npsQuestion", orphanRemoval=true, cascade={"persist","remove"})
     */
    private $npsQuestionDetails;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $randomOrder;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isRequired;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isConfidential;

    public function __construct()
    {
        $this->nps = new ArrayCollection();
        $this->course = new ArrayCollection();
        $this->main = 0;
        $this->announcement = new ArrayCollection();
        $this->npsQuestionDetails = new ArrayCollection();
    }

    public function __toString()
    {
        // TODO: Implement __toString() method.
        return $this->getQuestion();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        if (!\in_array($type, self::TYPE_QUESTION)) {
            throw new \InvalidArgumentException('Invalid NPS Question Type');
        }

        $this->type = $type;

        return $this;
    }

    public function getMain(): ?bool
    {
        return $this->main;
    }

    public function setMain(bool $main): self
    {
        $this->main = $main;

        return $this;
    }

    public function getQuestion(): ?string
    {
        return $this->question;
    }

    public function setQuestion(string $question): self
    {
        $this->question = $question;

        return $this;
    }

    /**
     * @return Collection|Nps[]
     */
    public function getNps(): Collection
    {
        return $this->nps;
    }

    public function addNps(Nps $np): self
    {
        if (!$this->nps->contains($np)) {
            $this->nps[] = $np;
            $np->setQuestion($this);
        }

        return $this;
    }

    public function removeNps(Nps $np): self
    {
        if ($this->nps->contains($np)) {
            $this->nps->removeElement($np);
            // set the owning side to null (unless already changed)
            if ($np->getQuestion() === $this) {
                $np->setQuestion(null);
            }
        }

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    /**
     * @return Collection|Course[]
     */
    public function getCourse(): Collection
    {
        return $this->course;
    }

    public function addCourse(Course $course): self
    {
        if (!$this->course->contains($course)) {
            $this->course[] = $course;
        }

        return $this;
    }

    public function removeCourse(Course $course): self
    {
        $this->course->removeElement($course);

        return $this;
    }

    public function getSource(): ?int
    {
        return $this->source;
    }

    public function setSource(int $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function getEntitySubsidizer(): ?string
    {
        return $this->entitySubsidizer;
    }

    public function setEntitySubsidizer(?string $entitySubsidizer): self
    {
        $this->entitySubsidizer = $entitySubsidizer;

        return $this;
    }

    /**
     * @return Collection<int, Announcement>
     */
    public function getAnnouncement(): Collection
    {
        return $this->announcement;
    }

    public function addAnnouncement(Announcement $announcement): self
    {
        if (!$this->announcement->contains($announcement)) {
            $this->announcement[] = $announcement;
        }

        return $this;
    }

    public function removeAnnouncement(Announcement $announcement): self
    {
        $this->announcement->removeElement($announcement);

        return $this;
    }

    public function getSurvey(): ?Survey
    {
        return $this->survey;
    }

    public function setSurvey(?Survey $survey): self
    {
        $this->survey = $survey;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(?bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    /**
     * @return Collection<int, NpsQuestionDetail>
     */
    public function getNpsQuestionDetails(): Collection
    {
        return $this->npsQuestionDetails;
    }

    public function addNpsQuestionDetail(NpsQuestionDetail $npsQuestionDetail): self
    {
        if (!$this->npsQuestionDetails->contains($npsQuestionDetail)) {
            $this->npsQuestionDetails[] = $npsQuestionDetail;
            $npsQuestionDetail->setNpsQuestion($this);
        }

        return $this;
    }

    public function removeNpsQuestionDetail(NpsQuestionDetail $npsQuestionDetail): self
    {
        if ($this->npsQuestionDetails->removeElement($npsQuestionDetail)) {
            // set the owning side to null (unless already changed)
            if ($npsQuestionDetail->getNpsQuestion() === $this) {
                $npsQuestionDetail->setNpsQuestion(null);
            }
        }

        return $this;
    }

    public function isRandomOrder(): ?bool
    {
        return $this->randomOrder;
    }

    public function setRandomOrder(?bool $randomOrder): self
    {
        $this->randomOrder = $randomOrder;

        return $this;
    }

    public static function getQuestionTypes(): array
    {
        return [
            [
                'type' => self::TYPE_NPS,
                'icon' => 'fa fa-star',
                'name' => strtoupper(self::TYPE_NPS),
                'detail' => false,
            ],
            [
                'type' => self::TYPE_TEXT,
                'icon' => 'fa fa-comment',
                'name' => strtoupper(self::TYPE_TEXT),
                'detail' => false,
            ],
            [
                'type' => self::TYPE_SWITCH,
                'icon' => 'fa fa-toggle-on',
                'name' => strtoupper(self::TYPE_SWITCH),
                'detail' => false,
            ],
            [
                'type' => self::TYPE_CHECKBOX,
                'icon' => 'fa fa-check-circle',
                'name' => strtoupper(self::TYPE_CHECKBOX),
                'detail' => true,
            ],
            [
                'type' => self::TYPE_RADIO,
                'icon' => 'fa fa-circle-o',
                'name' => strtoupper(self::TYPE_RADIO),
                'detail' => true,
            ],
        ];
    }

    public function isIsRequired(): ?bool
    {
        return $this->isRequired;
    }

    public function setIsRequired(?bool $isRequired): self
    {
        $this->isRequired = $isRequired;

        return $this;
    }

    public function isIsConfidential(): ?bool
    {
        return $this->isConfidential;
    }

    public function setIsConfidential(?bool $isConfidential): self
    {
        $this->isConfidential = $isConfidential;

        return $this;
    }

    public function __clone()
    {
        $npsQuestionDetails = $this->getNpsQuestionDetails();
        $this->npsQuestionDetails = new ArrayCollection();
        
        foreach ($npsQuestionDetails as $npsQuestionDetail) {
            $clonedDetail = clone $npsQuestionDetail;
            $this->addNpsQuestionDetail($clonedDetail);           
        }

        
    }
}
