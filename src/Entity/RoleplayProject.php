<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\RoleplayProjectRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=RoleplayProjectRepository::class)
 *
 * @ORM\Table(name="roleplay_project")
 */
class RoleplayProject
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"project:detail", "project:visor"})
     */
    private ?int $id;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"project:detail", "project:visor"})
     */
    private ?string $title;

    /**
     * @ORM\OneToMany(targetEntity=RoleplaySequence::class, mappedBy="project", cascade={"persist", "remove"})
     *
     * @Groups({"project:detail", "project:visor"})
     */
    private $sequences;

    /**
     * @ORM\OneToMany(targetEntity=RoleplayEnding::class, mappedBy="project", cascade={"persist"})
     *
     * @Groups({"project:detail", "project:visor"})
     */
    private $endings;

    /**
     * @ORM\OneToMany(targetEntity=RoleplayBeginning::class, mappedBy="project", cascade={"persist"})
     *
     * @Groups({"project:detail", "project:visor"})
     */
    private $beginnings;

    public function __construct()
    {
        $this->sequences = new ArrayCollection();
        $this->endings = new ArrayCollection();
        $this->beginnings = new ArrayCollection();

        $beginning = (new RoleplayBeginning())
            ->setOrder(1);
        $this->addBeginning($beginning);

        $scene = (new RoleplayScene())->setOrder(1);
        $sequence = (new RoleplaySequence())
            ->setOrder(1)
            ->addScene($scene);
        $this->addSequence($sequence);

        $ending = (new RoleplayEnding())
            ->setOrder(1);
        $this->addEnding($ending);
    }

    public function __clone()
    {
        $this->id = null;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): self
    {
        $this->title = $title;

        return $this;
    }

    /**
     * @return Collection<int, RoleplaySequence>
     */
    public function getSequences(): Collection
    {
        return $this->sequences;
    }

    public function addSequence(RoleplaySequence $sequence): self
    {
        if (!$this->sequences->contains($sequence)) {
            $this->sequences[] = $sequence;
            $sequence->setProject($this);
        }

        return $this;
    }

    public function removeSequence(RoleplaySequence $sequence): self
    {
        if ($this->sequences->removeElement($sequence)) {
            // set the owning side to null (unless already changed)
            if ($sequence->getProject() === $this) {
                $sequence->setProject(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, RoleplayEnding>
     */
    public function getEndings(): Collection
    {
        return $this->endings;
    }

    public function addEnding(RoleplayEnding $ending): self
    {
        if (!$this->endings->contains($ending)) {
            $this->endings[] = $ending;
            $ending->setProject($this);
        }

        return $this;
    }

    public function removeEnding(RoleplayEnding $ending): self
    {
        if ($this->endings->removeElement($ending)) {
            // set the owning side to null (unless already changed)
            if ($ending->getProject() === $this) {
                $ending->setProject(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, RoleplayBeginning>
     */
    public function getBeginnings(): Collection
    {
        return $this->beginnings;
    }

    public function addBeginning(RoleplayBeginning $beginning): self
    {
        if (!$this->beginnings->contains($beginning)) {
            $this->beginnings[] = $beginning;
            $beginning->setProject($this);
        }

        return $this;
    }

    public function removeBeginning(RoleplayBeginning $beginning): self
    {
        if ($this->beginnings->removeElement($beginning)) {
            // set the owning side to null (unless already changed)
            if ($beginning->getProject() === $this) {
                $beginning->setProject(null);
            }
        }

        return $this;
    }

    public function isValid(): bool
    {
        foreach ($this->getBeginnings() as $beginning) {
            if (!$beginning->isValid()) {
                return false;
            }
        }

        foreach ($this->getSequences() as $sequence) {
            if (!$sequence->isValid()) {
                return false;
            }
        }

        foreach ($this->getEndings() as $ending) {
            if (!$ending->isValid()) {
                return false;
            }
        }

        return true;
    }

    public function initializeSequences()
    {
        $this->sequences = new ArrayCollection();
    }

    public function initializeBeginnings(): void
    {
        $this->beginnings = new ArrayCollection();
    }

    public function initializeEndings()
    {
        $this->endings = new ArrayCollection();
    }
}
