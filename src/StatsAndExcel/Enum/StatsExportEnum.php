<?php

declare(strict_types=1);

namespace App\StatsAndExcel\Enum;

final class StatsExportEnum
{
    public const EXPORT_CLIENT_IBEROSTAR = 'Iberostar Campus';

    public const FILENAME = 'stats-export';
    public const FILENAME_DIPLOMAS = 'diplomas-export';
    public const FILENAME_DIPLOMAS_USER = 'diplomas-user-export';
    public const CRON_TASK_ID = 'stats-export';
    public const MODE_DEFAULT = 'DEFAULT';
    public const DEFAULT_HEADER_FIELD_USERID = 'UserId';
    public const DEFAULT_HEADER_FIELD_USERNAME = 'User name';
    public const DEFAULT_HEADER_FIELD_EMAIL = 'email';
    public const DEFAULT_HEADER_FIELD_BIRTHDATE = 'Birthdate';
    public const DEFAULT_HEADER_FIELD_DNI = 'DNI';
    public const DEFAULT_HEADER_FIELD_HRP = 'HRP';
    public const DEFAULT_HEADER_FIELD_COURSEID = 'Course ID';
    public const DEFAULT_HEADER_FIELD_COURSENAME = 'Course name';
    public const DEFAULT_HEADER_FIELD_COURSE_CATEGORY_NAME = 'Course Category name';
    public const DEFAULT_HEADER_FIELD_ANNOUNCEMENT_CREATOR = 'Announcement Creator';
    public const DEFAULT_HEADER_FIELD_GENDER = 'gender';
    public const DEFAULT_HEADER_FIELD_STARTED_AT = 'Started At';
    public const DEFAULT_HEADER_FIELD_FINISHED_AT = 'Finished At';
    public const DEFAULT_HEADER_FIELD_VALUED_AT = 'Valued At';
    public const DEFAULT_HEADER_FIELD_TIME_SPENT = 'Time spent';
    public const DEFAULT_HEADER_FIELD_NPS = 'Nps';
    public const DEFAULT_HEADER_FIELD_EMPLOYEER = 'Employeer';
    public const DEFAULT_HEADER_FIELD_OPINION = 'Opinion';
    public const DEFAULT_HEADER_FIELD_COMPANY_NAME = 'Company name';
    public const DEFAULT_HEADER_PREFIX = [StatsExportEnum::DEFAULT_HEADER_FIELD_USERID, StatsExportEnum::DEFAULT_HEADER_FIELD_USERNAME,
        StatsExportEnum::DEFAULT_HEADER_FIELD_EMAIL, StatsExportEnum::DEFAULT_HEADER_FIELD_BIRTHDATE
    ];

    public const DEFAULT_HEADER_IBERO = [StatsExportEnum::DEFAULT_HEADER_FIELD_DNI, StatsExportEnum::DEFAULT_HEADER_FIELD_HRP];

    public const DEFAULT_HEADER_INBETWEEN = [StatsExportEnum::DEFAULT_HEADER_FIELD_COURSEID, StatsExportEnum::DEFAULT_HEADER_FIELD_COURSENAME,
        StatsExportEnum::DEFAULT_HEADER_FIELD_COURSE_CATEGORY_NAME, StatsExportEnum::DEFAULT_HEADER_FIELD_ANNOUNCEMENT_CREATOR,
    ];

    public const DEFAULT_HEADER_SUFFIX = [StatsExportEnum::DEFAULT_HEADER_FIELD_STARTED_AT, StatsExportEnum::DEFAULT_HEADER_FIELD_FINISHED_AT,
        StatsExportEnum::DEFAULT_HEADER_FIELD_VALUED_AT, StatsExportEnum::DEFAULT_HEADER_FIELD_TIME_SPENT, StatsExportEnum::DEFAULT_HEADER_FIELD_NPS,
        StatsExportEnum::DEFAULT_HEADER_FIELD_OPINION,
    ];

    public const DEFAULT_HEADER_SUFFIX_IBEROSTAR = [StatsExportEnum::DEFAULT_HEADER_FIELD_COMPANY_NAME, StatsExportEnum::DEFAULT_HEADER_FIELD_STARTED_AT, StatsExportEnum::DEFAULT_HEADER_FIELD_FINISHED_AT,
        StatsExportEnum::DEFAULT_HEADER_FIELD_VALUED_AT, StatsExportEnum::DEFAULT_HEADER_FIELD_TIME_SPENT, StatsExportEnum::DEFAULT_HEADER_FIELD_NPS,
        StatsExportEnum::DEFAULT_HEADER_FIELD_OPINION,
    ];
}
