<?php

declare(strict_types=1);

namespace App\StatsAndExcel\Enum;

final class StatsLiteMaskEnum
{
    public const GROUP_SETTINGS_SEPARATOR = '.';

    public const GROUP_SETTINGS_VAL_TRUE = 'true';
    public const GROUP_SETTINGS_VAL_FALSE = 'false';

    public const GROUP_SETTINGS_FORMATION_NAME = 'app.stats.lite_formation';
    public const GROUP_SETTINGS_EVOLUTION_NAME = 'app.stats.lite_evolution';
    public const GROUP_SETTINGS_DEMOGRAPHY_NAME = 'app.stats.lite_demography';
    public const GROUP_SETTINGS_ACTIVITY_NAME = 'app.stats.lite_activity';
    public const GROUP_SETTINGS_ITINERARY_NAME = 'app.stats.lite_itinerary';

    public const STATS_LITE_FORMATION_HOURS_TEXT = 'formationHours';

    public const STATS_LITE_PEOPLE_WITH_COURSES_TEXT = 'peopleWithCourses';

    public const STATS_LITE_COURSES_STARTED_AND_FINISHED_TEXT = 'courseStartedAndFinished';

    public const STATS_LITE_REQUIRED_COURSES_TEXT = 'requiredCourses';

    public const STATS_LITE_GENERAL_TEXT = 'general';

    public const STATS_LITE_OPENED_COURSES_TEXT = 'openedCourses';

    public const STATS_LITE_EDUCATIVE_STATUS_TEXT = 'educativeStatus';

    public const STATS_LITE_GAMIFIED_PILLS_TEXT = 'gamifiedPills';

    public const STATS_LITE_GAMIFIED_TEST_TEXT = 'gamifiedTest';

    public const STATS_LITE_PEOPLE_PERFORMANCE_TEXT = 'peoplePerformance';

    public const STATS_LITE_COURSES_BY_STARS_TEXT = 'coursesByStars';

    public const STATS_LITE_STRUCTURE_AND_HOTEL_TEXT = 'structureAndHotel';

    public const STATS_LITE_SCHOOL_FINISHED_AND_PROGRESS_TEXT = 'schoolFinishedAndProgress';

    public const STATS_LITE_COURSES_BY_SCHOOL_TEXT = 'coursesBySchool';

    public const STATS_LITE_COURSES_BY_DEPARTMENT_TEXT = 'coursesByDepartment';

    public const STATS_LITE_USERS_MORE_ACTIVE_BY_COURSES_TEXT = 'usersMoreActivesByCourses';
}
