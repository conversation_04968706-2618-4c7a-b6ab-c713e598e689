<?php

declare(strict_types=1);

namespace App\StatsAndExcel\Controller;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Center;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\Department;
use App\Entity\Export;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\ItineraryCourse;
use App\Entity\Nps;
use App\Entity\ProfessionalCategory;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Entity\UserExtra;
use App\Repository\CenterRepository;
use App\Repository\ChapterRepository;
use App\Repository\CourseRepository;
use App\Repository\ExportRepository;
use App\Repository\FilterCategoryRepository;
use App\Repository\FilterRepository;
use App\Repository\ForumPostRepository;
use App\Repository\NpsRepository;
use App\Repository\ProfessionalCategoryRepository;
use App\Repository\UserCourseChapterRepository;
use App\Repository\UserCourseRepository;
use App\Repository\UserLoginRepository;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use App\Service\Task\TaskService;
use App\StatsAndExcel\Enum\StatsExportEnum;
use App\StatsAndExcel\Enum\StatsFiltersEnum;
use App\StatsAndExcel\Services\StatsLiteService;
use App\StatsAndExcel\Services\StatsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use PhpOffice\PhpSpreadsheet\Writer\Exception;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Intl\Countries;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class StatsController extends AbstractController
{
    use SerializerTrait;

    private SettingsService $settings;
    private EntityManagerInterface $em;
    private AdminContextProvider $context;
    private LoggerInterface $logger;
    private JWTManager $jwt;
    private TaskService $taskService;
    protected TranslatorInterface $translator;
    protected RequestStack $request;
    protected AdminUrlGenerator $adminUrlGenerator;

    protected StatsService $statsService;
    protected StatsLiteService $statsLiteService;

    public function __construct(
        SettingsService $settings,
        EntityManagerInterface $em,
        AdminContextProvider $context,
        LoggerInterface $logger,
        JWTManager $jwt,
        TranslatorInterface $translator,
        RequestStack $request,
        AdminUrlGenerator $adminUrlGenerator,
        StatsService $statsService,
        StatsLiteService $statsLiteService,
        TaskService $taskService
    ) {
        $this->settings = $settings;
        $this->em = $em;
        $this->context = $context;
        $this->logger = $logger;
        $this->jwt = $jwt;
        $this->translator = $translator;
        $this->request = $request;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->statsService = $statsService;
        $this->statsLiteService = $statsLiteService;
        $this->taskService = $taskService;
    }

    /**
     * @Route("/admin/oldstats", name="admin-old-stats")
     */
    public function index(
        CourseRepository $courseRepository,
        CenterRepository $centerRepository,
        ProfessionalCategoryRepository $professionalCategoryRepository,
        UserLoginRepository $userLoginRepository,
        UserRepository $userRepository,
        UserCourseRepository $userCourseRepository
    ): Response {
        $countries = $this->getCountries();

        $courses = array_map(function ($course) {
            return ['id' => $course->getId(), 'name' => $course->getName()];
        }, $courseRepository->getOriginalCourses());

        $centers = $this->getCenters();
        $professionalCategories = $this->getCategory();
        $departaments = $this->getDepartament();
        $genders = $this->getGenders();
        $divisions = $this->getDivisions();
        $devicesSesion = $this->getAllDevicesSesion($userLoginRepository);
        $distributionAgeUsers = $this->distributionUserByAgeAll($userRepository);
        $totalLogins = $this->getAllLogins($userLoginRepository);
        $totalDistinctLogins = $this->getAllDistinctLogins($userLoginRepository);
        $usersWithAtLeastOneCourseFinished = $this->getAllUsersWithAtLeastOneCourseFinished($userCourseRepository);

        $filter_categories = $this->getFilterCategories();

        return $this->render('admin/stats/index.html.twig', [
            'countries' => $this->settings->get('app.filter.country') ? $countries : '',
            'courses' => $courses,
            'centers' => $this->settings->get('app.filter.center') ? $centers : '',
            'professionalCategories' => $this->settings->get('app.filter.category') ? $professionalCategories : '',
            'departaments' => $this->settings->get('app.filter.departament') ? $departaments : '',
            'genders' => $this->settings->get('app.filter.gender') ? $genders : '',
            'divisions' => $this->settings->get('app.filter.division') ? $divisions : '',
            'divisionCountries' => $this->settings->get('app.filter.division') ? $this->settings->get('app.filter.divisions') : '',
            'filterCategories' => $filter_categories,
            'divicesSesion' => $devicesSesion,
            'distributionAgeUsers' => $distributionAgeUsers,
            'totalLogin' => $totalLogins,
            'totalDistincLogin' => $totalDistinctLogins,
            'usersWithAtLeastOneCourseFinished' => $usersWithAtLeastOneCourseFinished,
        ]);
    }

    /**
     * @Route("/admin/stats", name="admin_stats")
     */
    public function newstats(CourseRepository $courseRepository): Response
    {
        $courses = array_map(function ($course) {
            return ['id' => $course->getId(), 'name' => $course->getName()];
        }, $courseRepository->getOriginalCourses());

        return $this->render('admin/stats/newStats.html.twig', [
            'countries' => $this->settings->get('app.filter.country') ? $this->getCountries() : [],
            'courses' => $courses,
            'centers' => $this->settings->get('app.filter.center') ? $this->getCenters() : [],
            'professionalCategories' => $this->settings->get('app.filter.category') ? $this->getCategory() : [],
            'departaments' => $this->settings->get('app.filter.departament') ? $this->getDepartament() : [],
            'genders' => $this->settings->get('app.filter.gender') ? $this->getGenders() : [],
            'divisions' => $this->settings->get('app.filter.division') ? $this->getDivisions() : [],
            'divisionCountries' => $this->settings->get('app.filter.division') ? $this->settings->get('app.filter.divisions') : [],
            'filterCategories' => $this->getFilterCategories() ?? [],
            'statsFormationSettings' => $this->statsLiteService->getFormationSettings(),
            'statsEvolutionSettings' => $this->statsLiteService->getEvolutionSettings(),
            'statsDemographySettings' => $this->statsLiteService->getDemographySettings(),
            'statsActivitySettings' => $this->statsLiteService->getActivitySettings(),
            'statsItinerarySettings' => $this->statsLiteService->getItinerarySettings(),
        ]);
    }

    /**
     * @Route("/admin/stats/export", name="admin_stats_export")
     *
     * @throws Exception
     */
    public function exportData(
        Request $request,
        UserCourseRepository $userCourseRepository,
        NpsRepository $npsRepository,
        FilterCategoryRepository $filterCategoryRepository
    ): Response {
        if ($request->isMethod('post')) {
            $conditions['country'] = $request->get('country');
            $conditions['center'] = $request->get('center');
            $conditions['category'] = $request->get('category');
            $conditions['departament'] = $request->get('departament');
            $conditions['startDate'] = $request->get('start');
            $conditions['endDate'] = $request->get('end');
            $conditions['gender'] = $request->get('gender');
            $conditions['activeUsers'] = $request->get('active');
            // $conditions['course_full'] = $request->get('course_full');
            // $conditions['course_intime'] = $request->get('course_intime');
            $conditions['courseStartedIntime'] = $request->get('courseStartedIntime');
            $conditions['courseFinishedIntime'] = $request->get('courseFinishedIntime');
            $conditions['customFilters'] = $request->get('custom');
            $default_name = StatsExportEnum::FILENAME;
            $filename = !empty($request->get('filename')) ? $request->get('filename') : $default_name;
            $conditions = $this->cleanConditions($conditions, false);

            if ($this->settings->get('app.export.active_cron_exports')) {
                $exportRepository = $this->em->getRepository(Export::class);
                $this->logger->debug(serialize($conditions));

                $result = $this->taskService->enqueueTask(
                    $this->getUser(),
                    'export-file',
                    $conditions,
                    StatsExportEnum::CRON_TASK_ID,
                    $filename
                );

                return $this->sendResponse([
                    'status' => Response::HTTP_OK,
                    'error' => false,
                    'data' => $result
                ]);
            }

            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
            $userDataItems = $userCourseRepository->findExport($conditions, $user);

            /* EXCEL EXPORT GENERATION */

            $fileNameExt = "{$filename}.xlsx";

            $filterCategory = $this->em->getRepository(FilterCategory::class)->findAll();
            // $dataHeader = $this->setupExportHeaderItems( $filterCategory);
            // $dataItems = $this->setupExportDataItems( $userDataItems, $npsRepository, $filterCategory);
            $dataItems = $this->statsService->setupExportDataItems($userDataItems, $npsRepository, $filterCategory);

            // $exportedFile = $this->statsService->generateExcelSheet( $conditions, $dataItems, $filename, $dataHeader);
            $exportedFile = $this->statsService->generateExcelSheet($conditions, $dataItems, $filename);

            if ($exportedFile) {
                return $this->file($exportedFile, $fileNameExt, ResponseHeaderBag::DISPOSITION_INLINE);
            }
            // ERROR MESSAGE WHEN FILE IS NOT CREATED CORRECTLY
        }

        if (!$this->settings->get('app.user.useFilters')) {
            return $this->render('admin/stats/export.html.twig', [
                'countries' => $this->settings->get('app.filter.country') ? $this->getCountries() : '',
                'centers' => $this->settings->get('app.filter.center') ? $this->getCenters() : '',
                'categories' => $this->settings->get('app.filter.category') ? $this->getCategory() : '',
                'departaments' => $this->settings->get('app.filter.departament') ? $this->getDepartament() : '',
                'genders' => $this->settings->get('app.filter.gender') ? $this->getGenders() : '',
            ]);
        }

        if (!\in_array('ROLE_ADMIN', $this->getUser()->getRoles())) {
            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
            $filtersManager = $user->getFilters();

            $filters_id = [];
            $filters = [];

            foreach ($filtersManager as $filter) {
                array_push($filters_id, $filter->getId());
            }

            if ($filtersManager) {
                $filterRepository = $this->em->getRepository(Filter::class);
                $filtersCategory = $filterRepository->getFilterCategoryManager($filters_id);

                foreach ($filtersCategory as $category) {
                    $filterUser = $filterRepository->getManagerFilters($user->getId(), $category['id']);
                    array_push($filters, [
                        'id' => $category['id'],
                        'name' => $category['name'],
                        'filters' => $filterUser,
                    ]);
                }
            }
        } else {
            $filters = $filterCategoryRepository->findAll();
        }

        return $this->render('admin/stats/exportFilters.html.twig', [
            'filters' => $filters,
            'showOnlyActivesFilter' => 1,
            'courseStartedIntime' => 1,
            'courseFinishedIntime' => 1,
            // 'course_full' => 1,
            // 'course_intime' => 1
        ]);
    }

    /* MOVED TO THE STATS SERVICE: TO BE DELETED *
    private function setupExportHeaderItems( $filterCategory): array {

        $categories = [];

        foreach ($filterCategory as $filCat) {
            array_push(
                $categories,
                $filCat->getName(),
            );
        }

        $head1 = ['UserId',  'User name', 'email', 'Birthdate', 'Course ID', 'Course name', 'Announcement Creator'];
        $head1_gender = ['UserId', 'User name', 'email', 'Birthdate', 'gender', 'Course ID', 'Course name', 'Announcement Creator'];

        $head2 = ['Started At', 'Finished At', 'Time spent', 'Nps', 'Opinion'];

        $headExcel = $this->settings->get('app.export.gender_excel') ? array_merge($head1_gender, $categories, $head2) :  array_merge($head1, $categories, $head2);

        return $headExcel;
    }
    */

    private function setupExportDataItems($userDataItems, $npsRepository, $filterCategory): array
    {
        $filterRepository = $this->em->getRepository(Filter::class);

        $newUserData = [];

        foreach ($userDataItems as $userDataItem) {
            $debug = '';

            $userTypeNps = '-';
            $userTypeOpinion = '-';
            $userId = $userDataItem['userId'];
            $userFind = $this->em->getRepository(User::class)->find($userId);
            $userNps = $npsRepository->findBy(['course' => $userDataItem['userCourseId'], 'user' => $userDataItem['userId'], 'main' => 1]);

            foreach ($filterCategory as $filCat) {
                $filtersUser = $filterRepository->fetchFiltersUsersAll($userFind->getId(), $filCat->getId());
                if ('' != $filtersUser) {
                    $data = '';
                    foreach ($filtersUser as $filterResult) {
                        $data .= $filterResult['name'] . StatsFiltersEnum::CATEGORY_SEPARATOR;
                    }
                    // remove separator if found
                    $lastSeparatorPosition = strrpos($data, StatsFiltersEnum::CATEGORY_SEPARATOR);
                    if (false !== $lastSeparatorPosition && $lastSeparatorPosition == (\strlen($data) - 1)) {
                        $data = substr($data, 0, -1);
                    }
                    $userDataItem['filtro_' . $filCat->getId()] = $data;
                }
            }

            $userDataItem['startedAt_'] = $userDataItem['startedAt'];
            $userDataItem['finishedAt_'] = $userDataItem['finishedAt'];
            $userDataItem['timeSpent_'] = TimeUtils::timeUserInTheCOurse($userDataItem['timeSpent']); // $this->timeUserInTheCOurse($userDataItem['timeSpent']);

            if ($userNps) {
                foreach ($userNps as $user) {
                    if ('nps' === $user->getType()) {
                        $userTypeNps = $user->getValue();
                    } else {
                        $userTypeOpinion = $user->getValue();
                    }
                }
            }
            $userDataItem['nps'] = $userTypeNps;
            $userDataItem['opinion'] = $userTypeOpinion;
            if (!$this->settings->get('app.export.gender_excel')) {
                unset($userDataItem['gender']);
            }

            unset($userDataItem['userCourseId']);
            unset($userDataItem['startedAt']);
            unset($userDataItem['finishedAt']);
            unset($userDataItem['timeSpent']);

            array_push($newUserData, $userDataItem);
        }

        // return $userDataItems;
        return $newUserData;
    }

    /*
    private function generateExcelConfigSheet($spreadsheet, $conditions, $filters ) {
        //moved to service

    }
    */

    private function timeUserInTheCOurse($time)
    {
        $horas = floor($time / 3600);
        $minutos = floor(($time - ($horas * 3600)) / 60);
        $segundos = $time - ($horas * 3600) - ($minutos * 60);

        return $horas . ':' . $minutos . ':' . $segundos;
    }

    /**
     * @Route("/admin/stats/general", name="admin_stats_general")
     */
    public function generalStats(): Response
    {
        $conditions = $this->getConditions();

        $data = [];

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'stats' => $data,
            ],
        ];

        return $this->sendResponse($response);
    }

    private function mostPopularCourse($userCourseRepository, $conditions)
    {
        $rankingCourses = $userCourseRepository->getRankingCourses($conditions, 'DESC');
        $mostPopularCourse = [];

        foreach ($rankingCourses as $ranking) {
            $id = $ranking['course'];
            array_push($mostPopularCourse, [
                'id' => $ranking['course'],
                'name' => $ranking['name'],
                'count' => $ranking['count'],
                'link' => $this->generateUrlCourse($id),
            ]);
        }

        return $mostPopularCourse;
    }

    private function npsTopAndWorstCourses($order, $conditions)
    {
        $npsRepository = $this->em->getRepository(Nps::class);

        $chartByNps = $this->settings->get('app.stats.chart_by_nps');
        $topAndWorstCourses = [];

        if ($chartByNps) {
            $rankingCourses = $npsRepository->getNpsRanking($order, $conditions, 10);
        } else {
            $rankingCourses = $npsRepository->getStarsAvgRanking($order, $conditions, 10);
        }

        foreach ($rankingCourses as $ranking) {
            $row = [
                'id' => $ranking['id'],
                'name' => $ranking['name'],
                'link' => $this->generateUrlCourse($ranking['id']),
                'chart_by_nps' => $chartByNps,
            ];

            if ($chartByNps) {
                $row['nps'] = number_format($ranking['nps'], 2);
            } else {
                $row['stars'] = number_format($ranking['stars'], 2);
            }

            $topAndWorstCourses[] = $row;
        }

        return $topAndWorstCourses;
    }

    private function usersMoreActivesAndInactives($order, $conditions): array
    {
        $userCourseRepository = $this->em->getRepository(UserCourse::class);

        $users = $userCourseRepository->countFinishedByUser($order, $conditions);

        $ranking = [];

        foreach ($users as $user) {
            $ranking[] = [
                'id' => $user['userId'],
                'name' => $user['firstName'] . ' ' . $user['lastName'],
                'finishedCourses' => (int) $user['count'],
                'link' => $this->generateUrlUser($user['userId']),
            ];
        }

        return $ranking;
    }

    private function generateUrlUser($userId)
    {
        $user = $this->em->getRepository(User::class)->find($userId);

        return $this->adminUrlGenerator
            ->unsetAll()
            ->setController(UserCrudController::class)
            ->setAction('detail')
            ->setEntityId($user->getId())
            ->generateUrl();
    }

    private function generateUrlCourse($courseId)
    {
        $course = $this->em->getRepository(Course::class)->find($courseId);

        return $this->adminUrlGenerator
            ->unsetAll()
            ->setController(CourseCrudController::class)
            ->setAction('detail')
            ->setEntityId($course->getId())
            ->generateUrl();
    }

    /**
     * @Route ("/admin/stats/logins", name="admin_stats_logins")
     */
    public function userLogins(UserLoginRepository $userLoginRepository): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'logins' => $userLoginRepository->getDailyLogins($this->getConditions()),
            ],
        ]);
    }

    /**
     * @Route ("/admin/stats/users-status", name="admin_stats_users_status")
     */
    public function userStatus(UserLoginRepository $userLoginRepository, UserRepository $userRepository): Response
    {
        $conditions = $this->getConditions();
        // Consultar como se debe filtrar las fechas en este grafico
        unset($conditions['dateFrom']);
        unset($conditions['dateTo']);
        $locale = $this->getUser()->getLocale();

        $activeUsers = $userLoginRepository->getActiveUsersLastMonth($conditions);
        $loginOnceUsers = $userLoginRepository->getLoginOnceUsers($conditions);
        $totalUsers = $userRepository->countTotalUsers($conditions);
        $neverLogin = $totalUsers - $loginOnceUsers - $activeUsers;

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'status' => [
                    [
                        'name' => $this->translator->trans('stats.users_active_last_30', [], 'messages', $locale),
                        'y' => (int) $activeUsers,
                    ],
                    [
                        'name' => $this->translator->trans('stats.users_inactive_last_30', [], 'messages', $locale),
                        'y' => (int) $loginOnceUsers - $activeUsers,
                    ],
                    [
                        'name' => $this->translator->trans('stats.users_never_login', [], 'messages', $locale),
                        'y' => $neverLogin,
                    ],
                ],
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/stats/chapter-types", name="admin_stats_chapters_type")
     */
    public function chapterTypes(ChapterRepository $chapterRepository): Response
    {
        $conditions = $this->getConditions();
        if (!empty($conditions['category'])) {
            $conditions['category'] = \is_array($conditions['category']) ? array_filter($conditions['category']) : [$conditions['category']];
            $chapters = $chapterRepository->countByTypeWithCategory($conditions);
        } else {
            $chapters = $chapterRepository->countByType($conditions);
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'types' => $this->getChaptersTypes($chapters),
            ],
        ]);
    }

    private function getChaptersTypes($chapters): array
    {
        $types = [];
        foreach ($chapters as $chapter) {
            $types[] = [
                'name' => $chapter['name'],
                'y' => (int) $chapter['count'],
                // 'icon' => $chapter->getType()->getIcon(),
            ];
        }

        return $types;
    }

    /**
     * @Route ("/admin/stats/finished-chapter-types", name="admin_stats_finished_chapters_type")
     */
    public function finishedChapterTypes(UserCourseChapterRepository $userCourseChapterRepository): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'types' => $this->getChaptersTypes($userCourseChapterRepository->countByType($this->getConditions())),
            ],
        ]);
    }

    /**
     * @Route ("/admin/stats/finished-chapters", name="admin_stats_finished_chapters")
     */
    public function finishedChapters(UserCourseChapterRepository $userCourseChapterRepository): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'finishedChapters' => $userCourseChapterRepository->getDailyFinishedChapters($this->getConditions()),
            ],
        ]);
    }

    /**
     * @Route ("/admin/stats/finished-courses", name="admin_stats_finished_courses")
     */
    public function finishedCourses(UserCourseRepository $userCourseRepository): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'finishedCourses' => $userCourseRepository->getDailyFinishedCourses($this->getConditions()),
            ],
        ]);
    }

    /**
     * @Route ("/admin/stats/time-spent", name="admin_stats_time_spent")
     */
    public function userTimeSpent(UserCourseChapterRepository $userCourseChapterRepository): Response
    {
        $conditions = $this->getConditions();

        $times = $userCourseChapterRepository->getTimeSpentByType($conditions);

        $niceTime = function ($seconds) {
            if (0 === $seconds) {
                return '-';
            }

            $hours = floor($seconds / 3600);
            $mins = floor(($seconds - $hours * 3600) / 60);

            $return = [];
            if ($hours > 0) {
                $return[] = $hours . ' ' . (1 == $hours ? 'hora' : 'horas');
            }
            if ($mins > 0) {
                $return[] = $mins . ' ' . (1 == $mins ? 'minuto' : 'minutos');
            }
            if (empty($return)) {
                $return[] = $seconds . ' ' . (1 == $seconds ? 'segundo' : 'segundos');
            }

            return implode(' ', $return);
        };

        $timeSpent = [];
        foreach ($times as $time) {
            $timeSpent[] = [
                'name' => $time['name'] ?? 'Otros',
                'y' => (int) $time['time'],
                'time' => $niceTime($time['time']),
            ];
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'time' => $timeSpent,
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/stats/daily-posts", name="admin_stats_daily_posts")
     *
     * @return Response
     */
    public function dailyForumPosts(ForumPostRepository $forumPostRepository)
    {
        $dailyPosts = $forumPostRepository->getDailyPosts();

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'dailyPosts' => $dailyPosts,
            ],
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route ("/admin/stats/users-gender", name="admin_stats_users_gender")
     */
    public function usersGender(UserRepository $userRepository): Response
    {
        $conditions = $this->getConditions();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'status' => [
                    [
                        'name' => 'Hombres',
                        'y' => (int) $userRepository->countUserGender('M', $conditions),
                    ],
                    [
                        'name' => 'Mujeres',
                        'y' => (int) $userRepository->countUserGender('F', $conditions),
                    ],
                ],
            ],
        ]);
    }

    /**
     * @Route ("/admin/stats/users-country", name="admin_stats_users_country")
     */
    public function usersCountries(UserRepository $userRepository): Response
    {
        $conditions = $this->getConditions();

        $countryUsers = $userRepository->countUserCountries($conditions);
        $dataCountriesC3 = Countries::getAlpha3Names();

        $data = [];
        foreach ($countryUsers as $countryData) {
            $data[] = [
                'country' => $countryData['country'],
                'count' => $countryData['users'],
                'countryName' => $dataCountriesC3[$countryData['country']] ?? $countryData['country'],
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    protected function getConditions()
    {
        $request = $this->request->getCurrentRequest();
        $conditions = json_decode($request->getContent(), true);
        if (!$conditions) {
            $conditions = [];
        }

        $conditions = array_merge($conditions, $request->query->all());

        return $this->cleanConditions($conditions);
    }

    private function cleanConditions($conditions, $generalStats = true)
    {
        if (empty($conditions['country'])) {
            if ($this->getUser()->isAdmin() || $this->settings->get('app.user.useFilters')) {
                unset($conditions['country']);
            } elseif ($this->getUser()->getManage()) {
                $conditions['country'] = $this->getUser()->getManage()->getCountries();
            }
        }

        if (empty($conditions['center'])) {
            if ($this->getUser()->isAdmin() || $this->settings->get('app.user.useFilters')) {
                unset($conditions['center']);
            } elseif ($this->getUser()->getManage()) {
                $conditions['center'] = $this->getUser()->getManage()->getCenters();
            }
        }

        if (!empty($conditions['dateFrom'])) {
            $conditions['dateFrom'] = \DateTime::createFromFormat('Y-m-d H:i:s', $conditions['dateFrom'] . ' 00:00:00')->format('Y-m-d H:i:s');
        }

        if (!empty($conditions['dateTo'])) {
            $conditions['dateTo'] = \DateTime::createFromFormat('Y-m-d H:i:s', $conditions['dateTo'] . ' 23:59:59')->format('Y-m-d H:i:s');
        }

        /*if (!empty($conditions['course_full']) && ($conditions['course_full'] == "false") ) {
            $conditions['course_full'] = 0;
        }
        if (!empty($conditions['course_intime']) && ($conditions['course_intime'] == "false") ) {
            $conditions['course_intime'] = 0;
        }*/
        if (!empty($conditions['courseStartedIntime']) && ('true' == $conditions['courseStartedIntime'])) {
            $conditions['courseStartedIntime'] = 1;
        }
        if (!empty($conditions['courseFinishedIntime']) && ('true' == $conditions['courseFinishedIntime'])) {
            $conditions['courseFinishedIntime'] = 1;
        }
        if (!empty($conditions['courseStartedIntime']) && ('false' == $conditions['courseStartedIntime'])) {
            $conditions['courseStartedIntime'] = 0;
        }
        if (!empty($conditions['courseFinishedIntime']) && ('false' == $conditions['courseFinishedIntime'])) {
            $conditions['courseFinishedIntime'] = 0;
        }

        if (!isset($conditions['activeUsers'])
            || ((!empty($conditions['activeUsers'])) && (1 != \intval($conditions['activeUsers'])))
        ) {
            // reset
            $conditions['activeUsers'] = '';
        }

        // To text filters filter
        // $conditions['filters'] = [1, 2];

        if (!empty($conditions['filters'])) {
            /**
             * @var Filter[] $filters
             */
            $filters = $this->em->getRepository(Filter::class)->getFiltersByArray($conditions['filters']);
            $categories = [];
            foreach ($filters as $filter) {
                $categories[$filter->getFilterCategory()->getId()][] = $filter->getId();
            }
            $conditions['filters'] = $categories;
        } elseif (!\in_array('ROLE_ADMIN', $this->getUser()->getRoles()) && $this->settings->get('app.user.useFilters') && $generalStats) {
            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
            $filters = $user->getFilters();
            $categories = [];

            foreach ($filters as $filter) {
                $categories[$filter->getFilterCategory()->getId()][] = $filter->getId();
            }
            if (!empty($categories)) {
                $conditions['filters'] = $categories;
            }
        }

        return $conditions;
    }

    private function getCountries()
    {
        $countries = [];

        if ($this->getUser()->isAdmin()) {
            $countries = array_flip($this->settings->get('app.user.extrafields')['country']['options']['choices']);
        } elseif (!empty($this->getUser()->getManage()->getCountries())) {
            $countries = array_flip($this->settings->get('app.user.extrafields')['country']['options']['choices']);

            $userCountries = [];
            foreach ($this->getUser()->getManage()->getCountries() as $country) {
                $userCountries[$country] = $countries[$country];
            }
            $countries = $userCountries;
        }

        return array_map(function ($key, $name) {
            return ['id' => $key, 'name' => $name];
        }, array_keys($countries), $countries);
    }

    private function getDivisions()
    {
        $divisions = [];

        $divisions = array_flip($this->settings->get('app.user.extrafields')['division']['options']['choices']);

        return array_map(function ($key, $name) {
            return ['id' => $key, 'name' => $name];
        }, array_keys($divisions), $divisions);
    }

    private function getCenters()
    {
        $centerRepository = $this->em->getRepository(Center::class);
        $centers = [];

        if ($this->getUser()->isAdmin()) {
            $centers = $centerRepository->findList();
        } elseif ($this->getUser()->getManage() && $this->getUser()->getManage()->getCenters()) {
            $centers = $centerRepository->findList($this->getUser()->getManage()->getCenters());
        }

        return !empty($centers) ? array_map(function ($center) {
            return ['id' => $center->getId(), 'name' => $center->getName()];
        }, $centers) : [];
    }

    private function getCategory()
    {
        $categoryRepository = $this->em->getRepository(ProfessionalCategory::class);
        $categorys = [];

        $categorys = $this->em->getRepository(ProfessionalCategory::class)->getProfesionalCategoryUser();

        return array_map(function ($category) {
            return ['id' => $category->getId(), 'name' => $category->getName()];
        }, $categorys);
    }

    private function getDepartament()
    {
        $departamentRepository = $this->em->getRepository(Department::class);
        $departaments = [];

        if ($this->getUser()->isAdmin()) {
            $departaments = $departamentRepository->findList();
        } elseif ($this->getUser()->getManage()->getCenters()) {
            $departaments = $departamentRepository->findList($this->getUser()->getManage()->getCategory());
        }

        return array_map(function ($departament) {
            return ['id' => $departament->getId(), 'name' => $departament->getName()];
        }, $departaments);
    }

    private function getGenders()
    {
        $genders = [
            [
                'id' => 'M',
                'name' => $this->translator->trans('user.gender.m', [], 'messages', $this->getUser()->getLocale()),
            ],
            [
                'id' => 'F',
                'name' => $this->translator->trans('user.gender.f', [], 'messages', $this->getUser()->getLocale()),
            ],
        ];

        return $genders;
    }

    private function getFilterCategories()
    {
        $filterRepository = $this->em->getRepository(Filter::class);
        $filter_categories = [];

        if (!\in_array('ROLE_ADMIN', $this->getUser()->getRoles())) {
            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUserIdentifier()]);
            $filtersManager = $user->getFilters();
            $filters_id = [];

            foreach ($filtersManager as $filter) {
                $filters_id[] = $filter->getId();
            }

            $filterRepository = $this->em->getRepository(Filter::class);
            $filtersCategory = $filterRepository->getFilterCategoryManager($filters_id);

            foreach ($filtersCategory as $category) {
                $filters = [];
                $filterUser = $filterRepository->getManagerFilters($user->getId(), $category['id']);

                foreach ($filterUser as $fil) {
                    $filters[] = [
                        'id' => $fil->getId(),
                        'name' => $fil->getName(),
                    ];
                }

                usort($filters, function (array $elem1, $elem2) {
                    return $elem1['name'] <=> $elem2['name'];
                });

                $filter_categories[] = [
                    'id' => $category['id'],
                    'name' => $category['name'],
                    'filters' => $filters,
                ];
            }
        } else {
            foreach ($this->em->getRepository(FilterCategory::class)->getCategories() as $category) {
                $filters = [];
                foreach ($filterRepository->getFilterCategory($category->getId()) as $filter) {
                    $filters[] = [
                        'id' => $filter->getId(),
                        'name' => $filter->getName(),
                    ];
                }

                usort($filters, function (array $elem1, $elem2) {
                    return $elem1['name'] <=> $elem2['name'];
                });

                $filter_categories[] = [
                    'id' => $category->getId(),
                    'name' => $category->getName(),
                    'filters' => $filters,
                ];
            }
        }

        return $filter_categories;
    }

    /**
     * @Route ("/admin/stats/devices-sesion", name="admin_stats_devices_sesion")
     */
    public function getDevicesSesion(UserLoginRepository $userLoginRepository): Response
    {
        $conditions = $this->getConditions();
        $devices = $userLoginRepository->devicesSesion($conditions);

        foreach ($devices as $key => $device) {
            if ('mobile' == $device['name']) {
                $devices[$key]['name'] = 'mobile/tablet';
            }
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'devices' => $devices,
            ],
        ]);
    }

    public function getAllDevicesSesion(UserLoginRepository $userLoginRepository)
    {
        $conditions = [];
        $devices = $userLoginRepository->devicesSesion($conditions);
        $devicesSesion = [];
        foreach ($devices as $device) {
            $count = $userLoginRepository->countSesionDevice($device['name'], $conditions);
            $devicesSesion[] = [
                'name' => $device['name'],
                'y' => \intval($count),
            ];
        }

        return $devicesSesion;
    }

    public function getAllLogins($userLoginRepository)
    {
        $conditions = [];

        return $userLoginRepository->loginSesion($conditions);
    }

    /**
     * @Route ("admin/stats/login-sesion", name="admin_stats_login_sesion")
     */
    public function getLoginsSesion(UserLoginRepository $userLoginRepository): Response
    {
        $conditions = $this->getConditions();
        $devices = $userLoginRepository->loginSesion($conditions);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'devices' => $devices,
            ],
        ]);
    }

    public function getAllDistinctLogins($userLoginRepository)
    {
        $conditions = [];

        return $userLoginRepository->loginDistinctSesion($conditions);
    }

    /**
     * @Route ("admin/stats/login-distinct-sesion", name="admin_stats_login_distinct-sesion")
     */
    public function getLoginsDistinctSesion(UserLoginRepository $userLoginRepository): Response
    {
        $conditions = $this->getConditions();
        $devices = $userLoginRepository->loginDistinctSesion($conditions);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'devices' => $devices,
            ],
        ]);
    }

    public function getAllUsersWithAtLeastOneCourseFinished($userCourseRepository)
    {
        $conditions = [];

        return $userCourseRepository->getAllUsersWithAtLeastOneCourseFinished($conditions);
    }

    /**
     * @Route ("admin/stats/users-course-finished", name="admin_stats_users_course_finished")
     */
    public function getUserCourseFinished(UserCourseRepository $userCourseRepository): Response
    {
        $conditions = $this->getConditions();
        $users = $userCourseRepository->getAllUsersWithAtLeastOneCourseFinished($conditions);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'users' => $users,
            ],
        ]);
    }

    /**
     * @Route ("/admin/stats/distribution-user-age", name="adistribution-user-age")
     */
    public function distributionUserByAge(UserRepository $userRepository): Response
    {
        $conditions = $this->getConditions();
        $agesUsers = [];
        $ages = $this->rangeAges();

        foreach ($ages as $range) {
            $distributionAge = $userRepository->getDistributionUserByAge($range['ageInit'], $range['ageFinish'], $conditions);

            if ($distributionAge > 0) {
                $agesUsers[] = [
                    'name' => $range['name'],
                    'y' => \intval($distributionAge),
                ];
            }
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'user' => $agesUsers,
            ],
        ]);
    }

    private function rangeAges()
    {
        $locale = $this->getUser()->getLocale();

        $ages = [
            [
                'ageInit' => '1949-01-01',
                'ageFinish' => '1968-12-31',
                'name' => $this->translator->trans('stats.generation_babyboom', [], 'messages', $locale),
            ],
            [
                'ageInit' => '1969-01-01',
                'ageFinish' => '1980-12-31',
                'name' => $this->translator->trans('stats.generation_x', [], 'messages', $locale),
            ],
            [
                'ageInit' => '1981-01-01',
                'ageFinish' => '1993-12-31',
                'name' => $this->translator->trans('stats.generacion_milenials', [], 'messages', $locale),
            ],
            [
                'ageInit' => '1994-01-01',
                'ageFinish' => '2010-12-31',
                'name' => $this->translator->trans('stats.generacion_z', [], 'messages', $locale),
            ],
        ];

        return $ages;
    }

    public function distributionUserByAgeAll(UserRepository $userRepository)
    {
        $conditions = [];
        $agesUsers = [];
        $ages = $this->rangeAges();
        foreach ($ages as $range) {
            $distributionAge = $userRepository->distributionUserByAge($range['ageInit'], $range['ageFinish']);

            if ($distributionAge > 0) {
                $agesUsers[] = [
                    'name' => $range['name'],
                    'y' => \intval($distributionAge),
                ];
            }
        }

        return $agesUsers;
    }

    /**
     * @Route ("/admin/stats/export-data-info", name="export-data-info")
     * */
    public function exportDataInfo(ExportRepository $exportRepository): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $exportRepository->loadData(),
        ]);
    }

    /**
     * @Route ("/admin/stats/stats-download-file", name="stats-download-file")
     * */
    public function statsDownloadFile(Request $request, ExportRepository $exportRepository): Response
    {
        if (empty($request->get('id'))) {
            $this->sendResponse(['status' => Response::HTTP_BAD_REQUEST, 'error' => true]);
        }

        $fileName = $exportRepository->getFileName($request->get('id'));

        return (empty($fileName['filename'])) ?
            $this->sendResponse(['status' => Response::HTTP_NOT_FOUND, 'error' => true]) :
            $this->file($fileName['tempName'], $fileName['filename'], ResponseHeaderBag::DISPOSITION_INLINE);
    }

    /**
     * @Route ("/admin/stats/abort-export", name="abort-export")
     * */
    public function abortExport(Request $request, ExportRepository $exportRepository): Response
    {
        return $this->sendResponse($exportRepository->abortExport($request->get('id')));
    }

    /**
     * @Route ("/admin/stats/name-filter-export", name="name-filter-export", methods="POST")
     * */
    public function nameFilterExported(Request $request)
    {
        try {
            $requestData = json_decode($request->getContent(), true);
            $filters = $requestData['filters'];

            if (isset($filters)) {
                $findFilter = $this->em->getRepository(Filter::class)->getFilterDataExport($filters);
                $response = [
                    'status' => 200,
                    'error' => false,
                    'data' => $findFilter,
                ];
            }
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'ocurrio un erro al obtener los filtros: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['filter_excel']]);
    }

    /**
     * @Route ("/admin/stats/acumulative", name="stats-accumulative", methods={"GET"})
     *
     * @return Response
     */
    public function accumulativeStats(Request $request)
    {
        return $this->render('admin/stats/accumulative.html.twig', []);
    }

    /**
     * @Route("/admin/stats/accumulative/trained", name="stats-accumulative-trained", methods={"POST"})
     */
    public function accumulativeTrainedUsers(Request $request, UserCourseRepository $userCourseRepository): Response
    {
        $queryDate = new \DateTime($this->settings->get('app.stats.accumulative.start_date'));
        $lastDate = new \DateTime('first day of next month');
        $data = [];
        $past = 0;

        do {
            $actual = $userCourseRepository->getTrainedUsersByMaxDate($queryDate);
            $labelDate = clone $queryDate;
            $labelDate->sub(new \DateInterval('P1D'));
            $data[$labelDate->format('m/Y')] = [
                'period' => $actual - $past,
                'total' => $actual,
            ];
            $past = $actual;
            $queryDate = new \DateTime(date('Y-m-d', strtotime('+1 month', strtotime($queryDate->format('Y-m-d')))));
        } while ($queryDate <= $lastDate);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Route("/admin/stats/accumulative/sessions", name="stats-accumulative-sessions", methods={"POST"})
     *
     * @return Response
     */
    public function accumulativeSessions(Request $request, UserLoginRepository $userLoginRepository)
    {
        $queryDate = new \DateTime($this->settings->get('app.stats.accumulative.start_date'));
        $lastDate = new \DateTime('first day of next month');
        $data = [];
        $past = 0;

        do {
            $actual = $userLoginRepository->getUniqueLoginsByMaxDate($queryDate);
            $labelDate = clone $queryDate;
            $labelDate->sub(new \DateInterval('P1D'));
            $data[$labelDate->format('m/Y')] = [
                'period' => $actual - $past,
                'total' => $actual,
            ];
            $past = $actual;
            $queryDate = new \DateTime(date('Y-m-d', strtotime('+1 month', strtotime($queryDate->format('Y-m-d')))));
        } while ($queryDate <= $lastDate);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Route("/admin/stats/accumulative/courses", name="stats-accumulative-courses", methods={"POST"})
     *
     * @return Response
     */
    public function accumulativeCourses(Request $request, UserCourseRepository $userCourseRepository)
    {
        $queryDate = new \DateTime($this->settings->get('app.stats.accumulative.start_date'));
        $lastDate = new \DateTime('first day of next month');
        $data = [];
        $pastStarted = 0;
        $pastFinished = 0;

        do {
            $actualStarted = $userCourseRepository->getStartedCoursesByMaxDate($queryDate);
            $actualFinished = $userCourseRepository->getFinishedCoursesByMaxDate($queryDate);
            $labelDate = clone $queryDate;
            $labelDate->sub(new \DateInterval('P1D'));

            $data['started'][$labelDate->format('m/Y')] = [
                'period' => $actualStarted - $pastStarted,
                'total' => $actualStarted,
            ];
            $data['finished'][$labelDate->format('m/Y')] = [
                'period' => $actualFinished - $pastFinished,
                'total' => $actualFinished,
            ];

            $pastStarted = $actualStarted;
            $pastFinished = $actualFinished;

            $queryDate = new \DateTime(date('Y-m-d', strtotime('+1 month', strtotime($queryDate->format('Y-m-d')))));
        } while ($queryDate <= $lastDate);
    }

    /**
     * @Route("/admin/stats/accumulative/ratings", name="stats-accumulative-ratings", methods={"POST"})
     *
     * @return Response
     */
    public function accumulativeRatings(Request $request, NpsRepository $npsRepository)
    {
        $queryDate = new \DateTime($this->settings->get('app.stats.accumulative.start_date'));
        $lastDate = new \DateTime('first day of next month');
        $data = [];
        $past = 0;

        do {
            $actual = $npsRepository->getRatingsByMaxDate($queryDate);
            $labelDate = clone $queryDate;
            $labelDate->sub(new \DateInterval('P1D'));
            $data[$labelDate->format('m/Y')] = [
                'period' => $actual - $past,
                'total' => $actual,
            ];
            $past = $actual;
            $queryDate = new \DateTime(date('Y-m-d', strtotime('+1 month', strtotime($queryDate->format('Y-m-d')))));
        } while ($queryDate <= $lastDate);
    }

    /**
     * @Route("/admin/stats/accumulative/time", name="stats-accumulative-time", methods={"POST"})
     *
     * @return Response
     */
    public function accumulativeTime(Request $request, UserCourseChapterRepository $userCourseChapterRepository)
    {
        $queryDate = new \DateTime($this->settings->get('app.stats.accumulative.start_date'));
        $lastDate = new \DateTime('first day of next month');
        $data = [];
        $past = 0;

        do {
            $actual = $userCourseChapterRepository->getTimeByMaxDate($queryDate);
            $labelDate = clone $queryDate;
            $labelDate->sub(new \DateInterval('P1D'));
            $data[$labelDate->format('m/Y')] = [
                'period' => $actual - $past,
                'total' => $actual,
            ];
            $past = $actual;
            $queryDate = new \DateTime(date('Y-m-d', strtotime('+1 month', strtotime($queryDate->format('Y-m-d')))));
        } while ($queryDate <= $lastDate);
    }

    /**
     * @Route("/admin/stats/accumulative/filters", name="stats-accumulative-filters", methods={"POST", "GET"})
     *
     * @return Response
     */
    public function accumulativeUsersByFilters(Request $request, FilterRepository $filterRepository)
    {
        $categories = [4, 1, 6];
        $data = [];

        foreach ($categories as $category_id) {
            $total = 0;
            $category = $this->em->getRepository(FilterCategory::class)->find($category_id);

            $usersByFilter = $filterRepository->countUsersByFilterCategory($category);

            $data[$category->getId()] = [
                'name' => $category->getName(),
                'filters' => [],
            ];

            foreach ($usersByFilter as $filter) {
                $data[$category->getId()]['filters'][] = [
                    'name' => $filter['name'],
                    'count' => $filter['count'],
                ];
                $total += $filter['count'];
            }

            foreach ($data[$category->getId()]['filters'] as $id => $filter) {
                $data[$category->getId()]['filters'][$id]['percentage'] = round(($filter['count'] / $total) * 100, 2);
            }
        }
    }

    /**
     * @Route("/admin/stats/activity-info", name="stats-activity-info", methods={"POST"})
     */
    public function activityInfo(UserRepository $userRepository, UserLoginRepository $userLoginRepository): Response
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

        $conditionsTotalUser = $conditions;
        unset($conditionsTotalUser['active']);
        $totalUsers = $userRepository->countUsers($conditionsTotalUser);

        $conditionsActive = $conditions;
        $conditionsActive['active'] = true;
        $activeUsers = $userRepository->countUsers($conditionsActive);

        $loggedOnceUsers = $userLoginRepository->getLoginOnceUsers($conditions);
        $loggedLastMonth = $userLoginRepository->getActiveUsersLastMonth($conditions);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'totalUsers' => $totalUsers,
                'activeUsers' => $activeUsers,
                'loggedOnceUsers' => $loggedOnceUsers,
                'loggedLastMonth' => $loggedLastMonth,
            ],
        ]);
    }

    /**
     * Stats.
     */
    /**
     * @Route("/admin/stats/courseFinished/top", name="course-top-closed")
     */
    public function totalCourseByType(): Response
    {
        try {
            $itineraryRepository = $this->em->getRepository(UserCourse::class);
            $data = $itineraryRepository->topCourse();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data,
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route("/admin/stats/deparment/moreActive", name="department-more-active")
     */
    public function totalDeparmenteMoreActive(): Response
    {
        try {
            $itineraryRepository = $this->em->getRepository(Course::class);
            $data = $itineraryRepository->departmentMoreActive();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data,
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route ("/admin/stats/total/GeneralHour", name="tota-general-hour-stat", methods={"POST"})
     * */
    public function totaGeneralHourStat(UserCourseRepository $userCourseRepository): Response
    {
        $result = $userCourseRepository->getSegmentedHourGeneral($this->getConditions());

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $result['result'],
            // debug 'query'   => $result['query']
        ]);
    }

    /**
     * @Route("/admin/stats/person/withCourse", name="stat-persons-with-course")
     */
    public function statsPersonsWithCourse(UserRepository $userRepository, UserCourseRepository $userCourseRepository): Response
    {
        try {
            $conditions = $this->getConditions();
            $total = $userRepository->countUsers($conditions);
            $totalInCourse = $userCourseRepository->getTotalPersonInCourse($conditions);
            $total1Course = $userCourseRepository->getTotalPersonWithAtLeast1Course($conditions);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'totalPerson' => \intval($total),
                    'totalInCourse' => (int) $totalInCourse[0]['total'],
                    'totalOneCourse' => (int) $total1Course[0]['total'],
                    'percentWithOneCourse' => $total ? ($total1Course[0]['total'] * 100) / $total : 0,
                ],
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route ("/admin/stats/segmented/total/structureAndHotel", name="segmented-total-structure", methods={"POST"})
     * */
    public function segmentdTotalByStructure(UserCourseRepository $userCourseRepository): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $userCourseRepository->getTotalByStructure($this->getConditions()),
        ]);
    }

    public function conversorSegundosHoras($tiempo_en_segundos)
    {
        if (0 == $tiempo_en_segundos) {
            return '0h 0min 0s';
        }

        $horas = floor($tiempo_en_segundos / 3600);
        $minutos = floor(($tiempo_en_segundos - ($horas * 3600)) / 60);
        $segundos = $tiempo_en_segundos - ($horas * 3600) - ($minutos * 60);

        $hora_texto = '';
        if ($horas > 0) {
            $hora_texto .= $horas . 'h ';
        }

        if ($minutos > 0) {
            $hora_texto .= $minutos . 'min ';
        }

        if ($segundos > 0) {
            $hora_texto .= $segundos . 's';
        }

        return $hora_texto;
    }

    /**
     * @Route("/admin/stats/user/moreAndLessTime", name="stats-user-more-and-less-time-used")
     */
    public function statsUserMoreAndLessTimeUsed(): Response
    {
        try {
            $repository = $this->em->getRepository(UserCourseChapter::class);
            $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
            $dataMore = $repository->getUsersActivesAndInactives('DESC', $conditions);
            $dataLess = $repository->getUsersActivesAndInactives('ASC', $conditions);

            foreach ($dataMore as &$valorMore) {
                unset($valorMore['id']);
                $valorMore['total'] = $this->conversorSegundosHoras($valorMore['total']);
            }

            foreach ($dataLess as &$valorLess) {
                unset($valorLess['id']);
                $valorLess['total'] = $this->conversorSegundosHoras($valorLess['total']);
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'more' => $dataMore,
                    'less' => $dataLess,
                ],
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route("/admin/stats/courseFinishedVsStarted", name="stats-course-finished-vs-started")
     */
    public function statsCourseFinishedVsStarted(UserCourseRepository $userCourseRepository): Response
    {
        try {
            $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
            $dataStarted = $userCourseRepository->getCourseStartedOrFinished('started', $conditions);
            $dataFinished = $userCourseRepository->getCourseStartedOrFinished('finished', $conditions);

            $data = [];

            foreach ($dataStarted as $item) {
                if (null == $item['id']) {
                    continue;
                }
                $data[$item['id']] = [
                    'name' => $item['name'],
                    'started' => $item['count'],
                    'finished' => 0,
                    'percentFinished' => 0,
                ];
            }

            foreach ($dataFinished as $item) {
                if (null == $item['id']) {
                    continue;
                }

                if (!isset($data[$item['id']])) {
                    $data[$item['id']] = [
                        'name' => $item['name'],
                        'started' => 0,
                        'finished' => 0,
                        'percentFinished' => 0,
                    ];
                }

                $total = $item['count'] + $data[$item['id']]['started'];
                $data[$item['id']]['finished'] = $item['count'];
                $data[$item['id']]['percentFinished'] = round(($item['count'] * 100) / $total);
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => array_values($data),
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route("/admin/stats/heatMap/login", name="admin-stats-heat-map-login")
     */
    public function loginHeatMap(UserLoginRepository $userLoginRepository): Response
    {
        $loginData = $userLoginRepository->getLoginHeatMap($this->getConditions());

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => true,
            'data' => $loginData,
        ]);
    }

    /**
     * @Route("/admin/stats/heatMap/{type}", name="admin-stats-heat-map-course-activity")
     */
    public function courseActivityHeatMap($type, UserCourseRepository $userCourseRepository): Response
    {
        $userCourseData = $userCourseRepository->getCourseActivityHeatMap($type, $this->getConditions());

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => true,
            'data' => $userCourseData,
        ]);
    }

    /**
     * @Route("/admin/stats/heatMap/{type}", name="stats-time-course-started-and-finished")
     */
    public function timeCourseStartedAndFinished($type): Response
    {
        if ('login' !== $type && 'start' !== $type && 'finish' !== $type) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => true,
                'data' => 'invalid Heat Map',
            ]);
        }

        try {
            $repository = $this->em->getRepository(UserCourseChapter::class);
            $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

            $output = [];
            for ($week = 0; $week <= 6; ++$week) {
                for ($hour = 0; $hour <= 22; ++$hour) {
                    $dataLogin = $repository->getStatTimeCourseStatedFinished($conditions, $type, $hour, $hour + 2, $week);

                    $data = [
                        'weekday' => $week,
                        'time' => $hour / 2,
                        'count' => $dataLogin[0]['count'],
                    ];
                    array_push($output, $data);

                    ++$hour;
                }
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $output,
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route("/admin/stats/mandatoryCourses", name="admin_stats_mandatory_courses")
     */
    public function mandatoryCourses(): Response
    {
        $courses = 0;

        try {
            $sql = 'SELECT 	count( DISTINCT course_id ) as count
                FROM
	                ( SELECT DISTINCT course_id FROM announcement UNION SELECT DISTINCT course_id FROM itinerary_course ) c
	                LEFT JOIN course ON course.id = c.course_id 
	                WHERE course.deleted_at IS NULL';

            $stmt = $this->em->getConnection()->prepare($sql);
            $result = $stmt->executeQuery();
            $courses = (int) $result->fetchFirstColumn()[0];
        } catch (\Doctrine\DBAL\Driver\Exception $e) {
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $courses,
        ]);
    }

    /**
     * @Route("/admin/stats/voluntaryCourses", name="admin_stats_voluntary_courses")
     */
    public function voluntaryCourses(CourseRepository $courseRepository): Response
    {
        $courses = 0;

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $courseRepository->countOpenCourses(),
        ]);
    }

    /**
     * @Route("/admin/stats/courseByItinerary", name="stats-course-by-itinerary")
     */
    public function courseByItinerary(): Response
    {
        try {
            $repository = $this->em->getRepository(ItineraryCourse::class);
            $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

            $data = $repository->getCourseByItinerary($conditions);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data[0]['count'],
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    public function addUrlToGames($data, $dataHits, $dataMistake)
    {
        $return = [];
        foreach ($data as $item) {
            if ('Roulette' === $item['name']) {
                $item['url'] = '/img/stats/ruleta.svg';
            } elseif ('Double or Nothing' === $item['name']) {
                $item['url'] = '/img/stats/doble.svg';
            } elseif ('Quiz' === $item['name']) {
                $item['url'] = '/img/stats/quiz.svg';
            } elseif ('Puzzle' === $item['name']) {
                $item['url'] = '/img/stats/puzzle.svg';
            } elseif ('Hidden Words' === $item['name']) {
                $item['url'] = '/img/stats/palabras.svg';
            }

            foreach ($dataHits as $hits) {
                if ($hits['name'] == $item['name']) {
                    $item['hits'] = $hits['count'];
                    break;
                }
            }

            foreach ($dataMistake as $mistake) {
                if ($mistake['name'] == $item['name']) {
                    $item['mistake'] = $mistake['count'];
                    break;
                }
            }
            array_push($return, $item);
        }

        return $return;
    }

    /**
     * @Route("/admin/stats/totalGameUsed", name="stats-total-game-used")
     */
    public function totalGameUsed(): Response
    {
        try {
            $repository = $this->em->getRepository(Chapter::class);
            $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

            $data = $repository->getTotalGameUsed($conditions);
            $dataHits = $repository->getTotalGameUsed($conditions, true);
            $dataMistake = $repository->getTotalGameUsed($conditions, false);

            $data = $this->addUrlToGames($data, $dataHits, $dataMistake);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data,
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route("/admin/stats/totalGamesByResult", name="stats-total-games-by-result")
     */
    public function totalGamesByResult(): Response
    {
        try {
            $repository = $this->em->getRepository(Chapter::class);
            $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

            $data = $repository->getTotalGamesByResult($conditions);
            $dataTotal = $repository->getTotalGamesByResult($conditions, false);

            $hits = $repository->getTotalGamesByResult($conditions, false, true);
            $mistake = $repository->getTotalGamesByResult($conditions, false, false);

            $totalPlay = $hits[0]['count'] + $mistake[0]['count'];

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'totalGame' => $data[0]['count'],
                    'totalGeneral' => $dataTotal[0]['count'],
                    'totalHits' => $hits[0]['count'],
                    'totalMistake' => $mistake[0]['count'],
                    'percentHits' => ($totalPlay > 0) ? round(($hits[0]['count'] * 100) / $totalPlay, 0) : 0,
                    'percentMistake' => ($totalPlay > 0) ? round(($mistake[0]['count'] * 100) / $totalPlay, 0) : 0,
                ],
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route ("/admin/stats/distributionBySexAndAge", name="distribution-sex-age")
     *
     * @return Response
     */
    public function distributionBySexAndAge()
    {
        try {
            $repository = $this->em->getRepository(UserExtra::class);

            $man = $repository->getDistributionBySexAndAge('M', $this->getConditions());
            $woman = $repository->getDistributionBySexAndAge('F', $this->getConditions());

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'man' => $man,
                    'woman' => $woman,
                ],
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route ("/admin/stats/openCourse", name="stats-open-course")
     *
     * @return Response
     */
    public function statOpenCourse()
    {
        try {
            $repository = $this->em->getRepository(Course::class);
            $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

            $man = $repository->getstatOpenCourse($conditions);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $man,
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route ("/admin/stats/userPoints", name="stats-user-point")
     *
     * @return Response
     */
    public function statUserPoints()
    {
        try {
            $repository = $this->em->getRepository(User::class);
            $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    [
                        'name' => 'Star',
                        'legend' => '0-5.000 ptos.',
                        'points' => $data = $repository->getStatUserPoints($conditions, 0, 5000),
                    ],
                    [
                        'name' => 'Super star',
                        'legend' => '5.001-10.000 ptos.',
                        'points' => $data = $repository->getStatUserPoints($conditions, 5001, 10000),
                    ],
                    [
                        'name' => 'Expert',
                        'legend' => '10.001-15.000 ptos.',
                        'points' => $data = $repository->getStatUserPoints($conditions, 10001, 15000),
                    ],
                    [
                        'name' => 'Pro',
                        'legend' => '15.001-20.000 ptos.',
                        'points' => $data = $repository->getStatUserPoints($conditions, 15001, 20000),
                    ],
                    [
                        'name' => 'Challenger',
                        'legend' => '20.001-30.000 ptos.',
                        'points' => $data = $repository->getStatUserPoints($conditions, 20001, 30000),
                    ],
                    [
                        'name' => 'Master',
                        'legend' => '30.001-40.000 ptos.',
                        'points' => $data = $repository->getStatUserPoints($conditions, 30001, 40000),
                    ],
                    [
                        'name' => 'GrandMaster',
                        'legend' => '40.001-50.000 ptos.',
                        'points' => $data = $repository->getStatUserPoints($conditions, 40001, 50000),
                    ],
                    [
                        'name' => 'Legend',
                        'legend' => 'creciente en base a estrellas, +50.00 ptos.',
                        'points' => $data = $repository->getStatUserPoints($conditions, 50001),
                    ],
                ],
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route ("/admin/stats/segmented", name="stats-segmented", methods={"GET"})
     */
    public function segmentedStats(CourseRepository $courseRepository): Response
    {
        $courses = array_map(
            function ($course) {
                return ['id' => $course->getId(), 'name' => $course->getName()];
            },
            $courseRepository->getOriginalCourses()
        );

        return $this->render('admin/stats/segmented.html.twig', [
            'divisions' => $this->settings->get('app.filter.division') ? $this->getDivisions() : '',
            'countries' => $this->settings->get('app.filter.country') ? $this->getCountries() : '',
            'centers' => $this->settings->get('app.filter.center') ? $this->getCenters() : '',
            'departaments' => $this->settings->get('app.filter.departament') ? $this->getDepartament() : '',
            'professionalCategories' => $this->settings->get('app.filter.category') ? $this->getCategory() : '',
            'genders' => $this->settings->get('app.filter.gender') ? $this->getGenders() : '',
            'courses' => $courses,
            'filterCategories' => $this->getFilterCategories(),
        ]);
    }

    /**
     * @Route("/admin/stats/segmented/person", name="stats-segmented-person", methods={"POST","GET"})
     *
     * @return Response
     */
    public function segmentedPerson(UserCourseRepository $userCourseRepository)
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
        $startDate = $this->datesStatsSegments()['startDate'];
        $endDate = $this->datesStatsSegments()['endDate'];
        $data = [];
        $iterationCount = 0;

        for ($currentDate = $startDate; $currentDate < $endDate; $currentDate = strtotime('+1 month', $currentDate)) {
            $monthData = date('m', $currentDate);
            $yearData = date('Y', $currentDate);
            ++$iterationCount;

            $lastDayOfMonth = date('t', $currentDate);

            if (1 == $iterationCount) {
                $conditions['dateFrom'] = date('Y-m-d H:i:s', $currentDate);
            } else {
                $conditions['dateFrom'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-01 00:00:00"));
            }

            if ($currentDate + 2678400 >= $endDate) { // 2678400 segundos = 31 días
                $conditions['dateTo'] = date('Y-m-d H:i:s', $endDate);
            } else {
                $conditions['dateTo'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-{$lastDayOfMonth} 23:59:59"));
            }

            $bdData = $userCourseRepository->getAcumulativeUsersFormed($conditions);
            $conditionAcumulate = $conditions;
            $conditionAcumulate['dateFrom'] = date('Y-m-d H:i:s', $startDate);
            $conditionAcumulate['dateTo'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-{$lastDayOfMonth} 23:59:59"));

            $bdDataAccumulate = $userCourseRepository->getAcumulativeUsersFormed($conditionAcumulate);

            $data[$monthData . '/' . $yearData] = [
                'period' => $bdData[0]['total'],
                'total' => $bdDataAccumulate[0]['total'],
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/personBy/{type}", name="stats-segments-person-by-division", methods={"POST"})
     * */
    public function segmentedPersonDivision($type, UserCourseRepository $userCourseRepository): Response
    {
        if ('division' === $type) {
            $idFilter = 1;
        } elseif ('country' === $type) {
            $idFilter = 2;
        } else {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => false,
                'data' => [],
            ]);
        }
        $total = 0;
        $category = $this->em->getRepository(FilterCategory::class)->find($idFilter);
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

        $usersByFilter = $userCourseRepository->getSegmentedCountryOrDivision($conditions, $idFilter);

        foreach ($usersByFilter as $filter) {
            $data[$category->getId()][] = [
                'name' => $filter['name'],
                'count' => $filter['total'],
            ];
            $total += $filter['total'];
        }

        $data = empty($usersByFilter) ? [] : $data[$idFilter];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/personAndGroupBy/{type}", name="stats-segments-person-group--by-division", methods={"POST"})
     * */
    public function segmentedPersonDivisionGroup($type, UserCourseRepository $userCourseRepository): Response
    {
        if ('structure' === $type) {
            $idFilter = 1;
        } elseif ('hotel' === $type) {
            $idFilter = 2;
        } else {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => false,
                'data' => [],
            ]);
        }
        $category = $this->em->getRepository(FilterCategory::class)->find($idFilter);
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

        $usersByFilter = $userCourseRepository->getStructureOrDepartmentGeneral($conditions, $idFilter);

        foreach ($usersByFilter as $filter) {
            $usersByFilterDivi1 = $userCourseRepository
                ->getStructureOrDepartmentDetail($conditions, $idFilter, 'AMES', $filter['id']);
            $usersByFilterDivi2 = $userCourseRepository
                ->getStructureOrDepartmentDetail($conditions, $idFilter, 'EMEA', $filter['id']);

            $data[$category->getId()][] = [
                'name' => $filter['name'],
                'ames' => $usersByFilterDivi1[0]['total'],
                'emea' => $usersByFilterDivi2[0]['total'],
            ];
        }

        $data = empty($usersByFilter) ? [] : $data[$idFilter];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Route("/admin/stats/segmented/course", name="stats-segmented-course-finish")
     *
     * @return Response
     */
    public function segmentedCourse(UserCourseRepository $userCourseRepository)
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
        $startDate = $this->datesStatsSegments()['startDate'];
        $endDate = $this->datesStatsSegments()['endDate'];
        $data = [];
        $iterationCount = 0;

        for ($currentDate = $startDate; $currentDate < $endDate; $currentDate = strtotime('+1 month', $currentDate)) {
            $monthData = date('m', $currentDate);
            $yearData = date('Y', $currentDate);
            ++$iterationCount;

            $lastDayOfMonth = date('t', $currentDate);

            if (1 == $iterationCount) {
                $conditions['dateFrom'] = date('Y-m-d H:i:s', $currentDate);
            } else {
                $conditions['dateFrom'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-01 00:00:00"));
            }

            if ($currentDate + 2678400 >= $endDate) { // 2678400 segundos = 31 días
                $conditions['dateTo'] = date('Y-m-d H:i:s', $endDate);
            } else {
                $conditions['dateTo'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-{$lastDayOfMonth} 23:59:59"));
            }

            $bdData = $userCourseRepository->getCountCoursesFinished($conditions);
            $conditionAcumulate = $conditions;
            $conditionAcumulate['dateFrom'] = date('Y-m-d H:i:s', $startDate);
            $conditionAcumulate['dateTo'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-{$lastDayOfMonth} 23:59:59"));

            $bdDataAccumulate = $userCourseRepository->getCountCoursesFinished($conditionAcumulate);

            $data['started'][$monthData . '/' . $yearData] = [
                'period' => $bdData,
                'total' => $bdDataAccumulate,
            ];

            $data['finished'][$monthData . '/' . $yearData] = [
                'period' => $bdData,
                'total' => $bdDataAccumulate,
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Route("/admin/stats/segmented/course/new-finished", name="stats-started-course-new")
     *
     * @return Response
     */
    public function segmentedUserCoursesNewFinishedInPlatform(UserCourseRepository $userCourseRepository)
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
        $startDate = $this->datesStatsSegments()['startDate'];
        $endDate = $this->datesStatsSegments()['endDate'];

        $data = [];
        $acumulate = 0;
        $iterationCount = 0;

        for ($currentDate = $startDate; $currentDate <= $endDate; $currentDate = strtotime('+1 month', $currentDate)) {
            $monthData = date('m', $currentDate);
            $yearData = date('Y', $currentDate);
            ++$iterationCount;

            $lastDayOfMonth = date('t', $currentDate);

            if (1 == $iterationCount) {
                $conditions['dateFrom'] = date('Y-m-d H:i:s', $currentDate);
            } else {
                $conditions['dateFrom'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-01 00:00:00"));
            }

            if ($currentDate + 2678400 >= $endDate) { // 2678400 segundos = 31 días
                $conditions['dateTo'] = date('Y-m-d H:i:s', $endDate);
            } else {
                $conditions['dateTo'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-{$lastDayOfMonth} 23:59:59"));
            }

            $bdData = $userCourseRepository->getCountUserWhitCoursesFinished($conditions);
            $conditionAcumulate = $conditions;
            $conditionAcumulate['dateFrom'] = date('Y-m-d H:i:s', $startDate);
            $conditionAcumulate['dateTo'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-{$lastDayOfMonth} 23:59:59"));
            $acumulate += $bdData;

            $data['finished'][$monthData . '/' . $yearData] = [
                'period' => $bdData,
                'total' => $acumulate,
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Route("/admin/stats/segmented/course/started", name="stats-started-course")
     *
     * @return Response
     */
    public function segmentedCourseStarted(UserCourseRepository $userCourseRepository)
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
        $startDate = $this->datesStatsSegments()['startDate'];
        $endDate = $this->datesStatsSegments()['endDate'];

        $data = [];
        $iterationCount = 0;

        for ($currentDate = $startDate; $currentDate <= $endDate; $currentDate = strtotime('+1 month', $currentDate)) {
            $monthData = date('m', $currentDate);
            $yearData = date('Y', $currentDate);
            ++$iterationCount;

            $lastDayOfMonth = date('t', $currentDate);

            if (1 == $iterationCount) {
                $conditions['dateFrom'] = date('Y-m-d H:i:s', $currentDate);
            } else {
                $conditions['dateFrom'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-01 00:00:00"));
            }

            if ($currentDate + 2678400 >= $endDate) { // 2678400 segundos = 31 días
                $conditions['dateTo'] = date('Y-m-d H:i:s', $endDate);
            } else {
                $conditions['dateTo'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-{$lastDayOfMonth} 23:59:59"));
            }

            $bdData = $userCourseRepository->getCountCoursesStarted($conditions);
            $conditionAcumulate = $conditions;
            $conditionAcumulate['dateFrom'] = date('Y-m-d H:i:s', $startDate);
            $conditionAcumulate['dateTo'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-{$lastDayOfMonth} 23:59:59"));

            $bdDataAccumulate = $userCourseRepository->getCountCoursesStarted($conditionAcumulate);

            $data['started'][$monthData . '/' . $yearData] = [
                'period' => $bdData,
                'total' => $bdDataAccumulate,
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    private function datesStatsSegments()
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

        if ($conditions && ($conditions['dateTo'] && $conditions['dateFrom']) && ($conditions['dateTo'] > $conditions['dateFrom'])) {
            $startDate = strtotime($conditions['dateFrom']);
            $endDate = strtotime($conditions['dateTo']);
        } else {
            $startDate = '2021-01-03';
            $startDate = strtotime($startDate);
            $endDate = strtotime(date('Y-m-d'));
        }

        return [
            'startDate' => $startDate,
            'endDate' => $endDate,
        ];
    }

    /**
     * @Route("/admin/stats/segmented/course/started-in-process", name="stats-started-in-process")
     *
     * @return Response
     */
    public function segmentedCourseInProcess(UserCourseRepository $userCourseRepository)
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
        $startDate = $this->datesStatsSegments()['startDate'];
        $endDate = $this->datesStatsSegments()['endDate'];

        $data = [];
        $iterationCount = 0;

        for ($currentDate = $startDate; $currentDate < $endDate; $currentDate = strtotime('+1 month', $currentDate)) {
            $monthData = date('m', $currentDate);
            $yearData = date('Y', $currentDate);
            ++$iterationCount;

            $lastDayOfMonth = date('t', $currentDate);

            if (1 == $iterationCount) {
                $conditions['dateFrom'] = date('Y-m-d H:i:s', $currentDate);
            } else {
                $conditions['dateFrom'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-01 00:00:00"));
            }

            if ($currentDate + 2678400 >= $endDate) { // 2678400 segundos = 31 días
                $conditions['dateTo'] = date('Y-m-d H:i:s', $endDate);
            } else {
                $conditions['dateTo'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-{$lastDayOfMonth} 23:59:59"));
            }

            // CAPPED
            $bdData = $userCourseRepository->getCountCoursesInProcess($conditions);
            $conditionAcumulate = $conditions;
            $conditionAcumulate['dateFrom'] = date('Y-m-d H:i:s', $startDate);
            $conditionAcumulate['dateTo'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-{$lastDayOfMonth} 23:59:59"));

            // CAPPED
            $bdDataAccumulate = $userCourseRepository->getCountCoursesInProcess($conditionAcumulate);

            $data['started'][$monthData . '/' . $yearData] = [
                'period' => $bdData,
                'total' => $bdDataAccumulate,
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/course/startedVsFinished", name="stats-segmented-course-startVsFinish", methods={"POST"})
     * */
    public function segmentedCourseStartVsFinish(UserCourseRepository $userCourseRepository): Response
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
        $data = $userCourseRepository->getSegmentedAndGroupBy($conditions);

        $dataDetail[] = [
            'started' => $data[0]['total'],
            'finished' => $data[1]['total'],
        ];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $dataDetail,
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/course/country", name="stats-segmented-course-country", methods={"POST"})
     * */
    public function segmentedCourseCountry(UserCourseRepository $userCourseRepository): Response
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
        $dataStarted = $userCourseRepository->getSegmentedCourseCountry($conditions, 1);
        $dataFinish = $userCourseRepository->getSegmentedCourseCountry($conditions, 2);

        $dataDetail[] = [
            'started' => $dataStarted,
            'finished' => $dataFinish,
        ];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $dataDetail,
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/course/total", name="stats-segmented-course-total", methods={"POST"})
     * */
    public function segmentedCourseCountryTotal(UserCourseRepository $userCourseRepository): Response
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
        $dataStarted = $userCourseRepository->getSegmentedCourseCountryTotal($conditions, 1);
        $dataFinish = $userCourseRepository->getSegmentedCourseCountryTotal($conditions, 2);

        $dataDetail[] = [
            'started' => $dataStarted[0]['total'],
            'finished' => $dataFinish[0]['total'],
        ];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $dataDetail,
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/courseGroupBy/{type}", name="stats-segmented-groupby-strucyure-hotel", methods={"POST"})
     * */
    public function segmentedCourseGroupByStrutureHotel($type, UserCourseRepository $userCourseRepository): Response
    {
        if ('structure' === $type) {
            $idFilter = 1;
        } elseif ('hotel' === $type) {
            $idFilter = 2;
        } else {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => false,
                'data' => [],
            ]);
        }

        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
        $dataStarted = $userCourseRepository->getSegmentedCourseGroupByStructureHotel($conditions, 'F', $idFilter);
        $dataFinish = $userCourseRepository->getSegmentedCourseGroupByStructureHotel($conditions, 'V', $idFilter);

        $dataDetail[] = [
            'started' => $dataStarted,
            'finished' => $dataFinish,
        ];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $dataDetail,
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/hour", name="segmented-hour-acumulative", methods={"POST","GET"})
     * */
    public function segmentedHourAcumulative(UserCourseRepository $userCourseRepository)
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
        $startDate = $this->datesStatsSegments()['startDate'];
        $endDate = $this->datesStatsSegments()['endDate'];
        $data = [];
        $iterationCount = 0;

        for ($currentDate = $startDate; $currentDate < $endDate; $currentDate = strtotime('+1 month', $currentDate)) {
            $monthData = date('m', $currentDate);
            $yearData = date('Y', $currentDate);
            ++$iterationCount;

            $endDay = date('j', $endDate);
            $endMonth = date('m', $endDate);
            $endYear = date('Y', $endDate);

            $lastDayOfMonth = date('t', $currentDate);

            if (1 == $iterationCount) {
                $conditions['dateFrom'] = date('Y-m-d H:i:s', $currentDate);
            } else {
                $conditions['dateFrom'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-01 00:00:00"));
            }

            // if ($currentDate + 2678400 >= $endDate) { // 2678400 segundos = 31 días
            if (($yearData == $endYear) && ($monthData == $endMonth) && ($lastDayOfMonth >= $endDay)) {
                // $conditions['dateTo'] = date("Y-m-d H:i:s", $endDate);
                $conditions['dateTo'] = date('Y-m-d H:i:s', strtotime("{$endYear}-{$endMonth}-{$endDay} 23:59:59"));
            } else {
                $conditions['dateTo'] = date('Y-m-d H:i:s', strtotime("{$yearData}-{$monthData}-{$lastDayOfMonth} 23:59:59"));
            }

            $result = $userCourseRepository->getSegmentedHourGeneral($conditions);
            $bdData = $result['result'];
            $query_text = $result['query'];

            $conditionAcumulate = $conditions;
            $conditionAcumulate['dateFrom'] = date('Y-m-d H:i:s', $startDate);
            // $conditionAcumulate['dateTo'] = date("Y-m-d H:i:s", strtotime("{$yearData}-{$monthData}-{$lastDayOfMonth} 23:59:59"));
            $conditionAcumulate['dateTo'] = $conditions['dateTo'];

            $acum_result = $userCourseRepository->getSegmentedHourGeneral($conditionAcumulate);
            $bdDataAccumulate = $acum_result['result'];
            $acum_query_text = $acum_result['query'];

            $horas = ceil(ceil($bdData[0]['horas'] * 10000) / 10000);
            $horasAcumulate = ceil(ceil($bdDataAccumulate[0]['horas'] * 10000) / 10000);

            $data[$monthData . '/' . $yearData] = [
                'period' => $horas,
                'total' => $horasAcumulate,
                'period_raw' => $bdData[0]['horas'],
                'total_raw' => $bdDataAccumulate[0]['horas'],
                'currentDate' => date('Y-m-d H:i:s', $currentDate),
                'dateFrom' => $conditions['dateFrom'],
                'dateTo' => $conditions['dateTo'],
                // DEBUG 'query'     => $query_text,
                'Acum_dateFrom' => $conditionAcumulate['dateFrom'],
                'Acum_dateTo' => $conditionAcumulate['dateTo'],
                // DEBUG 'Acum_query'     => $acum_query_text,
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/hour/{type}", name="segmented-hour-structure-hotel", methods={"POST"})
     * */
    public function segmentedHourStructureVsHotel($type, UserCourseRepository $userCourseRepository): Response
    {
        if ('structureVsHotel' === $type) {
            $idFilter = 6;
        } elseif ('division' === $type) {
            $idFilter = 1;
        } else {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => false,
                'data' => [],
            ]);
        }

        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

        $result = $userCourseRepository->getSegmentedHourStructureVsHotel($conditions, $idFilter);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $result,
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/hour/groupBy/{type}", name="segmented-hour-group-structure", methods={"POST"})
     * */
    public function segmentedAccessGroupStructure($type, UserCourseRepository $userCourseRepository): Response
    {
        if ('structure' === $type) {
            $idFilter = 1;
        } elseif ('hotel' === $type) {
            $idFilter = 2;
        } else {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => false,
                'data' => [],
            ]);
        }
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

        $result = $userCourseRepository->getSegmentedGroupStructureHotel($conditions, $idFilter);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $result,
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/hour/total/country", name="segmented-hourby-country", methods={"POST"})
     * */
    public function segmentedHourByCountry(UserCourseRepository $userCourseRepository): Response
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $userCourseRepository->getSegmentedHourCountry($conditions),
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/hour/total/general", name="segmented-hour-total", methods={"POST"})
     * */
    public function segmentedHourTotal(UserCourseRepository $userCourseRepository): Response
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $userCourseRepository->getSegmentedHourTotal($conditions),
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/hour/total/school", name="segmented-hour-school", methods={"POST"})
     * */
    public function segmentedHourBySchool(UserCourseRepository $userCourseRepository): Response
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $userCourseRepository->getSegmentedHourSchool($conditions),
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/access/acumulative", name="segmented-access-acumulative", methods={"POST"})
     * */
    public function segmentedAccessAcumulative(UserCourseRepository $userCourseRepository): Response
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();
        $startDate = $this->datesStatsSegments()['startDate'];
        $endDate = $this->datesStatsSegments()['endDate'];

        $data = [];
        $past = 0;
        do {
            $queryDate = date('Y-m-d H:i:s', strtotime('+1 month', $startDate));
            $monthDate = date('m', strtotime('+1 month', $startDate));
            $yearDate = date('Y', strtotime('+1 month', $startDate));

            $monthData = $monthDate - 1;
            $monthData = (0 == $monthData) ? 12 : $monthData;
            $yearData = (12 == $monthData) ? $yearDate - 1 : $yearDate;

            $access = $userCourseRepository->getAcumulativeAccess($conditions, $monthDate, $yearDate);

            $data[$monthData . '/' . $yearData] = [
                'period' => $access[0]['total'] - $past,
                'total' => $access[0]['total'],
            ];

            $past = $access[0]['total'];

            $startDate = strtotime($queryDate);
        } while (strtotime($queryDate) < $endDate);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/access/total", name="segmented-access-total", methods={"POST"})
     * */
    public function segmentedAccessTotal(UserCourseRepository $userCourseRepository): Response
    {
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $userCourseRepository->getSegmentedAccessTotal($conditions),
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/access/groupBy/{type}", name="segmented-access-group-by", methods={"POST"})
     * */
    public function segmentedAccessGroupDivision($type, UserCourseRepository $userCourseRepository): Response
    {
        if ('division' === $type) {
            $idFilter = 1;
        } elseif ('country' === $type) {
            $idFilter = 2;
        } elseif ('structureVsHotel' === $type) {
            $idFilter = 6;
        } else {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => false,
                'data' => [],
            ]);
        }
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $userCourseRepository->getSegmentedAccessGroupBy($conditions, $idFilter),
        ]);
    }

    /**
     * @Route ("/admin/stats/segmented/access/groupByDepartment/{type}", name="segmented-access-groupBy-department", methods={"POST"})
     * */
    public function segmentedAccessByGroupStructure($type, UserCourseRepository $userCourseRepository): Response
    {
        if ('structure' === $type) {
            $idFilter = 1;
        } elseif ('hotel' === $type) {
            $idFilter = 2;
        } else {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => false,
                'data' => [],
            ]);
        }
        $conditions = empty($this->getConditions()) ? [] : $this->getConditions();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $userCourseRepository
                ->getSegmentedAccessGroupByStructureHotel($conditions, $idFilter),
        ]);
    }

    /**
     * @Route ("/admin/stats/courses/started-finished", name="admin_stats_courses_started_finished")
     */
    public function coursesStartedAndFinished(UserCourseRepository $userCourseRepository): Response
    {
        $conditions = $this->getConditions();

        $data = [
            'started' => $userCourseRepository->getCountCoursesStarted($conditions),
            'in_process' => $userCourseRepository->getCountCoursesInProcess($conditions),
            'finished' => $userCourseRepository->getCountCoursesFinished($conditions),
        ];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Route("/admin/stats/courseBySchool", name="admin_stats_course_by_school")
     */
    public function courseBySchool(CourseRepository $courseRepository): Response
    {
        try {
            $conditions = $this->getConditions();
            $data = $courseRepository->top10courseBySchool($conditions);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data,
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route("/admin/stats/schoolFinishedAndProgress", name="admin_stats_school_finished_and_progress")
     */
    public function schoolFinishedAndProgress(Request $request): Response
    {
        try {
            $conditions = $this->getConditions();
            $courseRepository = $this->em->getRepository(Course::class);
            $data = $courseRepository->schoolFinishedAndProgress('', $conditions);

            foreach ($data as &$valor) {
                $schoolFinishedAndProgress = $courseRepository
                    ->schoolFinishedAndProgress(
                        $valor['name'],
                        $conditions
                    );
                $valor['finished'] = $schoolFinishedAndProgress[0]['finished'] ?? 0;
                $valor['inCourse'] = $valor['count'] - $valor['finished'];
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data,
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route("/admin/stats/courseByPersonGroup", name="course-by-person-group")
     */
    public function courseByPersonGroup(Request $request): Response
    {
        try {
            $conditions = \is_null($this->getConditions()) ? [] : $this->getConditions();

            $repository = $this->em->getRepository(UserCourse::class);

            $data = $repository->getGroupCourseByPersons('poco', $conditions);
            $poco = 0;
            foreach ($data as $item) {
                $poco += $item['total'];
            }

            $data = $repository->getGroupCourseByPersons('medio', $conditions);
            $medio = 0;
            foreach ($data as $item) {
                $medio += $item['total'];
            }

            $data = $repository->getGroupCourseByPersons('bastante', $conditions);
            $bastante = 0;
            foreach ($data as $item) {
                $bastante += $item['total'];
            }

            $data = $repository->getGroupCourseByPersons('muy', $conditions);
            $muy = 0;
            foreach ($data as $item) {
                $muy += $item['total'];
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    ['name' => 'Poco formadas (0-1)', 'value' => $poco],
                    ['name' => 'Medio formadas (2-5)', 'value' => $medio],
                    ['name' => 'Bastante formadas (6-9)', 'value' => $bastante],
                    ['name' => 'Muy formadas >10', 'value' => $muy],
                ],
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Route("/admin/stats/courseAndStarts", name="course-sum-stars")
     */
    public function courseAndStarts(Request $request): Response
    {
        try {
            $conditions = $this->getConditions(); // fixfer
            $npsRepository = $this->em->getRepository(Nps::class);

            $rankingCourses = $npsRepository->getStarsGroupRanking('DESC', $conditions, 10);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [$rankingCourses],
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }
}
