<?php

declare(strict_types=1);

namespace App\StatsAndExcel\Services;

use App\Service\SettingsService;
use App\StatsAndExcel\Enum\StatsLiteMaskEnum;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class StatsLiteService
{
    private EntityManagerInterface $em;
    private SettingsService $settings;
    private LoggerInterface $logger;
    protected TranslatorInterface $translator;
    protected RequestStack $requestStack;
    private Security $security;

    private int $statsLiteFormationConfig;
    private array $statsFormationSettings;
    private array $statsEvolutionSettings;
    private array $statsDemographySettings;
    private array $statsActivitySettings;
    private array $statsItinerarySettings;

    private string $locale;

    public function __construct(EntityManagerInterface $em, SettingsService $settings, LoggerInterface $logger, TranslatorInterface $translator, Security $security, RequestStack $requestStack)
    {
        $this->em = $em;
        $this->settings = $settings;
        $this->logger = $logger;
        $this->translator = $translator;
        $this->security = $security;
        $this->requestStack = $requestStack;

        $this->statsLiteFormationConfig = 0;

        $this->initFormationSettings();
        $this->initEvolutionSettings();
        $this->initDemographySettings();
        $this->initActivitySettings();
        $this->initItinerarySettings();
    }

    protected function getLocale()
    {
        if (!isset($this->locale)) {
            if (!$this->security->getUser()) {
                $this->locale = $this->settings->get('app.defaultLanguage');
            } else {
                $this->locale = $this->security->getUser()->getLocale();
            }
        }

        return $this->locale;
    }

    protected function getGroupSettingsConfiguration(string $groupSettingsName): array
    {
        $chartData = [];
        $groupSettings = $this->settings->getSettingOptions($groupSettingsName);

        foreach ($groupSettings as $groupChartSetting) {
            $this->logger->debug('SETTING ' . $groupChartSetting);
            $chartSettingValue = $this->settings->get($groupChartSetting);
            if (StatsLiteMaskEnum::GROUP_SETTINGS_VAL_TRUE == $chartSettingValue) {
                $chartCode = str_replace($groupSettingsName . StatsLiteMaskEnum::GROUP_SETTINGS_SEPARATOR, '', $groupChartSetting);
                $chartData[] = $chartCode;
                $this->logger->debug(' ENABLED');
            } else {
                $this->logger->debug(' DISABLED');
            }
        }

        return $chartData;
    }

    protected function initFormationSettings()
    {
        $this->statsFormationSettings = [];

        $this->logger->debug('initFormationSettings');

        $this->statsFormationSettings = $this->getGroupSettingsConfiguration(StatsLiteMaskEnum::GROUP_SETTINGS_FORMATION_NAME);

        $this->logger->debug(implode(';', $this->statsFormationSettings));
    }

    protected function initEvolutionSettings()
    {
        $this->statsEvolutionSettings = [];

        $this->logger->debug('initEvolutionSettings');

        $this->statsEvolutionSettings = $this->getGroupSettingsConfiguration(StatsLiteMaskEnum::GROUP_SETTINGS_EVOLUTION_NAME);

        $this->logger->debug(implode(';', $this->statsEvolutionSettings));
    }

    protected function initDemographySettings()
    {
        $this->statsDemographySettings = [];

        $this->logger->debug('initDemographySettings');

        $this->statsDemographySettings = $this->getGroupSettingsConfiguration(StatsLiteMaskEnum::GROUP_SETTINGS_DEMOGRAPHY_NAME);

        $this->logger->debug(implode(';', $this->statsDemographySettings));
    }

    protected function initActivitySettings()
    {
        $this->statsActivitySettings = [];

        $this->logger->debug('initActivitySettings');

        $this->statsActivitySettings = $this->getGroupSettingsConfiguration(StatsLiteMaskEnum::GROUP_SETTINGS_ACTIVITY_NAME);

        $this->logger->debug(implode(';', $this->statsActivitySettings));
    }

    protected function initItinerarySettings()
    {
        $this->statsItinerarySettings = [];

        $this->logger->debug('initItinerarySettings');

        $this->statsItinerarySettings = $this->getGroupSettingsConfiguration(StatsLiteMaskEnum::GROUP_SETTINGS_ITINERARY_NAME);

        $this->logger->debug(implode(';', $this->statsItinerarySettings));
    }

    public function getFormationSettings()
    {
        return $this->statsFormationSettings;
    }

    public function getEvolutionSettings()
    {
        return $this->statsEvolutionSettings;
    }

    public function getDemographySettings()
    {
        return $this->statsDemographySettings;
    }

    public function getActivitySettings()
    {
        return $this->statsActivitySettings;
    }

    public function getItinerarySettings()
    {
        return $this->statsItinerarySettings;
    }
}
