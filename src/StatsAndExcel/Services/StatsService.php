<?php

declare(strict_types=1);

namespace App\StatsAndExcel\Services;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\User;
use App\Service\SettingsService;
use App\StatsAndExcel\Enum\StatsCustomFiltersEnum;
use App\StatsAndExcel\Enum\StatsExcelEnum;
use App\StatsAndExcel\Enum\StatsExportEnum;
use App\StatsAndExcel\Enum\StatsFiltersEnum;
use App\Utils\CharactersCurationUtils;
use App\Utils\ExportExtraFieldsHelper;
use App\Utils\TimeUtils;
use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\MemoryDrawing;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class StatsService
{
    private SettingsService $settings;
    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    protected TranslatorInterface $translator;
    protected RequestStack $requestStack;
    private Security $security;

    private array $filterMapTranslations = [];

    private string $locale;

    private Spreadsheet $spreadSheet;

    public function __construct(SettingsService $settings, EntityManagerInterface $em, LoggerInterface $logger, TranslatorInterface $translator, Security $security, RequestStack $requestStack)
    {
        $this->settings = $settings;
        $this->em = $em;
        $this->logger = $logger;
        $this->translator = $translator;
        $this->security = $security;
        $this->requestStack = $requestStack;
        $this->spreadSheet = new Spreadsheet();
    }

    public function getSpreadsheet()
    {
        return $this->spreadSheet;
    }

    protected function getLocale()
    {
        if (!isset($this->locale)) {
            if (!$this->security->getUser()) {
                $this->locale = $this->settings->get('app.defaultLanguage');
            } else {
                $this->locale = $this->security->getUser()->getLocale();
            }
        }

        return $this->locale;
    }

    protected function generateExcelHeaders($dataHeader, $filterCategory): array
    {
        if (null == $dataHeader) {
            $categories = [];

            foreach ($filterCategory as $filCat) {
                array_push(
                    $categories,
                    $filCat->getName(),
                );
            }

            $dataHeader = StatsExportEnum::DEFAULT_HEADER_PREFIX;

            if ($this->settings->get('app.export.gender_excel')) {
                $dataHeader = array_merge($dataHeader, [StatsExportEnum::DEFAULT_HEADER_FIELD_GENDER]);
            }

            if (StatsExportEnum::EXPORT_CLIENT_IBEROSTAR == $this->settings->get('app.fromName')) {
                $dataHeader = array_merge($dataHeader, StatsExportEnum::DEFAULT_HEADER_INBETWEEN, $categories, StatsExportEnum::DEFAULT_HEADER_SUFFIX_IBEROSTAR);
            } else {
                $dataHeader = array_merge($dataHeader, StatsExportEnum::DEFAULT_HEADER_INBETWEEN, $categories, StatsExportEnum::DEFAULT_HEADER_SUFFIX);
            }

            if (StatsExportEnum::EXPORT_CLIENT_IBEROSTAR == $this->settings->get('app.fromName')) {
                $dataHeader = array_merge($dataHeader, StatsExportEnum::DEFAULT_HEADER_IBERO);
            }
        }

        return $dataHeader;
    }

    public function generateExcelSheet($conditions, $dataItems, $filename = StatsExportEnum::FILENAME, $dataHeader = null, $mode = StatsExportEnum::MODE_DEFAULT, array $extraFieldsDataHeader = []): string
    {
        $filterCategory = $this->em->getRepository(FilterCategory::class)->findAll();
        $filters = $this->em->getRepository(Filter::class)->findAll();

        $dataHeader = $this->generateExcelHeaders($dataHeader, $filterCategory);
        $dataHeader = array_merge($dataHeader, $extraFieldsDataHeader);

        /* EXPORT CONFIG DATA - DASHBOARD - STATS DATA CONFIG */
        $title = null;
        $this->generateExcelConfigSheet($this->spreadSheet, $title, $conditions, $filters);
        // $this->generateExcelConfigSheet_OLD( $this->spreadSheet, $conditions, $filters);
        /* EXPORT CONFIG DATA - DASHBOARD - STATS DATA EXPORT */
        $this->generateExcelDataSheet($this->spreadSheet, $conditions, $dataItems, $dataHeader);
        /* EXPORT WRITE TO FILE */
        $result = $this->writeExcelToFile($filename);

        if (!$result) {
            $this->logger->debug(serialize('generateExcelSheet KO'));
        }

        return $result;
    }

    public function setupExportDataItems($userDataItems, $npsRepository, $filterCategory, $extraFieldsDataHeaders = null, $locale = null, $settingsExtraFields = null): array
    {
        $filterRepository = $this->em->getRepository(Filter::class);

        $newUserData = [];

        foreach ($userDataItems as $userDataItem) {
            $debug = '';

            $userTypeNps = '-';
            $userTypeOpinion = '-';
            $userId = $userDataItem['userId'];
            $userFind = $this->em->getRepository(User::class)->findWithDeleted($userId);
            if (!$userFind) {
                $this->logger->error('setupExportDataItems: user null ' . $userId);
                continue;
            }
            $usersNps = $npsRepository->findBy(['course' => $userDataItem['userCourseId'], 'user' => $userDataItem['userId'], 'main' => 1]);

            foreach ($filterCategory as $filCat) {
                $filtersUser = $filterRepository->fetchFiltersUsersAll($userFind->getId(), $filCat->getId());
                if ('' != $filtersUser) {
                    $data = '';
                    foreach ($filtersUser as $filterResult) {
                        $data .= $filterResult['name'] . StatsFiltersEnum::CATEGORY_SEPARATOR;
                    }
                    // remove separator if found
                    $lastSeparatorPosition = strrpos($data, StatsFiltersEnum::CATEGORY_SEPARATOR);
                    if (false !== $lastSeparatorPosition && $lastSeparatorPosition == (\strlen($data) - 1)) {
                        $data = substr($data, 0, -1);
                    }
                    $userDataItem['filtro_' . $filCat->getId()] = $data;
                }
            }

            if (StatsExportEnum::EXPORT_CLIENT_IBEROSTAR == $this->settings->get('app.fromName')) {
                $userDataItem['Company name'] = $userDataItem['companyName'];
            }

            $userDataItem['startedAt_'] = $userDataItem['startedAt'];
            $userDataItem['finishedAt_'] = $userDataItem['finishedAt'];
            $userDataItem['valuedAt_'] = $userDataItem['valuedAt'];
            $userDataItem['timeSpent_'] = TimeUtils::timeUserInTheCOurse($userDataItem['timeSpent']);

            if ($usersNps) {
                foreach ($usersNps as $userNps) {
                    if ('nps' === $userNps->getType()) {
                        $userTypeNps = $userNps->getValue();
                    } else {
                        $userTypeOpinion = $userNps->getValue();
                    }
                }
            }
            $userDataItem['nps'] = $userTypeNps;
            $userDataItem['opinion'] = CharactersCurationUtils::removeExcelFormulaPrefixFromText($userTypeOpinion);
            if (!$this->settings->get('app.export.gender_excel')) {
                unset($userDataItem['gender']);
            }
            if (StatsExportEnum::EXPORT_CLIENT_IBEROSTAR == $this->settings->get('app.fromName')) {
                $userDataItem['nif_'] = $userDataItem['nif'];
                $userDataItem['hrp_'] = $userDataItem['hrp'] ? str_replace('"', '', $userDataItem['hrp']) : '';
            }

            unset($userDataItem['userCourseId']);
            unset($userDataItem['startedAt']);
            unset($userDataItem['finishedAt']);
            unset($userDataItem['valuedAt']);
            unset($userDataItem['timeSpent']);
            unset($userDataItem['companyName']);
            unset($userDataItem['nif']);
            unset($userDataItem['hrp']);
            unset($userDataItem['employeer']);

            try {
                $userDataItem = array_merge(
                    $userDataItem,
                    ExportExtraFieldsHelper::getExportExtraFieldsValues(
                        $settingsExtraFields,
                        $userFind->getMetaValueByLabel('extraFields') ?? [],
                        $extraFieldsDataHeaders,
                        $locale
                    )
                );
            } catch (\UnexpectedValueException $e) {
                // Silent pass. This is a non-critical error.
                $this->logger->error($e->getMessage(), $userId);
            }

            array_push($newUserData, $userDataItem);
        }

        // return $userDataItems;
        return $newUserData;
    }

    private function setUserExtraFields(&$userDataItem, &$extraFieldsDataHeaders): void
    {
        $userRepository = $this->em->getRepository(User::class);
        $user = $userRepository->findBy(['id' => $userDataItem['userId']])[0];

        $settingsExtraFields = json_decode($this->settings->getSetting('app.user.extra_fields'), true) ?? [];

        $extraFieldsDataHeaders = ExportExtraFieldsHelper::getExportHeaders($settingsExtraFields, $this->getLocale());
        $userMeta = $user->getMetaValueByLabel('extraFields') ?? [];
        $exportExtraFieldsValues = ExportExtraFieldsHelper::getExportExtraFieldsValues($settingsExtraFields, $userMeta, $extraFieldsDataHeaders, $this->getLocale());

        foreach ($exportExtraFieldsValues as $key => $value) {
            $userDataItem[$key] = $value ?? '';
        }
    }

    /**
     * @throws Exception
     */
    public function generateExcelDataSheet($spreadsheet, $conditions, $dataItems, $dataHeader)
    {
        $sheet = $spreadsheet->createSheet();
        $sheet->setTitle($this->translator->trans('stats.export.datasheet.title', [], 'messages', $this->getLocale()));

        $sheet->fromArray([$dataHeader]);
        $row = 1;
        foreach ($dataItems as $dataItem) {
            $sheet->fromArray([$dataItem], null, 'A' . ++$row);
        }

        foreach (range('A', 'Z') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        $this->setOtherColumnsAutoSize($sheet);
    }

    public function setupExcelConfigSheetFilterRow($sheetconfig, $columnIdx, $category_name, $interpreted_value)
    {
        $sheetconfig->setCellValue('D' . $columnIdx, $category_name);
        $sheetconfig->setCellValue('E' . $columnIdx, $interpreted_value);
    }

    public function setupExcelConfigSheetFilters($sheetconfig, $conditions, $filters = [])
    {
        $general_filters = [];

        foreach ($filters as $filter) {
            $filter_data = [];
            $filter_data[StatsFiltersEnum::ENTITY_NAME] = $filter->getName();
            $filter_data[StatsFiltersEnum::ENTITY_CATEGORY] = $filter->getFilterCategory();
            $general_filters[$filter->getId()] = $filter_data;
        }

        // FILTERS
        $this->initFilterMapTranslations($conditions);
        $columnIdx = 11;
        foreach ($conditions as $condition_name => $condition_value) {
            switch ($condition_name) {
                case StatsFiltersEnum::START_DATE:
                    $sheetconfig->setCellValue('E6', $condition_value);
                    break;
                case StatsFiltersEnum::END_DATE:
                    $sheetconfig->setCellValue('E7', $condition_value);
                    break;
                default:
                    $interpreted_value = $condition_value;

                    if ($this->existsFilterMapTranslation($condition_name . StatsFiltersEnum::VALUE_TRANSLATION_SEPARATOR . $condition_value)) {
                        $interpreted_value = $this->getFilterMapTranslation($condition_name . StatsFiltersEnum::VALUE_TRANSLATION_SEPARATOR . $condition_value);
                        $interpreted_value = $this->translator->trans($interpreted_value, [], 'messages', $this->getLocale());
                    }

                    if (StatsCustomFiltersEnum::PARAM_KEY == $condition_name) {
                        $interpreted_value = '';
                        $custom_filters = explode(',', $condition_value);
                        foreach ($custom_filters as $filterIdx) {
                            if (\array_key_exists($filterIdx, $general_filters)) {
                                $interpreted_value = $general_filters[$filterIdx][StatsCustomFiltersEnum::PARAM_NAME];
                                $category_name = $general_filters[$filterIdx][StatsCustomFiltersEnum::PARAM_CATEGORY];

                                // $sheetconfig->setCellValue('D' . $columnIdx  , $category_name);
                                // $sheetconfig->setCellValue('E' . $columnIdx  , $interpreted_value);
                                $this->setupExcelConfigSheetFilterRow($sheetconfig, $columnIdx, $category_name, $interpreted_value);
                            }
                            ++$columnIdx;
                        }
                    } else {
                        if (!empty($interpreted_value) && ('' != $interpreted_value) || (StatsFiltersEnum::ACTIVE_USERS == $condition_name)) {
                            // $sheetconfig->setCellValue('D' . $columnIdx  ,  $this->translator->trans( $this->getFilterMapTranslation($condition_name), [], 'messages', $this->getLocale() ));
                            // $sheetconfig->setCellValue('E' . $columnIdx  , $interpreted_value);
                            $category_name = $this->translator->trans($this->getFilterMapTranslation($condition_name), [], 'messages', $this->getLocale());

                            $this->setupExcelConfigSheetFilterRow($sheetconfig, $columnIdx, $category_name, $interpreted_value);

                            ++$columnIdx;
                        }
                    }
            }
        }
    }

    public function setupExcelConfigSheetDates($sheetconfig)
    {
        // DATES
        $sheetconfig->setCellValue('D6', $this->translator->trans('stats.export.configsheet.content_period_from', [], 'messages', $this->getLocale()));
        $sheetconfig->setCellValue('D7', $this->translator->trans('stats.export.configsheet.content_period_to', [], 'messages', $this->getLocale()));
        $sheetconfig->getStyle('E6:E7')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB('FFD9EAD3');
        $sheetconfig->getStyle('E6:E7')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    public function setupExcelConfigSheetStyles($sheetconfig)
    {
        $sheetconfig->getColumnDimension('E')->setWidth(80, 'pt');
        $sheetconfig->getStyle('F4')->getFont()->setSize(14);
        $sheetconfig->getStyle('F4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheetconfig->getStyle('F10')->getFont()->setSize(14);
        $sheetconfig->getStyle('F10')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheetconfig->getStyle('D11:E20')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * @throws Exception
     */
    public function setOtherColumnsAutoSize($sheet)
    {
        $startIndex = Coordinate::columnIndexFromString('AA');
        $endIndex = Coordinate::columnIndexFromString('AG');
        for ($col = $startIndex; $col <= $endIndex; ++$col) {
            $columnID = Coordinate::stringFromColumnIndex($col);
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }
    }

    public function setupExcelConfigSheetHeader($sheetconfig, $title = null)
    {
        if (null == $title) {
            $this->translator->trans('stats.export.configsheet.content_title', [], 'messages', $this->getLocale());
        }
        $sheetconfig->setCellValue('D2', $title);
        $sheetconfig->getStyle('D2')->getFont()->setSize(18);
        $sheetconfig->setCellValue('F4', $this->translator->trans('stats.export.configsheet.content_period', [], 'messages', $this->getLocale()));
        $sheetconfig->setCellValue('F10', $this->translator->trans('stats.export.configsheet.content_filters', [], 'messages', $this->getLocale()));
    }

    public function setupExcelConfigSheetTitle($sheetconfig, $title = null)
    {
        if (null == $title) {
            $title = $this->translator->trans('stats.export.configsheet.title', [], 'messages', $this->getLocale());
        }
        $sheetconfig->setTitle($title);
        $sheetconfig->insertNewRowBefore(1, 20);
    }

    public function generateExcelConfigSheet($spreadsheet, $title = null, $conditions = null, $filters = [])
    {
        $sheetconfig = $spreadsheet->getActiveSheet();

        // TITLE
        $this->setupExcelConfigSheetTitle($sheetconfig, $title);

        /* FIXED DATA - TEMPLATE */
        $this->setupExcelConfigSheetHeader($sheetconfig, $title);

        // LOGO IMAGE
        // $this->addLogoImageToExcelSheet( $sheetconfig);

        // STYLES
        $this->setupExcelConfigSheetStyles($sheetconfig);

        // DATES
        $this->setupExcelConfigSheetDates($sheetconfig);

        // FILTERS
        if (null != $conditions) {
            $this->setupExcelConfigSheetFilters($sheetconfig, $conditions, $filters);
        }
    }

    public function generateExcelConfigSheet_OLD($spreadsheet, $conditions, $filters)
    {
        $general_filters = [];

        foreach ($filters as $filter) {
            $filter_data = [];
            $filter_data[StatsFiltersEnum::ENTITY_NAME] = $filter->getName();
            $filter_data[StatsFiltersEnum::ENTITY_CATEGORY] = $filter->getFilterCategory();
            $general_filters[$filter->getId()] = $filter_data;
        }

        $sheetconfig = $spreadsheet->getActiveSheet();
        $sheetconfig->setTitle($this->translator->trans('stats.export.configsheet.title', [], 'messages', $this->getLocale()));

        $sheetconfig->insertNewRowBefore(1, 20);

        /* FIXED DATA - TEMPLATE */
        $sheetconfig->setCellValue('D2', $this->translator->trans('stats.export.configsheet.content_title', [], 'messages', $this->getLocale()));
        $sheetconfig->getStyle('D2')->getFont()->setSize(18);
        $sheetconfig->setCellValue('F4', $this->translator->trans('stats.export.configsheet.content_period', [], 'messages', $this->getLocale()));
        $sheetconfig->setCellValue('F10', $this->translator->trans('stats.export.configsheet.content_filters', [], 'messages', $this->getLocale()));

        // LOGO IMAGE
        // $this->addLogoImageToExcelSheet( $sheetconfig);

        // STYLES
        $sheetconfig->getColumnDimension('E')->setWidth(80, 'pt');
        $sheetconfig->getStyle('F4')->getFont()->setSize(14);
        $sheetconfig->getStyle('F4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheetconfig->getStyle('F10')->getFont()->setSize(14);
        $sheetconfig->getStyle('F10')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheetconfig->getStyle('D11:E20')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        // DATES
        $sheetconfig->setCellValue('D6', $this->translator->trans('stats.export.configsheet.content_period_from', [], 'messages', $this->getLocale()));
        $sheetconfig->setCellValue('D7', $this->translator->trans('stats.export.configsheet.content_period_to', [], 'messages', $this->getLocale()));
        $sheetconfig->getStyle('E6:E7')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB('FFD9EAD3');
        $sheetconfig->getStyle('E6:E7')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        // FILTERS
        $this->initFilterMapTranslations($conditions);
        $columnIdx = 11;
        foreach ($conditions as $condition_name => $condition_value) {
            switch ($condition_name) {
                case StatsFiltersEnum::START_DATE:
                    $sheetconfig->setCellValue('E6', $condition_value);
                    break;
                case StatsFiltersEnum::END_DATE:
                    $sheetconfig->setCellValue('E7', $condition_value);
                    break;
                default:
                    $interpreted_value = $condition_value;

                    if ($this->existsFilterMapTranslation($condition_name . StatsFiltersEnum::VALUE_TRANSLATION_SEPARATOR . $condition_value)) {
                        $interpreted_value = $this->getFilterMapTranslation($condition_name . StatsFiltersEnum::VALUE_TRANSLATION_SEPARATOR . $condition_value);
                        $interpreted_value = $this->translator->trans($interpreted_value, [], 'messages', $this->getLocale());
                    }

                    if (StatsCustomFiltersEnum::PARAM_KEY == $condition_name) {
                        $interpreted_value = '';
                        $custom_filters = explode(',', $condition_value);
                        foreach ($custom_filters as $filterIdx) {
                            if (\array_key_exists($filterIdx, $general_filters)) {
                                $interpreted_value = $general_filters[$filterIdx][StatsCustomFiltersEnum::PARAM_NAME];
                                $category_name = $general_filters[$filterIdx][StatsCustomFiltersEnum::PARAM_CATEGORY];

                                $sheetconfig->setCellValue('D' . $columnIdx, $category_name);
                                $sheetconfig->setCellValue('E' . $columnIdx, $interpreted_value);
                                $sheetconfig->getStyle('D' . $columnIdx)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                                $sheetconfig->getStyle('D' . $columnIdx)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setARGB('FFD9EAD3');
                            }
                            ++$columnIdx;
                        }
                    } else {
                        if (!empty($interpreted_value) && ('' != $interpreted_value) || (StatsFiltersEnum::ACTIVE_USERS == $condition_name)) {
                            $sheetconfig->setCellValue('D' . $columnIdx, $this->translator->trans($this->getFilterMapTranslation($condition_name), [], 'messages', $this->getLocale()));
                            $sheetconfig->setCellValue('E' . $columnIdx, $interpreted_value);
                            $sheetconfig->getStyle('D' . $columnIdx)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                            ++$columnIdx;
                        }
                    }
            }
        }
    }

    public function writeExcelToFile($filename): string
    {
        /* WRITE EXPORT TO FILE */
        $writer = new Xlsx($this->spreadSheet);
        $fileNameExt = "{$filename}.xlsx";
        $tempFile = tempnam(sys_get_temp_dir(), $fileNameExt);
        $writer->save($tempFile);

        return $tempFile;
    }

    private function initFilterMapTranslations($conditions)
    {
        $this->addFilterMapTranslations('category', 'stats.export.filter.category');
        $this->addFilterMapTranslations('departament', 'stats.export.filter.departament');
        $this->addFilterMapTranslations('gender', 'stats.export.filter.gender');
        $this->addFilterMapTranslations('activeUsers', 'stats.export.filter.activeUsers');
        $this->addFilterMapTranslations('activeUsers_val_1', 'stats.content_active');
        $this->addFilterMapTranslations('activeUsers_val_0', 'stats.content_inactive');
        $this->addFilterMapTranslations('course_full', 'stats.export.filter.course_full_title');
        $this->addFilterMapTranslations('course_full' . StatsFiltersEnum::VALUE_TRANSLATION_SEPARATOR . '1', 'stats.export.filter.course_full_val_yes');
        $this->addFilterMapTranslations('course_full' . StatsFiltersEnum::VALUE_TRANSLATION_SEPARATOR . '0', 'stats.export.filter.course_full_val_no');
        $this->addFilterMapTranslations('course_intime', 'stats.export.filter.course_intime_title');
        $this->addFilterMapTranslations('course_intime' . StatsFiltersEnum::VALUE_TRANSLATION_SEPARATOR . '1', 'stats.export.filter.course_intime_val_yes');
        $this->addFilterMapTranslations('course_intime' . StatsFiltersEnum::VALUE_TRANSLATION_SEPARATOR . '0', 'stats.export.filter.course_intime_val_no');
        $this->addFilterMapTranslations('courseStartedIntime', 'stats.export.filter.course_started_in_period_title');
        $this->addFilterMapTranslations('courseStartedIntime' . StatsFiltersEnum::VALUE_TRANSLATION_SEPARATOR . '1', 'stats.export.filter.course_started_in_period_val_yes');
        $this->addFilterMapTranslations('courseStartedIntime' . StatsFiltersEnum::VALUE_TRANSLATION_SEPARATOR . '0', 'stats.export.filter.course_started_in_period_val_no');
        $this->addFilterMapTranslations('courseFinishedIntime', 'stats.export.filter.course_finished_in_period_title');
        $this->addFilterMapTranslations('courseFinishedIntime' . StatsFiltersEnum::VALUE_TRANSLATION_SEPARATOR . '1', 'stats.export.filter.course_finished_in_period_val_yes');
        $this->addFilterMapTranslations('courseFinishedIntime' . StatsFiltersEnum::VALUE_TRANSLATION_SEPARATOR . '0', 'stats.export.filter.course_finished_in_period_val_no');
        $this->addFilterMapTranslations('customFilters', 'stats.export.filter.customFilters');
    }

    private function addFilterMapTranslations($filter_name, $translation_entry)
    {
        array_push($this->filterMapTranslations, $filter_name);
        $this->filterMapTranslations[$filter_name] = $translation_entry;
    }

    private function existsFilterMapTranslation($filter_name)
    {
        return \array_key_exists($filter_name, $this->filterMapTranslations);
    }

    private function getFilterMapTranslation($filter_name)
    {
        if (\array_key_exists($filter_name, $this->filterMapTranslations)) {
            return $this->filterMapTranslations[$filter_name];
        }

        return $filter_name;
    }

    public function addLogoImageToExcelSheet($sheetconfig)
    {
        // TO BE UPDATED WITH REFERENCE TO CLIENT/ENVIRONMENT LOGO SOURCE
        // $img_url = CONST_STATS_SERVICE_LOGO_GESTIONET_IMG_URL;

        // $img_url = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost() . StatsExcelEnum::CLIENT_IMG_URL_SUFIX;
        $img_url = 'https://gestionet.net/wp-content/uploads/2021/05/logo.png';

        if (isset($img_url) && !empty($img_url)) {
            $imageType = StatsExcelEnum::FILE_EXTENSION_PNG;
            if (false === strpos($img_url, '.' . StatsExcelEnum::FILE_EXTENSION_PNG)) {
                $imageType = StatsExcelEnum::FILE_EXTENSION_JPG;
            }

            $drawing = new MemoryDrawing();
            $gdImage = (StatsExcelEnum::FILE_EXTENSION_PNG == $imageType) ? imagecreatefrompng($img_url) : imagecreatefromjpeg($img_url);
            $imgWidth = imagesx($gdImage);
            $imgHeigth = imagesy($gdImage);

            // TO BE UPDATED WITH REFERENCE TO CLIENT/ENVIRONMENT NAME & DESCRIPTION
            $drawing->setName(StatsExcelEnum::IMG_ALT_TEXT);
            $drawing->setDescription(StatsExcelEnum::IMG_ALT_DESCRIPTION);
            $drawing->setResizeProportional(false);
            $drawing->setImageResource($gdImage);
            $drawing->setRenderingFunction(MemoryDrawing::RENDERING_JPEG);
            $drawing->setMimeType(MemoryDrawing::MIMETYPE_DEFAULT);
            $drawing->setWidth($imgWidth / 2);
            $drawing->setHeight($imgHeigth / 2);
            $drawing->setOffsetX(5);
            $drawing->setOffsetY(5);
            $drawing->setCoordinates('K1');
            $drawing->setWorksheet($sheetconfig);
        }
    }
}
