<?php

declare(strict_types=1);

namespace App\Modules\CategoryFilter\Services;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\FilterCategoryTranslation;
use App\Entity\FilterTranslation;
use App\Modules\Common\Services\BaseService;

class CategoryFilterCrudService extends BaseService
{
    public function formatCategoryFilterStructure(FilterCategory $categoryFilter): array
    {
        $translations = $this->getTranslationCategoryFilter($categoryFilter);

        return [
            'id' => $categoryFilter->getId(),
            'name' => $categoryFilter->getName(),
            'translations' => $translations,
        ];
    }

    public function formatFilterStructure(Filter $filter): array
    {
        $translations = $this->getTranslationFilter($filter);

        return [
            'id' => $filter->getId(),
            'name' => $filter->getName(),
            'code' => $filter->getCode(),
            'source' => $filter->getSource(),
            'translations' => $translations,
        ];
    }

    public function getFilterCategoryFilterId(FilterCategory $filterCategory)
    {
        $local = $this->security->getUser()->getLocale() ?? $this->settings->get('app.adminDefaultLanguage');
        $filterCategorys = $this->em->getRepository(Filter::class)->findAllOrderBySort($filterCategory);
        $filterData = [];
        foreach ($filterCategorys as $filter) {
            /** @var FilterTranslation $translation */
            $translations = $filter->translate($local, false);

            $name = $translations ? $translations->getName() : $filter->getName();

            $filterData[] = [
                'id' => $filter->getId(),
                'name' => $name ?? $filter->getName(),
                'sort' => $filter->getSort(),
                'translations' => $this->getTranslationFilter($filter),
            ];
        }

        return $filterData;
    }

    public function getFilterCategory()
    {
        /** @var User $user */
        $userAdm = $this->getUser();
        $filters = [];
        $filterCategories = $this->em->getRepository(FilterCategory::class)->findAllOrderBySort();

        foreach ($filterCategories as $filter) {
            /** @var FilterCategoryTranslation $translated */
            $translated = $filter->translate($userAdm->getLocale(), false);
            $name = $translated->getName();
            $element = new \stdClass();
            $element->id = $filter->getId();

            $element->name = $name ?? $filter->getName();
            $element->sort = $filter->getSort();
            $element->isRanking = $filter->isIsRanking();

            $trans = $this->getTranslationCategoryFilter($filter);
            $element->translations = $trans;

            $element->totalFilters = \count($this->em->getRepository(Filter::class)->findBy(['filterCategory' => $filter]));

            array_push($filters, $element);
        }

        return $filters;
    }

    public function getTranslationFilter(Filter $filter): array
    {
        $translations = [];
        $locale = $this->getUser()->getLocale();
        $locales = $this->settings->get('app.languages');
        $filterLocal = [];

        /** @var FilterTranslation $translation */
        foreach ($filter->getTranslations() as $translation) {
            $translations[] = [
                'locale' => $translation->getLocale(),
                'name' => $translation->getName(),
            ];
            array_push($filterLocal, $translation->getLocale());
        }

        if (!\in_array($locale, $filterLocal)) {
            $translations[] = [
                'locale' => $locale,
                'name' => $filter->getName(),
            ];
            array_push($filterLocal, $locale);
        }

        foreach ($locales as $local) {
            if (!\in_array($local, $filterLocal)) {
                $translations[] = [
                    'locale' => $local,
                    'name' => '',
                ];
            }
        }

        return $translations;
    }

    public function getTranslationCategoryFilter(FilterCategory $filterCategory): array
    {
        $translations = [];
        $locale = $this->getUser()->getLocale();
        $locales = $this->settings->get('app.languages');
        $filterCategoryLocal = [];

        /** @var FilterCategoryTranslation $translation */
        foreach ($filterCategory->getTranslations() as $translation) {
            $translations[] = [
                'locale' => $translation->getLocale(),
                'name' => $translation->getName() ?? '',
            ];
            array_push($filterCategoryLocal, $translation->getLocale());
        }

        if (!\in_array($locale, $filterCategoryLocal)) {
            $translations[] = [
                'locale' => $locale,
                'name' => $filterCategory->getName(),
            ];
            array_push($filterCategoryLocal, $locale);
        }

        foreach ($locales as $local) {
            if (!\in_array($local, $filterCategoryLocal)) {
                $translations[] = [
                    'locale' => $local,
                    'name' => '',
                ];
            }
        }

        return $translations;
    }

    public function saveCategoryFilterTranslation(array $translations, FilterCategory $filterCategory): void
    {
        try {
            foreach ($translations as $data) {
                $translation = $this->em->getRepository(FilterCategoryTranslation::class)->findOneBy([
                    'translatable' => $filterCategory,
                    'locale' => $data['locale']
                ]);

                $name = $data['name'] ?? null;

                if (empty($name)) {
                    if ($translation) {
                        $this->em->remove($translation);
                    }
                    continue;
                }

                if (!$translation) {
                    $translation = new FilterCategoryTranslation();
                    $translation->setTranslatable($filterCategory);
                    $translation->setLocale($data['locale']);
                }

                $translation->setName($name);
                $this->em->persist($translation);
            }

            $this->em->flush();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function saveFilterTranslation(array $translations, Filter $filter): void
    {
        try {
            foreach ($translations as $data) {
                $translation = $this->em->getRepository(FilterTranslation::class)->findOneBy([
                    'translatable' => $filter,
                    'locale' => $data['locale']
                ]);

                $name = $data['name'] ?? null;

                if (empty($name)) {
                    if ($translation) {
                        $this->em->remove($translation);
                    }
                    continue;
                }

                if (!$translation) {
                    $translation = new FilterTranslation();
                    $translation->setTranslatable($filter);
                    $translation->setLocale($data['locale']);
                }

                $translation->setName($name);
                $this->em->persist($translation);
            }

            $this->em->flush();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function delTranslationCategoryFilter(FilterCategory $filterCategory): void
    {
        /** @var FilterCategoryTranslation $translation */
        foreach ($filterCategory->getTranslations() as $translation) {
            $this->em->remove($translation);
        }

        $this->em->flush();
    }

    public function delTranslationFilter(Filter $filter): void
    {
        /** @var FilterTranslation $translation */
        foreach ($filter->getTranslations() as $translation) {
            $this->em->remove($translation);
        }

        $this->em->flush();
    }

    public function getUserLocale()
    {
        return $this->getUser()->getLocale() ?? $this->settings->get('app.defaultLanguage');
    }
}
