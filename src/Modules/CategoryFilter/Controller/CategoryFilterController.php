<?php

declare(strict_types=1);

namespace App\Modules\CategoryFilter\Controller;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\Pages;
use App\Modules\CategoryFilter\Services\CategoryFilterCrudService;
use App\Modules\Common\Controller\BaseVueController;
use App\Repository\FilterCategoryRepository;
use App\Repository\FilterRepository;
use App\Repository\PagesRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Route("/admin/")
 */
class CategoryFilterController extends BaseVueController
{
    private AdminContextProvider $context;
    private JWTManager $JWTManager;
    private CategoryFilterCrudService $categoryFilterCrudService;
    private PagesRepository $pagesRepository;

    public function __construct(
        AdminContextProvider $context,
        JWTManager $JWTManager,
        SettingsService $settings,
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        CategoryFilterCrudService $categoryFilterCrudService,
        TranslatorInterface $translator,
        PagesRepository $pagesRepository
    ) {
        parent::__construct($settings, $em, $logger, $requestStack, $translator);

        $this->context = $context;
        $this->JWTManager = $JWTManager;
        $this->categoryFilterCrudService = $categoryFilterCrudService;
        $this->pagesRepository = $pagesRepository;
    }

    public static function getEntityFqcn(): string
    {
        return Pages::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud->overrideTemplate('crud/index', 'admin/categoryFilter/app.html.twig');
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_INDEX === $responseParameters->get('pageName')) {
            $this->configureAppResponseParameters(
                $responseParameters,
                $this->settings,
                $this->context,
                $this->JWTManager,
                [],
            );
        }

        return $responseParameters;
    }

    /**
     * @Rest\Get("categoryFilter/all")
     */
    public function getAllCategoryFilter(): Response
    {
        return $this->executeSafe(function () {
            return [
                'categorysFilter' => $this->categoryFilterCrudService->getFilterCategory(),
                'languages' => $this->settings->get('app.languages'),
                'userLocale' => $this->categoryFilterCrudService->getUserLocale(),
                'filterInRanking' => $this->settings->get('app.use.filter_in_ranking'),
            ];
        });
    }

    /**
     * @Rest\Get("categoryFilter/{id}/filter")
     */
    public function getFiltersCategoryFilter(FilterCategory $filterCategory): Response
    {
        return $this->executeSafe(function () use ($filterCategory) {
            return [
                'filter' => $this->categoryFilterCrudService->getFilterCategoryFilterId($filterCategory),
            ];
        });
    }

    /**
     * @Rest\Post("categoryFilter/create")
     */
    public function createCategoryFilter(): Response
    {
        return $this->executeSafe(function () {
            $request = $this->requestStack->getCurrentRequest();
            $data = json_decode($request->getContent(), true);
            $content = $data['pages'];
            $filters = $data['filters'];
            $filterCategory = new FilterCategory();
            
            $filterCategory->setSort($this->em->getRepository(FilterCategory::class)->getNextSort());

            if (($result = $this->validateFilterCategory($content)) instanceof Response) {
                return $result;
            }
            if (($result = $this->saveCategoryFilter($content, $filterCategory, $filters)) instanceof Response) {
                return $result;
            }

            return $this->categoryFilterCrudService->formatCategoryFilterStructure($filterCategory);
        }, [], [], Response::HTTP_CREATED);
    }

    /**
     * @Rest\Post("categoryFilter/create/filter")
     */
    public function createFilter(): Response
    {
        return $this->executeSafe(function () {
            $request = $this->requestStack->getCurrentRequest();
            $content = json_decode($request->getContent(), true);
            $filter = new Filter();

            if (($result = $this->saveFilter($content, $filter)) instanceof Response) {
                return $result;
            }
            $this->addFlash('success', $this->translator->trans('categoryFilter.save_succes'));

            return $this->categoryFilterCrudService->formatFilterStructure($filter);
        }, [], [], Response::HTTP_CREATED);
    }

    /**
     * @Rest\Post("categoryFilter/update/filter")
     */
    public function updateFilter(FilterRepository $filterRepository): Response
    {
        return $this->executeSafe(function () use ($filterRepository) {
            $request = $this->requestStack->getCurrentRequest();
            $content = json_decode($request->getContent(), true);
            $id = $content['id'] ?? -1;
            $filter = $filterRepository->find($id);

            if (!$filter) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'NOT_FOUND'
                ]);
            }

            if (($result = $this->saveFilter($content, $filter)) instanceof Response) {
                return $result;
            }
            $this->addFlash('success', $this->translator->trans('categoryFilter.update_succes'));

            return $this->categoryFilterCrudService->formatFilterStructure($filter);
        }, [], [], Response::HTTP_OK);
    }

    /**
     * @Rest\Post("categoryFilter/update")
     */
    public function updateFilterCategory(FilterCategoryRepository $filterCategoryRepository): Response
    {
        return $this->executeSafe(function () use ($filterCategoryRepository) {
            $request = $this->requestStack->getCurrentRequest();
            $data = json_decode($request->getContent(), true);
            $content = $data['pages'];
            $filters = $data['filters'];

            $id = $content['id'] ?? -1;
            $filterCategory = $filterCategoryRepository->find($id);

            if (!$filterCategory) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'NOT_FOUND'
                ]);
            }

            if (($result = $this->validateFilterCategory($content)) instanceof Response) {
                return $result;
            }

            if (($result = $this->saveCategoryFilter($content, $filterCategory, $filters)) instanceof Response) {
                return $result;
            }

            return $this->categoryFilterCrudService->formatCategoryFilterStructure($filterCategory);
        }, [], [], Response::HTTP_OK);
    }

    /**
     * @Rest\Delete("categoryFilter/{id}/delete")
     */
    public function deleteCategoryFilter(FilterCategory $filterCategory): Response
    {
        return $this->executeSafe(function () use ($filterCategory): void {
            $this->categoryFilterCrudService->delTranslationCategoryFilter($filterCategory);
            $this->em->remove($filterCategory);
            $this->em->flush();
        }, [], [], Response::HTTP_NO_CONTENT);
    }

    /**
     * @Rest\Delete("categoryFilter/{id}/delete_filter")
     */
    public function deleteFilter(Filter $filter): Response
    {
        return $this->executeSafe(function () use ($filter): void {
            $this->categoryFilterCrudService->delTranslationFilter($filter);
            $this->em->remove($filter);
            $this->em->flush();
            $this->addFlash('success', $this->translator->trans('categoryFilter.delete'));
        }, [], [], Response::HTTP_NO_CONTENT);
    }

    public function validateFilterCategory($content)
    {
        // $content = json_decode($request->getContent(), true);

        $name = $content['name'] ?? null;

        $errors = [];
        if (\is_null($name)) {
            $errors[] = 'Name required';
        }
        if (\count($errors)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => $errors
            ]);
        }

        return true;
    }

    public function saveCategoryFilter($content, FilterCategory $filterCategory, Array $filters)
    {
        $name = $content['name'] ?? null;

        $filterCategory->setName((string) $name);       
        $this->categoryFilterCrudService->saveCategoryFilterTranslation($content['translations'], $filterCategory);
        $this->em->persist($filterCategory);
        $this->em->flush();

        $result = $this->adminFilters($filters, $filterCategory);

        return true;
    }

    private function adminFilters(Array $filters, FilterCategory $filterCategory)
    {
        foreach ($filters as $filter) {
            $filterBD = null;
            if($filter['operation'] === 'delete'){
                $filterBD = $this->em->getRepository(Filter::class)->find($filter['id']);
                $this->em->remove($filterBD);
                $this->em->flush();
            }else{
                if($filter['operation'] === 'create'){
                    $filterBD = new Filter();
                    $filterBD->setSort($this->em->getRepository(Filter::class)->getNextSort($filterCategory));
                }
                if($filter['operation'] === 'update'){
                    $filterBD = $this->em->getRepository(Filter::class)->find($filter['id']);
                }
                $filterBD->setName((string) $filter['name']);
                $filterBD->setFilterCategory($filterCategory);
                $this->em->persist($filterBD);
                $this->em->flush();
                $this->categoryFilterCrudService->saveFilterTranslation($filter['translations'], $filterBD);
            }
        }
    }

    public function saveFilter($content, Filter $filter): bool
    {
        $name = $content['name'] ?? null;
        $categoryFilter_id = $content['filter_category_id'];

        $categoryFilter = $this->em->getRepository(FilterCategory::class)->find($categoryFilter_id);

        $filter->setName((string) $name);
        $filter->setFilterCategory($categoryFilter);
        $this->categoryFilterCrudService->saveFilterTranslation($content['translations'], $filter);
        $this->em->persist($filter);
        $this->em->flush();

        return true;
    }

    /**
     * @Rest\Put("categoryFilter/changeSort")
     */
    public function changeCategoryFilterSort(Request $request)
    {
        $categoryFilters = json_decode($request->getContent(), true)['categoryFilter'];

        foreach ($categoryFilters as $categoryFilter) {
            $categoryFilterBD = $this->em->getRepository(FilterCategory::class)->find($categoryFilter['id']);
            $categoryFilterBD->setSort($categoryFilter['newSort']);
            $this->em->persist($categoryFilterBD);
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => ''
        ]);
    }

    /**
     * @Rest\Put("categoryFilter/initializeSort")
     */
    public function InitializeCategoryFilterSort(Request $request)
    {
        $categoryFilters = $this->em->getRepository(FilterCategory::class)->findAll();

        foreach ($categoryFilters as $categoryFilter) {
            $categoryFilter->setSort($categoryFilter->getId());
            $this->em->persist($categoryFilter);
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => ''
        ]);
    }

    /**
     * @Rest\Put("categoryFilter/{id}/isRanking")
     * @param PAGES $pages
     * @return Response
     */
    public function changeIsranking(FilterCategory $filterCategory)
    {
        $filterCategory->setIsRanking(!$filterCategory->isIsRanking());
        $this->em->persist($filterCategory);   
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => ''
        ]);    
    }


    /**
     * @Rest\Put("filter/changeSort")
     */
    public function changeFilterSort(Request $request)
    {
        $filters = json_decode($request->getContent(), true)['filters'];

        foreach ($filters as $filter) {
            $filterBD = $this->em->getRepository(Filter::class)->find($filter['id']);
            $filterBD->setSort($filter['newSort']);
            $this->em->persist($filterBD);
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => ''
        ]);
    }
    /**
     * @Rest\Put("filter/initializeSort")
     */
    public function InitializeFilterSort(Request $request)
    {
        $filterCategoryId = 0;
        $filterSort = 0;
        $filters = $this->em->getRepository(Filter::class)->findAll();


        foreach ($filters as $filter) {
            if($filterCategoryId !== $filter->getFilterCategory()){
                $filterCategoryId = $filter->getFilterCategory();
                $filterSort = 0;
            }
            $filterSort++;
            $filter->setSort($filterSort);
            $this->em->persist($filter);
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => ''
        ]);
    }


}
