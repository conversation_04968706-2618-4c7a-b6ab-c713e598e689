<?php

namespace  App\Modules\Common\Controller;

use App\Admin\Traits\FilterValuesTrait;
use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\VueAppDefaultConfiguration;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;


abstract class BaseVueController extends AbstractCrudController
{
    use VueAppDefaultConfiguration;
    use SerializerTrait;
    use FilterValuesTrait;   

    protected $settings;
    protected $em;
    protected $logger;
    protected $requestStack;
    protected $translator;

    public function __construct(
        SettingsService $settings,
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        TranslatorInterface $translator
    ) {
        $this->settings = $settings;
        $this->em = $em;
        $this->logger = $logger;
        $this->requestStack = $requestStack;
        $this->translator = $translator;
    }


    protected function createResponse($data = null, array $paramsExtra = [])
    {
        $status = $paramsExtra['status'] ?? Response::HTTP_OK;
        $error = $paramsExtra['error'] ?? false;
        $message = $paramsExtra['message'] ?? null;
        $groups = $paramsExtra['groups'] ?? [];

        $responseData = [
            'status' => $status ,
            'error' => $error,
            'data' => $data
        ];
      
        if ($error && $message !== null) {
            $responseData['message'] = $message;
        }

      return  $this->sendResponse($responseData,  array("groups" => $groups));
    }

    protected function handleException(\Exception $e): Response
    {
        $errorMessage = sprintf('Error on line  %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());
        return $this->sendResponse(['error' => true, 'status' => Response::HTTP_INTERNAL_SERVER_ERROR, 'message' => $errorMessage]);
    }


    protected function validateData(Request $request, array $rules)
    {
        $requestData  =  json_decode($request->getContent(), true);

        $params = [];
        $params['status'] = Response::HTTP_BAD_REQUEST;
        $params['error'] = true;

        foreach ($rules as $key => $rule) {
            if (!array_key_exists($key, $requestData)) { 
                $params['message'] = 'Missing required field: ' . $key;            

                return $this->createResponse(null, $params);
            }

            if ($rule === 'required' && empty($requestData[$key])) {
                $params['status'] = Response::HTTP_BAD_REQUEST;
                $params['message'] = 'Field ' . $key . ' is required';
                return $this->createResponse(null, $params);
            }
        }
    }

    protected function executeSafe(callable $callback, $data = [], $groups = [], $code = Response::HTTP_OK)
    {
        try {  
            $result = $callback($data, $groups);        
            $params = ['groups' => $groups];
            $params['status'] = $code;
            
            return $this->createResponse($result, $params);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
}
