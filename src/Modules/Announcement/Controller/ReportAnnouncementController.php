<?php

declare(strict_types=1);

namespace App\Modules\Announcement\Controller;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementUser;
use App\Entity\User;
use App\Entity\ZipFileTask;
use App\Service\Annoucement\Report\AnnouncementReportService;
use App\Service\Annoucement\ReportPdf\AnnouncementReportZipGenerator;
use App\Service\Annoucement\ReportPdf\ExcelReportGeneratorService;
use App\Service\Annoucement\ReportPdf\Pdf\PdfAnnouncementGroupService;
use App\Service\Annoucement\ReportPdf\PdfReportGeneratorService;
use App\Service\Annoucement\ReportPdf\User\ReportActivitiesService;
use App\Service\Annoucement\ReportPdf\User\ReportConexionsService;
use App\Service\ZipFileTask\ZipFileTaskService;
use App\Utils\FileUtils;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationService;
use App\V2\Infrastructure\Utils\MpdfFactory;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class ReportAnnouncementController extends AbstractController
{
    use SerializerTrait;

    private EntityManagerInterface $em;
    private $logger;
    private $translator;
    private $fileUtils;
    private $excelReportGeneratorService;
    private $pdfReportGeneratorService;
    private $MpdfFactory;
    private $pdfAnnouncementGroupService;
    private $reportActivitiesService;
    private ZipFileTaskService $zipFileTaskService;

    private $security;

    public function __construct(
        EntityManagerInterface $em,
        LoggerInterface $logger,
        TranslatorInterface $translator,
        FileUtils $fileUtils,
        ExcelReportGeneratorService $excelReportGeneratorService,
        PdfReportGeneratorService $pdfReportGeneratorService,
        MpdfFactory $MpdfFactory,
        PdfAnnouncementGroupService $pdfAnnouncementGroupService,
        ReportActivitiesService $reportActivitiesService,
        Security $security,
        ZipFileTaskService $zipFileTaskService,
        readonly private AnnouncementAuthorizationService $announcementAuthorizationService,
    ) {
        $this->em = $em;
        $this->logger = $logger;
        $this->translator = $translator;
        $this->fileUtils = $fileUtils;
        $this->excelReportGeneratorService = $excelReportGeneratorService;
        $this->pdfReportGeneratorService = $pdfReportGeneratorService;
        $this->MpdfFactory = $MpdfFactory;
        $this->pdfAnnouncementGroupService = $pdfAnnouncementGroupService;
        $this->reportActivitiesService = $reportActivitiesService;
        $this->security = $security;
        $this->zipFileTaskService = $zipFileTaskService;
    }

    private function handleException(\Exception $e): Response
    {
        $errorMessage = \sprintf('Error on line %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());

        return $this->sendResponse(['error' => true, 'status' => 500, 'message' => $errorMessage]);
    }

    /**
     * @Route("admin/report-group/{id}", name="report-group", methods={"POST", "GET"})
     */
    public function reportGroup(Announcement $announcement, Request $request)
    {
        try {
            $content = json_decode($request->getContent(), true);

            $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($content['idGroup']);
            $pdf = $this->pdfAnnouncementGroupService->generatePdfGroup($announcementGroup, $announcement);

            return $this->MpdfFactory->createDownloadResponse($pdf, 'informe.pdf');
        } catch (\Exception $e) {
            $errorMessage = \sprintf('Error on line %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());
            $response = ['error' => true, 'status' => 500, 'message' => $errorMessage];

            return $this->sendResponse($response);
        }
    }

    /**
     * @Rest\Post("admin/report-info/announcement/download-group-info")
     */
    public function downloadAnnouncementGroupZipReport(Request $request): Response
    {
        $content = json_decode($request->getContent(), true);
        $zipFileTaskRepository = $this->em->getRepository(ZipFileTask::class);
        $report = $zipFileTaskRepository->findOneBy([
            'type' => AnnouncementReportService::TYPE_GROUP,
            'entityId' => $content['groupId'],
        ]);
        if (!$report || ZipFileTask::STATUS_COMPLETED != $report->getStatus()) {
            return new Response('File not found', Response::HTTP_NOT_FOUND);
        }

        return $zipFileTaskRepository->download($report);
    }

    /**
     * @Rest\Post("admin/report-info/announcement/group-info")
     */
    public function getAnnouncementGroupReportInfo(Request $request): Response
    {
        $content = json_decode($request->getContent(), true);
        $groupId = $content['groupId'] ?? null;
        if (empty($groupId)) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'data' => 'groupId required',
            ]);
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->em->getRepository(AnnouncementGroup::class)->tutorZipReportStatus($groupId),
        ]);
    }

    /**
     * @Route("admin/generate-report/announcement/{announcement}", name="reports-fundae", methods={"POST", "GET"})
     */
    public function generateReportByGroup(Announcement $announcement, Request $request): Response
    {
        $content = json_decode($request->getContent(), true);
        $groups = $announcement->getAnnouncementGroups();
        if ($groups->count() < 1) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'ANNOUNCEMENT.GENERATE_REPORT.GROUPS_INFORMATION_REQUIRED',
            ]);
        }

        $tutorSelected = true;
        foreach ($groups as $group) {
            $tutor = $group->getAnnouncementTutor();
            if (!$tutor) {
                $tutorSelected = false;
                break;
            }
        }

        if (!$tutorSelected) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'ANNOUNCEMENT.GENERATE_REPORT.GROUP_TUTOR_REQUIRED',
            ]);
        }

        $announcementId = $content['announcementId'] ?? null;
        $groupId = $content['groupId'] ?? null;

        if (empty($announcementId) && empty($groupId)) {
            throw new \RuntimeException('announcementId or groupId must be defined');
        }
        $user = $this->security->getUser();

        $this->zipFileTaskService->enqueueZipFileTask(
            $user,
            'announcement-report',
            $content,
            empty($groupId) ? AnnouncementReportService::TYPE_ANNOUNCEMENT : AnnouncementReportService::TYPE_GROUP,
            'announcement-report' . (new \DateTimeImmutable())->format('dmYHis'),
            empty($groupId) ? (string) $announcementId : (string) $groupId
        );

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => 'ANNOUNCEMENT.GENERATE_REPORT.SUCCESS',
        ]);
    }

    private function generatePdfReport(Announcement $announcement, array $content): ?array
    {
        if (!empty($content['PDF'])) {
            $mPdf = $this->pdfReportGeneratorService->informePdfAnnouncement($announcement, $content['idGroup']);
            $outputPdf = base64_encode($mPdf->Output('informe.pdf', 'S'));

            return [
                'base64' => $outputPdf,
                'name' => $this->fileUtils->getNameFileClean($announcement->getCourse()->getName(), 'pdf'),
            ];
        }

        return null;
    }

    private function generateExcelReport(Announcement $announcement, array $content): ?array
    {
        if (!empty($content['EXCEL'])) {
            $outputExcel = $this->excelReportGeneratorService->generateExcelReportAnnouncement($announcement);

            return [
                'base64' => $outputExcel,
                'name' => $this->fileUtils->getNameFileClean($announcement->getCourse()->getName(), 'xlsx'),
            ];
        }

        return null;
    }

    /**
     * @Route("/admin/report-pdf-announcement-user/{user}/announcement/{announcement}", name="report-pdf-announcement-user")
     */
    public function generatePdfReportUser(User $user, Announcement $announcement)
    {
        $requestUser = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageAnnouncementResource(user: $requestUser, announcement: $announcement);
        try {
            $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['user' => $user, 'announcement' => $announcement]);
            $mPdf = $this->pdfReportGeneratorService->generatePdfReportUser($announcementUser);
            $outputPdf = base64_encode($mPdf->Output('informe.pdf', 'S'));

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $outputPdf,
            ];
        } catch (\Exception $e) {
            $errorMessage = \sprintf('Error on line %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());
            $response = ['error' => true, 'status' => 500, 'message' => $errorMessage];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/report-pdf-announcement/{announcement}", name="report-pdf-announcement", methods={"POST", "GET"})
     */
    public function generateReportPdfAnnouncement(Announcement $announcement, Request $request)
    {
        try {
            $content = json_decode($request->getContent(), true);
            $contentIdGroup = $content['idGroup'];
            $contentPdf = $content['PDF'];

            $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->findOneBy(['id' => $contentIdGroup, 'announcement' => $announcement]);
            $idGroup = null;

            if ($announcementGroup) {
                $idGroup = $announcementGroup->getId();
            }

            $mPdf = $this->pdfReportGeneratorService->informePdfAnnouncement($announcement, $idGroup);

            return $this->MpdfFactory->createDownloadResponse($mPdf, "{$announcement->getCourse()->getName()}.pdf");
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Ha ocurrido un error al descargar el pdf {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Rest\Post("/test-report")
     *
     * @return Response
     */
    public function testZip(Request $request, AnnouncementReportZipGenerator $generator)
    {
        $content = json_decode($request->getContent(), true);
        $generator->generate($content);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    /**
     * @Route("admin/activities-user/{id}", name="activities", methods={"POST", "GET"})
     */
    public function activitiesUser(AnnouncementUser $announcementUser)
    {
        try {
            $pdf = $this->reportActivitiesService->generatePdfActivities($announcementUser);

            return $this->MpdfFactory->createDownloadResponse($pdf, 'informe.pdf');
        } catch (\Exception $e) {
            $errorMessage = \sprintf('Error on line %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());
            $response = ['error' => true, 'status' => 500, 'message' => $errorMessage];

            return $this->sendResponse($response);
        }
    }

    /**
     * @Route("admin/conexions-user/{id}", name="conexions", methods={"POST", "GET"})
     */
    public function conexionsUser(AnnouncementUser $announcementUser, ReportConexionsService $reportConexionsService)
    {
        try {
            $pdf = $reportConexionsService->generatePdfConnection($announcementUser);

            return $this->MpdfFactory->createDownloadResponse($pdf, 'informe.pdf');
        } catch (\Exception $e) {
            $errorMessage = \sprintf('Error on line %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());
            $response = ['error' => true, 'status' => 500, 'message' => $errorMessage];

            return $this->sendResponse($response);
        }
    }
}
