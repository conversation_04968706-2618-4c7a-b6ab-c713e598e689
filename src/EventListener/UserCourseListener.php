<?php

namespace App\EventListener;

use App\Service\Geolocation\GeolocationService;


use App\Entity\UserCourse;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Psr\Log\LoggerInterface;

class UserCourseListener
{
    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private GeolocationService $geolocationService;


    /**
     * @param EntityManagerInterface $em
     * @param LoggerInterface $logger
     */
    public function __construct (EntityManagerInterface $em, LoggerInterface $logger, GeolocationService $geolocationService)
    {
        $this->em = $em;
        $this->logger  = $logger;
        $this->geolocationService  = $geolocationService;
    }

    public function prePersist(UserCourse $userCourse,  PrePersistEventArgs $event){
      $timeActual = $this->geolocationService->getTimeActualInTheZone();
      $userCourse->setTimezone($timeActual->timezone);
      $userCourse->setCountry($timeActual->country);
      $userCourse->setTimezoneCreatedAt($timeActual->hourActual);
      $userCourse->setTimezoneUpdatedAt($timeActual->hourActual);
    }

    public function preUpdate(UserCourse $userCourse,  PreUpdateEventArgs $event){
      $timeActual = $this->geolocationService->getTimeActualInTheZone();
      $userCourse->setTimezoneUpdatedAt($timeActual->hourActual);
    }
}
