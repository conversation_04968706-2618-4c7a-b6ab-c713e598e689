<?php

namespace App\EventListener;

use App\Service\Geolocation\GeolocationService;


use App\Entity\UserHistoryDownloadDiploma;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Psr\Log\LoggerInterface;

class UserHistoryDownloadDiplomaListener
{
    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private GeolocationService $geolocationService;


    /**
     * @param EntityManagerInterface $em
     * @param LoggerInterface $logger
     * @param GeolocationService $geolocationService
     */
    public function __construct (EntityManagerInterface $em, LoggerInterface $logger, GeolocationService $geolocationService)
    {
        $this->em = $em;
        $this->logger  = $logger;
        $this->geolocationService  = $geolocationService;
    }

    public function prePersist(UserHistoryDownloadDiploma $userHistoryDownloadDiploma,  PrePersistEventArgs $event){
      $timeActual = $this->geolocationService->getTimeActualInTheZone();
      $userHistoryDownloadDiploma->setTimezone($timeActual->timezone);
      $userHistoryDownloadDiploma->setCountry($timeActual->country);
      $userHistoryDownloadDiploma->setTimezoneCreatedAt($timeActual->hourActual);
      $userHistoryDownloadDiploma->setTimezoneUpdatedAt($timeActual->hourActual);
    }

    public function preUpdate(UserHistoryDownloadDiploma $userHistoryDownloadDiploma,  PreUpdateEventArgs $event){
      $timeActual = $this->geolocationService->getTimeActualInTheZone();
      $userHistoryDownloadDiploma->setTimezoneUpdatedAt($timeActual->hourActual);
    }
}
