<?php

namespace App\EventListener;

use App\Service\Geolocation\GeolocationService;


use App\Entity\UserCourseChapter;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Psr\Log\LoggerInterface;

class UserCourseChapterListener
{
    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private GeolocationService $geolocationService;


    /**
     * @param EntityManagerInterface $em
     * @param LoggerInterface $logger
     */
    public function __construct (EntityManagerInterface $em, LoggerInterface $logger, GeolocationService $geolocationService)
    {
        $this->em = $em;
        $this->logger  = $logger;
        $this->geolocationService  = $geolocationService;
    }

    public function prePersist(UserCourseChapter $userCourseChapter,  PrePersistEventArgs $event){
      $timeActual = $this->geolocationService->getTimeActualInTheZone();
      $userCourseChapter->setTimezone($timeActual->timezone);
      $userCourseChapter->setCountry($timeActual->country);
      $userCourseChapter->setTimezoneCreatedAt($timeActual->hourActual);
      $userCourseChapter->setTimezoneUpdatedAt($timeActual->hourActual);
    }

    public function preUpdate(UserCourseChapter $userCourseChapter,  PreUpdateEventArgs $event){
        $timeActual = $this->geolocationService->getTimeActualInTheZone();
        $userCourseChapter->setTimezoneUpdatedAt($timeActual->hourActual);
    }
}
