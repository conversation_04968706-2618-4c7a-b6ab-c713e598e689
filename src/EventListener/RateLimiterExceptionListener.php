<?php

declare(strict_types=1);

namespace App\EventListener;

use App\Exception\RateLimiterException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;

class RateLimiterExceptionListener
{
    public function onKernelException(ExceptionEvent $event)
    {
        $exception = $event->getThrowable();
        $request = $event->getRequest();

        if ('application/json' === $request->headers->get('Content-Type')) {
            $response = new JsonResponse([
                'code' => $exception->getCode(),
                'error' => true,
                'data' => $exception->getMessage(),
            ]);

            if ($exception instanceof RateLimiterException) {
                $response->setStatusCode(Response::HTTP_TOO_MANY_REQUESTS);
            }
            $event->setResponse($response);
        }
    }
}
