<?php

namespace App\EventListener;

use App\Entity\Chapter;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Doctrine\ORM\Event\PostUpdateEventArgs;
use Psr\Log\LoggerInterface;

class ChapterListener
{
    private EntityManagerInterface $em;


    /**
     * @param EntityManagerInterface $em
     */
    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }


    /*
     * @postSoftDelete
     */
    public function postSoftDelete(LifecycleEventArgs $event)
    {

        /** @var Chapter $deletedChapter */
        $deletedChapter = $event->getObject();

        if (!($deletedChapter instanceof Chapter)) {
            return;
        }

        $criteria = (Criteria::create())->orderBy(['position' => Criteria::ASC]);
        $chapters = $deletedChapter->getSeason()->getChapters()->matching($criteria)->getValues();

        $position = 1;
        foreach ($chapters as $chapter) {
            if ($chapter->getDeletedAt() === null) {
                $chapter->setPosition($position);
                $position++;
            }
        }
        $this->em->flush();
    }
}
