<?php

namespace App\EventListener;

use App\Service\Geolocation\GeolocationService;


use App\Entity\UserTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Psr\Log\LoggerInterface;

class UserTimeListener
{
    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private GeolocationService $geolocationService;


    /**
     * @param EntityManagerInterface $em
     * @param LoggerInterface $logger
     */
    public function __construct (EntityManagerInterface $em, LoggerInterface $logger, GeolocationService $geolocationService)
    {
        $this->em = $em;
        $this->logger  = $logger;
        $this->geolocationService  = $geolocationService;
    }

    public function prePersist(UserTime $userTime,  PrePersistEventArgs $event){
      $timeActual = $this->geolocationService->getTimeActualInTheZone();
      $userTime->setTimezone($timeActual->timezone);
      $userTime->setCountry($timeActual->country);
      $userTime->setTimezoneCreatedAt($timeActual->hourActual);
      $userTime->setTimezoneUpdatedAt($timeActual->hourActual);
    }

    public function preUpdate(UserTime $userTime,  PreUpdateEventArgs $event){
      $timeActual = $this->geolocationService->getTimeActualInTheZone();
      $userTime->setTimezoneUpdatedAt($timeActual->hourActual);
    }
}
