<?php

namespace App\EventListener;

use App\Service\Geolocation\GeolocationService;


use App\Entity\UserLogin;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Psr\Log\LoggerInterface;

class UserLoginListener
{
    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private GeolocationService $geolocationService;


    /**
     * @param EntityManagerInterface $em
     * @param LoggerInterface $logger
     * @param GeolocationService $geolocationService
     */
    public function __construct (EntityManagerInterface $em, LoggerInterface $logger, GeolocationService $geolocationService)
    {
        $this->em = $em;
        $this->logger  = $logger;
        $this->geolocationService  = $geolocationService;
    }

    public function prePersist(UserLogin $userLogin,  PrePersistEventArgs $event){
      $timeActual = $this->geolocationService->getTimeActualInTheZone();
      $userLogin->setTimezone($timeActual->timezone);
      $userLogin->setCountry($timeActual->country);
      $userLogin->setTimezoneCreatedAt($timeActual->hourActual);
      $userLogin->setTimezoneUpdatedAt($timeActual->hourActual);
    }
}
