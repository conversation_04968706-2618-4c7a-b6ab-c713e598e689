<?php

namespace App\EventListener;

use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\LifecycleEventArgs;

class UserListener
{
    private EntityManagerInterface $em;


    public function __construct (EntityManagerInterface $em)
    {
        $this->em = $em;
    }


    public function postSoftDelete (LifecycleEventArgs $event): void
    {

        /** @var User $deletedUser */
        $deletedUser = $event->getObject();
        if (!($deletedUser instanceof User))
        {
            return;
        }

        $deletedUser->setEmail('[DELETED]' . $deletedUser->getEmail() . '[/DELETED]');
        $this->em->flush();
    }
}
