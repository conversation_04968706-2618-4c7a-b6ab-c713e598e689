<?php

namespace App\EventSubscriber;

use App\Entity\Filter;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Event\BeforeEntityPersistedEvent;
use EasyCorp\Bundle\EasyAdminBundle\Event\BeforeEntityUpdatedEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RequestStack;

class UserSubscriber implements EventSubscriberInterface
{
    private EntityManagerInterface $em;
    private RequestStack $requestStack;

    public function __construct(
        EntityManagerInterface $em,
        RequestStack $requestStack
    )
    {
        $this->em = $em;
        $this->requestStack = $requestStack;
    }


    public static function getSubscribedEvents(): array
    {
        return [
            BeforeEntityPersistedEvent::class => 'beforeEntityPersistedEvent',
            BeforeEntityUpdatedEvent::class => 'beforeEntityUpdatedEvent'
        ];
    }

    public function beforeEntityPersistedEvent(BeforeEntityPersistedEvent $event) {
        $entity = $event->getEntityInstance();
        if ($entity instanceof User) {
            $this->processJsonFilter($entity);
        }
    }

    public function beforeEntityUpdatedEvent(BeforeEntityUpdatedEvent $event) {
        $entity = $event->getEntityInstance();
        if ($entity instanceof User) {
            $local = $entity->getLocale();
            $locale_ = $this->requestStack->getSession()->get('_locale');
            if($local != $locale_){
                $this->requestStack->getSession()->set('_locale', $local);
            }
            $this->processJsonFilter($entity);
        }
    }

    private function processJsonFilter(User $user) {
        if (!empty($filtersJson = $user->getFiltersJson())) {
            $data = json_decode($filtersJson, true);

            $ids = [];
            foreach ($data as $category) {
                foreach ($category['selected'] as $filters) {
                    $ids[] = $filters['id'];
                }
            }

            foreach($ids as $id)
            {
                if($user->hasFilterId($id))
                {
                    continue;
                }

                if($user->isCustomExcludedFilter($id))
                {
                    $user->removeCustomFilter($id);
                }
                else
                {
                    $user->addCustomAssignedFilter($id);
                }
            }

            foreach($user->getFilter() as $filter)
            {
                if(!in_array($filter->getId(), $ids))
                {
                    if($user->isCustomAssignedFilter($filter->getId()))
                    {
                        $user->removeCustomFilter($filter->getId());
                    }
                    else
                    {
                        $user->addCustomExcludedFilter($filter->getId());
                    }
                }
            }


            $filters = $this->em->getRepository(Filter::class)
                ->createQueryBuilder('f')
                ->where("f.id IN (:ids)")
                ->setParameter('ids', $ids)
                ->getQuery()
                ->execute();

            $user->setFilters($filters);
        }
    }
}
