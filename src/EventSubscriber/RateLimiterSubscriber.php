<?php

namespace App\EventSubscriber;

use App\Exception\RateLimiterException;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\RateLimiter\RateLimiterFactory;

class RateLimiterSubscriber implements EventSubscriberInterface
{
    /** @var RateLimiterFactory */
    private RateLimiterFactory $apiLoginLimiter;
    /** @var RateLimiterFactory */
    private RateLimiterFactory $resetPasswordLimiter;
    private LoggerInterface $logger;

    public function __construct(
        RateLimiterFactory $apiLoginLimiter,
        RateLimiterFactory $resetPasswordLimiter,
        LoggerInterface $logger)
    {
        $this->apiLoginLimiter = $apiLoginLimiter;
        $this->resetPasswordLimiter = $resetPasswordLimiter;
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            RequestEvent::class => 'onKernelRequest'
        ];
    }

    public function onKernelRequest(RequestEvent $event): void {
        $request = $event->getRequest();
        $rateLimit = null;

        if (strpos($request->get('_route'), 'api_user_manual_login') !== FALSE) {
            $limiter = $this->apiLoginLimiter->create($request->getClientIp());
            $rateLimit = $limiter->consume();
        } elseif (strpos($request->get('_route'), 'user-reset-password') !== FALSE) {
            $limiter = $this->resetPasswordLimiter->create($request->getClientIp());
            $rateLimit = $limiter->consume();
        }

        if ($rateLimit) {
            if (false === $rateLimit->isAccepted()) {
                throw new RateLimiterException($rateLimit, Response::HTTP_TOO_MANY_REQUESTS);
            }
        }
    }
}
