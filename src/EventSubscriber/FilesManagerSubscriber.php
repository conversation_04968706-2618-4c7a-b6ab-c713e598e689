<?php

namespace App\EventSubscriber;

use App\Entity\FilesManager;
use App\Service\FilesManager\FilesManagerService;
use Doctrine\Common\EventSubscriber;
use Doctrine\ORM\Event\PostRemoveEventArgs;
use Doctrine\ORM\Events;

class FilesManagerSubscriber implements EventSubscriber
{
    private FilesManagerService $filesManagerService;

    public function __construct(FilesManagerService $filesManagerService)
    {
        $this->filesManagerService = $filesManagerService;
    }

    public function getSubscribedEvents(): array
    {
        return [Events::postRemove];
    }

    public function postRemove(PostRemoveEventArgs $args)
    {
        $entityInstance = $args->getObject();
        if ($entityInstance instanceof FilesManager)
        {
            // Remove the file
            $this->filesManagerService->delete($entityInstance);
        }
    }
}
