<?php

namespace App\EventSubscriber;

use App\Utils\TimeZoneConverter\TimeZoneConverterInterface;
use App\Utils\TimeZoneConverter\UtcTimezoneInterface;
use Doctrine\Common\EventSubscriber;
use Doctrine\ORM\Event\PostLoadEventArgs;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Psr\Log\LoggerInterface;

class TimezoneConverterSubscriber implements EventSubscriber
{
    private LoggerInterface $logger;
    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function getSubscribedEvents(): array
    {
        return [
            Events::postLoad,
            Events::prePersist,
            Events::preUpdate
        ];
    }

    public function preUpdate(PreUpdateEventArgs $args)
    {
        $object = $args->getObject();
        if ($object instanceof TimeZoneConverterInterface || $object instanceof UtcTimezoneInterface)
        {
            $object->toUtc();
        }

    }

    public function prePersist(PrePersistEventArgs $args)
    {
        $object = $args->getObject();
        if ($object instanceof TimeZoneConverterInterface || $object instanceof UtcTimezoneInterface)
        {
            $object->toUtc();
        }
    }

    public function postLoad(PostLoadEventArgs $args)
    {
        $object = $args->getObject();
        if ($object instanceof TimeZoneConverterInterface || $object instanceof UtcTimezoneInterface)
        {
            $object->fromUtc(['logger' => $this->logger]);
        }
    }
}
