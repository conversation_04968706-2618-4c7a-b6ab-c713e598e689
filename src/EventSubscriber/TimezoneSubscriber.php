<?php

namespace App\EventSubscriber;

use App\Service\Geolocation\GeolocationService;

use App\Entity\UserTime;
use App\Entity\UserLogin;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Doctrine\Common\EventSubscriber;



class TimezoneSubscriber implements EventSubscriber
{
    private EntityManagerInterface $em;
    private GeolocationService $geolocationService;

    public function __construct(
        EntityManagerInterface $em,
        GeolocationService $geolocationService
    )
    {
        $this->em = $em;
        $this->geolocationService  = $geolocationService;
    }

    public function getSubscribedEvents(): array
    {
        return [
            Events::prePersist,
            Events::preUpdate
        ];
    }

    public function prePersist(PrePersistEventArgs $args) {

        $entityInstance = $args->getObject();

        //quiero que la instanacia sea diferente de userLOgin
        if ($entityInstance instanceof UserLogin) {
            return;
        }

        if (method_exists($entityInstance, 'getTimezone') and
            method_exists($entityInstance, 'getCountry') and
            method_exists($entityInstance, 'getTimezoneCreatedAt') and
            method_exists($entityInstance, 'getTimezoneUpdatedAt')
        ) {
            $timeActual = $this->geolocationService->getTimeActualInTheZone();
            $entityInstance->setTimezone($timeActual->timezone);
            $entityInstance->setCountry($timeActual->country);
            $entityInstance->setTimezoneCreatedAt($timeActual->hourActual);
            $entityInstance->setTimezoneUpdatedAt($timeActual->hourActual);
        }
    }

    public function preUpdate(PreUpdateEventArgs $args) {
        $entityInstance = $args->getObject();
        if (method_exists($entityInstance, 'getTimezone') and
            method_exists($entityInstance, 'getCountry') and
            method_exists($entityInstance, 'getTimezoneCreatedAt') and
            method_exists($entityInstance, 'getTimezoneUpdatedAt')
        ) {
            $timeActual = $this->geolocationService->getTimeActualInTheZone();
            $entityInstance->setTimezoneUpdatedAt($timeActual->hourActual);
        }
    }
}
