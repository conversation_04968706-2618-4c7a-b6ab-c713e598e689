<?php

// src/EventSubscriber/LocaleSubscriber.php
namespace App\EventSubscriber;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Security\Core\Security;

class LocaleSubscriber implements EventSubscriberInterface
{
	private $defaultLocale;
	private $security;

	public function __construct(Security $security, string $defaultLocale = 'es')
	{
		$this->security = $security;
		$this->defaultLocale = $defaultLocale;
	}

	public function onKernelRequest(RequestEvent $event)
	{
		$request = $event->getRequest();

		// No need to proceed if no previous session exists
		if (!$request->hasPreviousSession()) {
			return;
		}

		$request = $event->getRequest();

		if (!$request->hasPreviousSession()) {
			return;
		}
	
		// Set the locale from the session
		$locale = $request->getSession()->get('_locale', $this->defaultLocale) ?? $this->defaultLocale;
		$request->setLocale($locale);
	}

	public static function getSubscribedEvents()
	{
		return [
			KernelEvents::REQUEST => [['onKernelRequest', 20]],
		];
	}
}
