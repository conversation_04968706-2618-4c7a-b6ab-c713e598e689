<?php

declare(strict_types=1);

namespace App\EventSubscriber;

use App\Controller\Admin\ChapterCrudController;
use App\Controller\Admin\CourseCrudController;
use App\Entity\Chapter;
use App\Enum\ChapterContent;
use App\Enum\ChapterContent as EnumChapterType;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Event\AfterCrudActionEvent;
use EasyCorp\Bundle\EasyAdminBundle\Event\AfterEntityPersistedEvent;
use EasyCorp\Bundle\EasyAdminBundle\Event\AfterEntityUpdatedEvent;
use EasyCorp\Bundle\EasyAdminBundle\Event\BeforeCrudActionEvent;
use EasyCorp\Bundle\EasyAdminBundle\Event\BeforeEntityPersistedEvent;
use EasyCorp\Bundle\EasyAdminBundle\Event\BeforeEntityUpdatedEvent;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;
use Twig\Environment;

class ChapterSubscriber implements EventSubscriberInterface
{
    private EntityManagerInterface $em;
    private AdminUrlGenerator $adminUrlGenerator;
    private Environment $twig;
    private RouterInterface $router;
    private RequestStack $requestStack;

    public function __construct(
        EntityManagerInterface $em,
        AdminUrlGenerator $adminUrlGenerator,
        Environment $twig,
        RouterInterface $router,
        RequestStack $requestStack
    ) {
        $this->em = $em;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->twig = $twig;
        $this->router = $router;
        $this->requestStack = $requestStack;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            BeforeEntityPersistedEvent::class => 'beforeEntityPersistedEvent',
            BeforeEntityUpdatedEvent::class => 'beforeEntityUpdatedEvent',
            AfterEntityPersistedEvent::class => 'afterEntityPersistedEvent',
            AfterEntityUpdatedEvent::class => 'afterEntityUpdatedEvent',
            AfterCrudActionEvent::class => 'afterCrudActionEvent',
            BeforeCrudActionEvent::class => 'beforeCrudActionEvent',
        ];
    }

    public function beforeEntityPersistedEvent(BeforeEntityPersistedEvent $event)
    {
        $entity = $event->getEntityInstance();
        if ($entity instanceof Chapter) {
        }
    }

    public function beforeEntityUpdatedEvent(BeforeEntityUpdatedEvent $event)
    {
        $entity = $event->getEntityInstance();
        if ($entity instanceof Chapter) {
        }
    }

    public function afterEntityPersistedEvent(AfterEntityPersistedEvent $event)
    {
        $entity = $event->getEntityInstance();
        if ($entity instanceof Chapter) {
            $request = $this->requestStack->getCurrentRequest();
            $queryParams = $request->query->all();
            $referrer = urldecode($queryParams['referrer']);
            $urlFromCreate = '';
            if (!empty($referrer) && str_contains($referrer, 'courseUrlRedirectNew')) {
                $urlFromCreate = explode('courseUrlRedirectNew', $referrer);
            }

            if ($entity->getStateRedirect() && EnumChapterType::VCMS_CODE !== $entity->getType()->getCode() && EnumChapterType::ROLE_PLAY_CODE !== $entity->getType()->getCode()) {
                $url = $this->adminUrlGenerator
                    ->setController(ChapterCrudController::class)
                    ->set('type', $entity->getType()->getId())  // getId  => getCode
                    ->set('modal', "modal-chapter-{$entity->getType()->getId()}")
                    ->set('referrer', !empty($urlFromCreate) ? $urlFromCreate[1] : null)
                    ->setAction(Action::EDIT)
                    ->setEntityId($entity->getId())
                    ->generateUrl();
            } else {
                $url = null != $urlFromCreate ? $urlFromCreate[1] : $this->adminUrlGenerator->unsetAll()
                    ->setController(CourseCrudController::class)
                    ->setAction('detail')
                    ->setEntityId($entity->getCourse()->getId())
                    ->generateUrl();
            }

            $response = new RedirectResponse($url);

            return $response->send();
        }
    }

    public function afterEntityUpdatedEvent(AfterEntityUpdatedEvent $event)
    {
        $entity = $event->getEntityInstance();
        if ($entity instanceof Chapter) {
            $referrer = $this->adminUrlGenerator
                ->setController(ChapterCrudController::class)
                ->setAction('edit')
                ->setEntityId($entity->getId())
                ->generateUrl();

            if ($entity->getStateRedirect()) {
                $url = $this->adminUrlGenerator
                    ->setController(ChapterCrudController::class)
                    ->set('type', $entity->getType()->getId())
                    ->set('modal', "modal-chapter-{$entity->getType()->getId()}")
                    ->set('referrer', $referrer)
                    ->setAction(Action::EDIT)
                    ->setEntityId($entity->getId())
                    ->generateUrl();
            } else {
                $request = $this->requestStack->getCurrentRequest();
                $queryParams = $request->query->all();
                if (!empty($queryParams['referrer'])) {
                    $referrer = urldecode($queryParams['referrer']);
                    if (str_contains($referrer, 'courseUrlRedirectNew')) {
                        $urlFromCreate = explode('courseUrlRedirectNew', $referrer);
                        $url = $urlFromCreate[1];
                        if (ChapterContent::VIDEO_TYPE == $entity->getType()->getId() && !empty($entity->getVideo()->getUrlVideo())) {
                            if (str_contains($url, '&submenuIndex') && 2 != substr_count($url, '&signature')) {
                                $url = explode('&submenuIndex', $url)[0];
                            } else {
                                $url = explode('&signature', $url)[0] . '&signature=' . explode('signature=', $url)[1];
                            }
                        }
                    } else {
                        $url = $referrer;
                    }
                } else {
                    $url = $this->adminUrlGenerator->unsetAll()
                        ->setController(CourseCrudController::class)
                        ->setAction('detail')
                        ->setEntityId($entity->getCourse()->getId())
                        ->generateUrl();
                }
            }

            $response = new RedirectResponse($url);

            return $response->send();
        }
    }

    public function afterCrudActionEvent(AfterCrudActionEvent $event)
    {
        // dd($event->getResponseParameters());
    }

    public function BeforeCrudActionEvent(BeforeCrudActionEvent $event)
    {
        // dd($event);
    }
}
