<?php

namespace App\EventSubscriber;

use App\Entity\Course;
use App\Entity\Scorm;
use App\Entity\User;
use App\Entity\UserFieldsFundae;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Event\AfterCrudActionEvent;
use EasyCorp\Bundle\EasyAdminBundle\Event\AfterEntityPersistedEvent;
use EasyCorp\Bundle\EasyAdminBundle\Event\BeforeEntityPersistedEvent;
use EasyCorp\Bundle\EasyAdminBundle\Event\BeforeEntityUpdatedEvent;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class EasyAdminSubscriber implements EventSubscriberInterface
{

    private $logger;
    private EntityManagerInterface $em;
    private UserPasswordHasherInterface $userPasswordHasher;

    public function __construct(LoggerInterface $logger, EntityManagerInterface $em, UserPasswordHasherInterface $userPasswordHasher)
    {
        $this->logger = $logger;
        $this->em = $em;
        $this->userPasswordHasher = $userPasswordHasher;
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            AfterEntityPersistedEvent::class => ['unzipScorm'],
            AfterCrudActionEvent::class => ['afterCrudActionEvent'],
            BeforeEntityUpdatedEvent::class => ['BeforeEntityUpdatedEvent'],
            BeforeEntityPersistedEvent::class => ['beforeEntityPersistedEvent'],
        ];
    }

    public function unzipScorm (AfterEntityPersistedEvent $event)
    {
//        $entity = $event->getEntityInstance();
//
//        if(!($entity instanceof Scorm)) {
//            return;
//        }
//
//        $zip = new \ZipArchive();
//        if($zip->open($entity->getScormPackage()) == TRUE) {
//            $folder = new \SplFileInfo($entity->getFolder());
//            $folder = $folder->getBasename('.' . $folder->getExtension());
//
//            $this->logger->error($folder);
//
//            $zip->extractTo(getcwd(). Scorm::SCORM_DIR . '\\' . $folder . '\\');
//            $zip->close();
//
//            $entity->setFolder($folder);
//
//            $this->em->flush();
//        }
    }

    public function afterCrudActionEvent(AfterCrudActionEvent $event) {
        if ($event->getResponseParameters()->get('pageName') != Crud::PAGE_INDEX) {
            $event->getAdminContext()->getCrud()->setSearchFields(null);
        }
    }

    public function beforeEntityPersistedEvent(BeforeEntityPersistedEvent $event) {
        $entity = $event->getEntityInstance();
        if ($entity instanceof User && ($userFieldsFundae = $entity->getUserFieldsFundae()) == null) {
            $userFieldsFundae = new UserFieldsFundae();
            $userFieldsFundae->setUser($entity);
            $entity->setUserFieldsFundae($userFieldsFundae);
        }
    }

    public function beforeEntityUpdatedEvent(BeforeEntityUpdatedEvent $event) {
        $entity = $event->getEntityInstance();
        if ($entity instanceof User && !empty($newPassword = $entity->getNewPassword())) {
            /**
             * Using User $newPassword field when updated to avoid conflict with symfony password auto upgrade feature
             */
            $newHashedPassword = $this->userPasswordHasher->hashPassword($entity, $newPassword);
            $entity->setPassword($newHashedPassword);
        }

        if ($entity instanceof Course) {
            $uow = $this->em->getUnitOfWork();
            $uow->computeChangeSets();
            $changeSet = $uow->getEntityChangeSet($entity);
            if (isset($changeSet['new']) && $entity->getNew()) {
                $entity->setNewAt(new \DateTimeImmutable());
            }
        }
    }
}
