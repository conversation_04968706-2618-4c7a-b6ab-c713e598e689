<?php

declare(strict_types=1);

namespace App\EventSubscriber;

use App\Event\ChapterContentChangedEvent;
use App\Service\Chapter\ChapterProgressResetService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Event subscriber that handles chapter content changes
 * This subscriber is responsible for resetting user progress when chapter content is modified.
 */
class ChapterProgressResetListener implements EventSubscriberInterface
{
    private ChapterProgressResetService $progressResetService;

    public function __construct(ChapterProgressResetService $progressResetService)
    {
        $this->progressResetService = $progressResetService;
    }

    /**
     * Returns an array of event names this subscriber wants to listen to.
     * The array keys are event names and the value can be:
     *
     *  * The method name to call (priority defaults to 0)
     *  * An array composed of the method name to call and the priority
     *
     * @return array<string, array<int, int|string>>
     */
    public static function getSubscribedEvents(): array
    {
        return [
            'chapter.content_changed' => ['onChapterContentChanged', 0],
        ];
    }

    /**
     * Handles the chapter content changed event
     * This method will reset the progress of users in the chapter if it's a SCORM chapter.
     *
     * @param ChapterContentChangedEvent $event The event containing the updated chapter
     */
    public function onChapterContentChanged(ChapterContentChangedEvent $event): void
    {
        $chapter = $event->getChapter();
        $this->progressResetService->resetCurrentProgress($chapter);
    }
}
