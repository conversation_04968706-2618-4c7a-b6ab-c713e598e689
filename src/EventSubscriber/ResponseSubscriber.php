<?php

declare(strict_types=1);

namespace App\EventSubscriber;

use App\V2\Domain\Security\TokenInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Security\Core\Security;

readonly class ResponseSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private LoggerInterface $logger,
        private Security $security,
        private TokenInterface $tokenInterface,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::RESPONSE => 'onKernelResponse',
        ];
    }

    public function onKernelResponse(ResponseEvent $event): void
    {
        $response = $event->getResponse();

        $this->postValidateToken($event);

        $this->logger->debug(
            'response event: ',
            [
                'code' => $response->getStatusCode(),
                'content' => $response->getContent(),
                'headers' => $response->headers->all(),
            ]
        );
    }

    private function postValidateToken(ResponseEvent $event): void
    {
        $request = $event->getRequest();
        $authorization = $request->headers->get('Authorization');
        if (null !== $authorization) {
            // A bearer is set, let the front handle unauthorized if fails
            return;
        }

        $user = $this->security->getUser();
        if (null === $user) {
            return; // We don't have access to the user, let the response continue. A call to refresh_token must be made
        }
        $jwtCookie = $request->cookies->get('BEARER');
        $refreshTokenCookie = $request->cookies->get('refresh_token');
        if (null !== $jwtCookie && null !== $refreshTokenCookie) {
            return; // Cookies valid
        }

        // We have a user, but one of the cookies has expired.
        // Instead of separate refresh token, we can do the refresh here to have
        // $jwtTokenTtl time available
        $this->tokenInterface->setTokensCookie($event->getResponse(), $user);
    }
}
