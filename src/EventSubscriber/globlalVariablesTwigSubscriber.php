<?php

namespace App\EventSubscriber;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ControllerEvent;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Yaml\Yaml;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;
use Twig\Environment;
use App\Service\SettingsService;

use App\Admin\Traits\LanguagesTrait;

class globlalVariablesTwigSubscriber implements EventSubscriberInterface
{
    private Environment $twig;
    private ParameterBagInterface $params;
    private LoggerInterface $logger;
    private TokenStorageInterface $storage;
    private SettingsService $settings;

    use LanguagesTrait;

    public function __construct(
        Environment           $twig,
        ParameterBagInterface $params,
        LoggerInterface       $logger,
        TokenStorageInterface $storage,
        SettingsService $settingsService)
    {
        $this->twig = $twig;
        $this->params = $params;
        $this->logger = $logger;
        $this->storage = $storage;
        $this->settings = $settingsService;
    }

    public function onControllerEvent(ControllerEvent $event)
    {

        if ($event->getRequest()->getPathInfo() == '/admin') {
	        $token = $this->storage->getToken();
          
            $user = $token ? $token->getUser() : null;
            $locale = $user && $user->getLocale() != '' ? $user->getLocale() : $this->params->get('app.adminDefaultLanguage');
            $languages = $this->settings->get('app.languages');
            $languagesName = array_flip($this->getLanguages());

            $translationFile = "{$this->params->get('kernel.project_dir')}/translations/messages.{$locale}.yaml";

            if (file_exists($translationFile)) {
                $translationMessage = Yaml::parseFile($translationFile);
            } else {
                $translationMessage = Yaml::parseFile("{$this->params->get('kernel.project_dir')}/translations/messages.{$this->params->get('app.adminDefaultLanguage')}.yaml");
            }
            $this->twig->addGlobal('translationsMessage', $translationMessage);
            $this->twig->addGlobal('languagesCampus', $languages);
            $this->twig->addGlobal('languagesName', $languagesName);
            $this->twig->addGlobal('languagesAdmin', $this->settings->get('app.languages.admin')); 
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ControllerEvent::class   => 'onControllerEvent'
        ];
    }

    private function getLanguagesName($languajes){

    }
}
