<?php

namespace App\DataFixtures\Catalog;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\CatalogTrait;
use Doctrine\Persistence\ObjectManager;

class CatalogFixtures extends BaseFixtures
{
    use CatalogTrait;
    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Catalog', 'CatalogFixtures'];
    }
}