# Fixtures

## Execute all fixtures

```bash
symfony console doctrine:fixtures:load # Use --help for other command information
```

## Use groups when is required to execute specific fixtures
### In the fixture implement Doctrine\Bundle\FixturesBundle\FixtureGroupInterface

```php
class ExampleFixture extends Doctrine\Bundle\FixturesBundle\Fixture implements Doctrine\Bundle\FixturesBundle\FixtureGroupInterface {
    public function load(\Doctrine\Persistence\ObjectManager $manager){
        // Do internal logic
    }
    
    public static function getGroups() : array{
        return ['Default', 'ExampleFixture']
    }
}
```

### Call the specific fixture by group
```bash
symfony console d:f:l --group ExampleFixture --append # Use append to avoid clearing database
```
