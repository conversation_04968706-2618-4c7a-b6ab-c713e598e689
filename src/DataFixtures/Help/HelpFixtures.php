<?php

namespace App\DataFixtures\Help;

use App\Entity\HelpCategory;
use App\Entity\HelpText;
use App\Resources\DataFixtureBase\Help\HelpData;
use App\Resources\Traits\Catalog\HelpTrait;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;

class HelpFixtures extends Fixture implements FixtureGroupInterface
{
    use HelpTrait;
    public function load(ObjectManager $manager) : void
    {
        $this->saveHelp();
    }

    public static function getGroups(): array
    {
        return ['Default', 'HelpFixtures'];
    }
}
