<?php

namespace App\DataFixtures\General;

use App\DataFixtures\BaseFixtures\BaseFixtures;

use App\Resources\Traits\Catalog\AppTrait;
use Doctrine\Persistence\ObjectManager;


class AppFixtures extends BaseFixtures
{
    use AppTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->saveAppData();
    }

    public static function getGroups(): array
    {
        return ['General', 'AppFixtures'];
    }
}
