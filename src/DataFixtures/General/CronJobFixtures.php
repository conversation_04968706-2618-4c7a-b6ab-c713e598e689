<?php

namespace App\DataFixtures\General;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\CronJobTrait;
use App\Resources\Traits\Catalog\CronJobTimeoutTrait;
use Doctrine\Persistence\ObjectManager;

class CronJobFixtures extends BaseFixtures
{
    use CronJobTrait;
    use CronJobTimeoutTrait;

    public function load(ObjectManager $manager) : void
    {
       $this->saveCronJon();
       $this->saveCronJobTimeouts();
    }

    public static function getGroups(): array
    {
        return ['Default', 'CronJobFixtures'];
    }
}
