<?php

namespace App\DataFixtures\General;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\UserTrait;
use Doctrine\Persistence\ObjectManager;

class UserFixtures extends BaseFixtures
{
    use UserTrait;
    public function load(ObjectManager $manager) : void
    {
        $this->saveUserData();
    }

    public static function getGroups(): array
    {
        return ['general', 'UserFixtures'];
    }
}
