<?php

namespace App\DataFixtures\General;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\SectionDefaultTrait;
use Doctrine\Persistence\ObjectManager;

class SectionDefaultFixtures extends BaseFixtures
{
    use SectionDefaultTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase($manager);
    }

    public static function getGroups(): array
    {
        return ['default', 'SectionDefaultFixtures'];
    }
}
