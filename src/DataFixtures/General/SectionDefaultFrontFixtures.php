<?php

namespace App\DataFixtures\General;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\SectionDefaultFrontTrait;
use Doctrine\Persistence\ObjectManager;

class SectionDefaultFrontFixtures extends BaseFixtures
{

    use SectionDefaultFrontTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase($manager);
    }

    public static function getGroups(): array
    {
        return ['default', 'SectionDefaultFrontFixtures'];
    }

}
