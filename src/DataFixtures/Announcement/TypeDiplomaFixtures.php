<?php

namespace App\DataFixtures\Announcement;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\TypeDiplomaTrait;
use Doctrine\Persistence\ObjectManager;

class TypeDiplomaFixtures extends BaseFixtures
{
    use TypeDiplomaTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Default', 'TypeDiplomaFixtures'];
    }
}
