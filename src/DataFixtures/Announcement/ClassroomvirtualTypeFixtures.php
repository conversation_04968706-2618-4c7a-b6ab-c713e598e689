<?php

namespace App\DataFixtures\Announcement;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\ClassroomVirtualTypeTrait;
use Doctrine\Persistence\ObjectManager;

class ClassroomvirtualTypeFixtures extends BaseFixtures
{

    use ClassroomVirtualTypeTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Default', 'ClassroomvirtualTypeFixtures'];
    }
}
