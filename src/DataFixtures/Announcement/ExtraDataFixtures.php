<?php

namespace App\DataFixtures\Announcement;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\ExtraDataTrait;
use Doctrine\Persistence\ObjectManager;

class ExtraDataFixtures extends BaseFixtures
{

    use ExtraDataTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Default', 'ExtraDataFixtures'];
    }
}
