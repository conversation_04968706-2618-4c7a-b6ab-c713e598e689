<?php

namespace App\DataFixtures\Announcement;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Entity\AnnouncementConfigurationType;
use App\Entity\AnnouncementConfigurationTypeTranslation;
use App\Entity\ConfigurationClientAnnouncement;
use App\Entity\TypeIdentification;
use App\Resources\DataFixtureBase\Announcement\AnnouncementConfigurationTypeData;
use App\Resources\Traits\Catalog\AnnouncementConfigurationTypeTrait;
use Doctrine\Persistence\ObjectManager;

class AnnouncementConfigurationTypeFixtures extends BaseFixtures
{
    use AnnouncementConfigurationTypeTrait;
    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
        $this->addTypeDocumentationMain();
    }

    public static function getGroups(): array
    {
        return ['Default', 'AnnouncementConfigurationTypeFixtures'];
    }
}
