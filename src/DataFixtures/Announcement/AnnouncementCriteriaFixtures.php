<?php

namespace App\DataFixtures\Announcement;


use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\AnnouncementCriteriaTrait;
use Doctrine\Persistence\ObjectManager;

class AnnouncementCriteriaFixtures extends BaseFixtures
{
    use AnnouncementCriteriaTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Default', 'AnnouncementCriteriaFixtures'];
    }
}
