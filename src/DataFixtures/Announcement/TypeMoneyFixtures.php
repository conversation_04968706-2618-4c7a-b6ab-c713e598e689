<?php

namespace App\DataFixtures\Announcement;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\TypeMoneyTrait;
use Doctrine\Persistence\ObjectManager;

class TypeMoneyFixtures extends BaseFixtures
{

    use TypeMoneyTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Default', 'TypeMoneyFixtures'];
    }
}
