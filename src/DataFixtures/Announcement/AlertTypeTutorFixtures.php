<?php

namespace App\DataFixtures\Announcement;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\AlertTypeTutorTrait;
use Doctrine\Persistence\ObjectManager;


class AlertTypeTutorFixtures extends BaseFixtures
{
    use AlertTypeTutorTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
        $this->assignAlertByTypeCourse();
    }

    public static function getGroups(): array
    {
        return ['Default', 'AlertTypeTutorFixtures'];
    }
}
