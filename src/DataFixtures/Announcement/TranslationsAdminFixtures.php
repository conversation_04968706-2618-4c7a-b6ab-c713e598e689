<?php

namespace App\DataFixtures\Announcement;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\TranslationsAdminTrait;
use Doctrine\Persistence\ObjectManager;

class TranslationsAdminFixtures  extends  BaseFixtures
{
    use TranslationsAdminTrait;

    public function load(ObjectManager $manager) : void
    {
       $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Default', 'TranslationsAdminFixtures']; 
    }
}
