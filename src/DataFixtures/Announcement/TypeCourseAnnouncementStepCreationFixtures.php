<?php

namespace App\DataFixtures\Announcement;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\TypeCourseAnnouncementStepCreationTrait;
use Doctrine\Persistence\ObjectManager;

class TypeCourseAnnouncementStepCreationFixtures extends BaseFixtures
{

    use TypeCourseAnnouncementStepCreationTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
        $this->saveToConfigurationTypeCourse();
    }

    public static function getGroups(): array
    {
        return ['Default', 'TypeCourseAnnouncementStepCreationFixtures'];
    }
}
