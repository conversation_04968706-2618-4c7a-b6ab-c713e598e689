<?php

namespace App\DataFixtures\Announcement;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\TypeIdentificationTrait;
use Doctrine\Persistence\ObjectManager;

class TypeIdentificationFixtures extends BaseFixtures
{
    use TypeIdentificationTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Default', 'TypeIdentificationFixtures'];
    }
}
