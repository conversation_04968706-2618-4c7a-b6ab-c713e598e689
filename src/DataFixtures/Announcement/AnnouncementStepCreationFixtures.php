<?php

namespace App\DataFixtures\Announcement;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Entity\AnnouncementStepCreation;
use App\Resources\DataFixtureBase\Announcement\AnnouncementStepCreationData;
use App\Resources\Traits\Catalog\AnnouncementStepCreationTrait;
use Doctrine\Persistence\ObjectManager;


class AnnouncementStepCreationFixtures extends BaseFixtures
{
    use AnnouncementStepCreationTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Default', 'AnnouncementStepCreationFixtures'];
    }
}
