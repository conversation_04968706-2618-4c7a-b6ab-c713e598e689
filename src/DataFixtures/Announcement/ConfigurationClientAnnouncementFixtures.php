<?php

namespace App\DataFixtures\Announcement;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\ConfigurationClienteAnnouncementTrait;
use Doctrine\Persistence\ObjectManager;

class ConfigurationClientAnnouncementFixtures extends BaseFixtures
{

    use ConfigurationClienteAnnouncementTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Default', 'ConfigurationClientAnnouncementFixtures'];
    }
}
