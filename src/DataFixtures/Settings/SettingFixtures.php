<?php

namespace App\DataFixtures\Settings;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\SettingTrait;
use Doctrine\Persistence\ObjectManager;

class SettingFixtures extends BaseFixtures
{
    use SettingTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->saveSetting();
    }

    public static function getGroups(): array
    {
        return ['Default', 'SettingFixtures'];
    }

}