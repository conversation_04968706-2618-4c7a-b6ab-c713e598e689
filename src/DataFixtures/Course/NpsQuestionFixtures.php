<?php

namespace App\DataFixtures\Course;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\NpsQuestionTrait;
use Doctrine\Persistence\ObjectManager;

class NpsQuestionFixtures extends BaseFixtures
{
    use NpsQuestionTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Course', 'NpsQuestionFixtures'];
    }
}
