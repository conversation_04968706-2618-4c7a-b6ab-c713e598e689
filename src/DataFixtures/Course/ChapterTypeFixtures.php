<?php

namespace App\DataFixtures\Course;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\ChapterTypeTrait;
use Doctrine\Persistence\ObjectManager;

class ChapterTypeFixtures extends BaseFixtures
{
    use ChapterTypeTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Course', 'ChapterTypeFixtures'];
    }

    

   
}
