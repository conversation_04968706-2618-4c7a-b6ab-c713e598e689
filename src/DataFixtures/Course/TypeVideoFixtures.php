<?php

namespace App\DataFixtures\Course;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Entity\TypeVideo;
use App\Resources\DataFixtureBase\Course\TypeVideoData;
use App\Resources\Traits\Catalog\TypeVideoTrait;
use Doctrine\Persistence\ObjectManager;

class TypeVideoFixtures extends BaseFixtures
{
    use TypeVideoTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Course', 'TypeVideoFixtures'];
    }
}
