<?php

namespace App\DataFixtures\Course;

use App\Resources\Traits\Catalog\CourseCategoryTrait;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;

class CourseCategoryFixtures extends Fixture implements FixtureGroupInterface
{
    use CourseCategoryTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->saveCourseCategory();
    }

    public static function getGroups(): array
    {
        return ['Course', 'CourseCategoryFixtures'];
    }
}
