<?php

namespace App\DataFixtures\Course;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Resources\Traits\Catalog\TypeCourseTrait;
use Doctrine\Persistence\ObjectManager;

class TypeCourseFixtures extends BaseFixtures
{
    use TypeCourseTrait;

    public function load(ObjectManager $manager) : void
    {
        $this->loadDataBase();
    }

    public static function getGroups(): array
    {
        return ['Course', 'TypeCourseFixtures'];
    }
}
