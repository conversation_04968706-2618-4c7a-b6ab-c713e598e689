<?php

namespace App\DataFixtures\BaseFixtures;

use App\Resources\Traits\FixturesTrait;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;

abstract class BaseFixtures extends Fixture implements FixtureGroupInterface
{
    const PARAMETERS_BASE =  [
        'fieldsToTranslate' => ['name', 'description'],
        'setFields' => ['name' => 'setName', 'description' => 'setDescription'],
        'fieldState' => ['active' => 'setActive'],
        'setFieldsToTranslations' => ['name' => 'setName', 'description' => 'setDescription'],
        'parametersQuery' => ['id'],
        'fileTrans' => 'fixtures'
    ];

    use FixturesTrait;
}
