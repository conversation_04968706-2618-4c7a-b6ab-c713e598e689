# BaseFixtures
BaseFixtures define una clase abstracta que puede ser extendida para crear fixtures personalizados dentro de easylearning

## Uso
- Crea una nueva clase de fixtures que extienda la clase BaseFixtures.
- Implementa los métodos abstractos según tus necesidades.
- Utiliza los métodos proporcionados para cargar datos en la base de datos.

## Características Principales
- Carga de datos en la base de datos.
- Traducción de datos para múltiples idiomas.
- Manejo de entidades y sus traducciones de forma dinámica.
-Verificación de la existencia de archivos YAML de traducción


### Función  getDefaultData() 

La función `getDefaultData()` devuelve un array con valores predeterminados que se utilizan durante el proceso de carga de fixtures. Estos valores incluyen:

- `'data'`: Representa los datos que se cargarán como fixtures en la base de datos. Por defecto, es un array vacío.
  
- `'parameters'`: Contiene parámetros adicionales que pueden ser necesarios durante la carga de fixtures. También se inicializa como un array vacío.

- `'baseEntity'`: Especifica la entidad base sobre la cual se cargarán los datos. Por defecto, se establece en `null`.

- `'translationEntity'`: Indica la entidad de traducción asociada con la entidad base, si es aplicable. Por defecto, también es `null`.

En conjunto, estos valores proporcionan una estructura básica para la carga de datos en la base de datos, permitiendo la personalización y configuración según sea necesario para cada conjunto particular de fixtures.

## Parámetros de la constante PARAMETERS_BASE:

1. `fieldsToTranslate`: Campos que necesitan ser traducidos.
2. `setFields`: Métodos setters para establecer valores de campos en las entidades.
3. `fieldState`: Estado de un campo en una entidad.
4. `setFieldsToTranslations`: Métodos setters para establecer valores de campos traducidos en las entidades de traducción.
5. `parametersQuery`: Son Parámetros para buscar un elemento en base de datos.

> Este ultimo elememento simula una búsqueda asi findOneBy(["id" => 1]) 

Estos parámetros sirven como valores predeterminados que pueden ser sobrescritos en la clase que hereda. Al sobrescribirlos, puedes personalizar cómo se manejan la traducción de campos y la configuración de valores en las entidades. 

### Ejemplo de uso
```php
<?php

namespace App\DataFixtures\Announcement;

use App\DataFixtures\BaseFixtures\BaseFixtures;
use App\Entity\AlertTypeTutor;
use App\Entity\AlertTypeTutorTranslation;
use Doctrine\Persistence\ObjectManager;

class AlertTypeTutorFixtures extends BaseFixtures
{

    const DEFAULT_DATA = [
        [
            'id' => 1,
            'name' => 'alert_type_tutor.1.name',
            'description' => 'alert_type_tutor.1.description',
            'active' => true,
        ]            
    ];

    public function load(ObjectManager $manager) : void
    {
       $this->loadDataBase($manager);
    }

    protected function getDefaultData(): array
    {   
        return [
            'data' => self::DEFAULT_DATA,
            'parameters' => self::PARAMETERS_BASE,
            'baseEntity' => AlertTypeTutor::class,
            'translationEntity' => AlertTypeTutorTranslation::class,        
        ];
    }

    public static function getGroups(): array
    {
        return ['Default', 'AlertTypeTutorFixtures'];
    }
}


```