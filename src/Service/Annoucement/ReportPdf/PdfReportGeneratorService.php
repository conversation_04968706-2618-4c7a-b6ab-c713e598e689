<?php

declare(strict_types=1);

namespace App\Service\Annoucement\ReportPdf;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\HistoryDeliveryTask;
use App\Entity\Message;
use App\Entity\TaskCourse;
use App\Entity\TaskUser;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Enum\Games;
use App\Service\Annoucement\Admin\AnnouncementGroupService;
use App\Service\Annoucement\Admin\AnnouncementUserService;
use App\Service\Annoucement\ReportPdf\User\ReportActivitiesService;
use App\Service\Annoucement\ReportPdf\User\ReportConexionsService;
use App\Service\Annoucement\ReportPdf\User\ReportUserChatService;
use App\V2\Infrastructure\Utils\MpdfFactory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment as TwigEnvironment;

class PdfReportGeneratorService
{
    private $em;
    private $MpdfFactory;
    private $twig;
    private $announcementUserService;
    private $announcementGroupService;
    private $reportConexionsService;
    private $reportActivitiesService;
    private $chatService;

    private $kernelInterface;

    private $translator;

    public function __construct(
        EntityManagerInterface $em,
        MpdfFactory $MpdfFactory,
        TwigEnvironment $twig,
        AnnouncementUserService $announcementUserService,
        AnnouncementGroupService $announcementGroupService,
        ReportConexionsService $reportConexionsService,
        ReportActivitiesService $reportActivitiesService,
        ReportUserChatService $chatService,
        KernelInterface $kernelInterface,
        TranslatorInterface $translator,
        readonly private string $pdfCssPath,
    ) {
        $this->em = $em;
        $this->MpdfFactory = $MpdfFactory;
        $this->twig = $twig;
        $this->announcementUserService = $announcementUserService;
        $this->announcementGroupService = $announcementGroupService;
        $this->reportConexionsService = $reportConexionsService;
        $this->reportActivitiesService = $reportActivitiesService;
        $this->chatService = $chatService;
        $this->kernelInterface = $kernelInterface;
        $this->translator = $translator;
    }

    public function informePdfAnnouncement(Announcement $announcement, $idGroup = null)
    {
        $mPdf = $this->initializeMpdf();

        $this->setupPdfLayout($mPdf);
        $this->generatePdfContent($announcement, $mPdf, $idGroup);

        return $mPdf;
    }

    private function initializeMpdf()
    {
        return $this->MpdfFactory->createMpdfObject([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_header' => 5,
            'margin_footer' => 5,
            'orientation' => 'P',
        ]);
    }

    private function getAnnouncementUsers(Announcement $announcement, $idGroup)
    {
        $announcementUserRepository = $this->em->getRepository(AnnouncementUser::class);
        $announcementUsers = $announcementUserRepository->findBy(['announcement' => $announcement, 'announcementGroup' => $idGroup]);

        return $announcementUsers;
    }

    private function setupPdfLayout(\Mpdf\Mpdf $mPdf): void
    {
        $mPdf->SetTopMargin('30');
        $mPdf->SetHTMLHeader($this->twig->render('layout/headerpdf.html.twig'));
        $mPdf->SetFooter('{PAGENO}');
    }

    private function generatePdfContent($announcement, $mPdf, $idGroup): void
    {
        $userCourses = [];
        $userChapters = [];
        $messages = [];
        $timeByDay = [];
        $sumTimeDay = [];
        $forumMessages = [];
        $taskUser = [];
        $informationExtraUser = [];

        $messageRepository = $this->em->getRepository(Message::class);
        // $forumRepository = $this->em->getRepository(ForumPost::class);
        $announcementUsers = $this->getAnnouncementUsers($announcement, $idGroup);
        // $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(['announcement' => $announcement]);

        foreach ($announcementUsers as $announcementUser) {
            $userCourse = $this->em->getRepository(UserCourse::class)->findOneBy([
                'announcement' => $announcement,
                'user' => $announcementUser->getUser(),
            ]);

            $userCourses[$announcementUser->getUser()->getId()] = $userCourse;

            if ($userCourse) {
                foreach ($userCourse->getChapters() as $chapter) {
                    $userChapters[$announcementUser->getUser()->getId()][$chapter->getChapter()->getId()] = $chapter;
                    if (date_format($chapter->getStartedAt(), 'Y-m-d') == date_format($chapter->getStartedAt(), 'Y-m-d')) {
                        $timeByDay[$announcementUser->getUser()->getId()][] = date_format($chapter->getStartedAt(), 'Y-m-d');
                    }
                }

                $unique = array_unique($timeByDay[$announcementUser->getUser()->getId()]);

                $suma = 0;
                $sumaDay = [];
                foreach ($unique as $uni) {
                    foreach ($userCourse->getChapters() as $chapter) {
                        if ($uni === date_format($chapter->getStartedAt(), 'Y-m-d')) {
                            $suma += $chapter->getTimeSpent();
                        }
                    }
                    $elem['date'] = $uni;
                    $elem['total'] = $suma;

                    array_push($sumaDay, $elem);
                    $sumTimeDay[$announcementUser->getUser()->getId()] = $sumaDay;
                    $suma = 0;
                }
            }

            $taskUser[$announcementUser->getUser()->getId()] = $this->fetchTaskAnnouncement($announcementUser->getUser(), $announcementUser->getAnnouncement());
            $informationExtraUser[$announcementUser->getUser()->getId()] = $this->getInformationExtraUser($announcementUser);
        }

        $mPdf->WriteHTML($this->twig->render(
            'fundae/report_pdf_general/main.html.twig',
            [
                'announcement' => $announcement,
                'informationGroup' => $this->announcementGroupService->getInformationGroup($announcement, $idGroup),
                'userCourses' => $userCourses,
                'userChapters' => $userChapters,
                'announcementUsers' => $announcementUsers,
                'messages' => [], // Initialize messages array
                'timeByDay' => $sumTimeDay,
                'taskUser' => $taskUser,
                'informationExtraUser' => $informationExtraUser,
            ]
        ));
    }

    private function getInformationExtraUser(AnnouncementUser $announcementUser)
    {
        return [
            'progressTotalCourse' => $this->announcementUserService->getProgressTotal($announcementUser),
            'progressTask' => $this->announcementUserService->getProgressUserTask($announcementUser),
            'downloadDiploma' => $this->announcementUserService->confirmDownloadedDiploma($announcementUser),
            'timeSpent' => $this->announcementUserService->getTotalInTheCourse($announcementUser),
            'progressTotalHour' => $this->announcementUserService->getProgressTotalHour($announcementUser),
        ];
    }

    private function fetchTaskAnnouncement(User $user, Announcement $announcement): array
    {
        $taskRepository = $this->em->getRepository(TaskCourse::class);
        $taskAnnouncement = $taskRepository->findBy(
            [
                'announcement' => $announcement->getId(),
                'isVisible' => true,
            ],
            [
                'dateDeliveryAnnouncement' => 'asc',
            ]
        );

        $taskCourse = $taskRepository->findBy([
            'course' => $announcement->getCourse(),
            'announcement' => null,
            'isVisible' => true,
        ]);

        $tasks = array_merge($taskCourse, $taskAnnouncement);
        $taskAll = [];

        foreach ($tasks as $task) {
            $taskUser = $this->getUserTasks($task, $user);
            $historyTask = $this->getHistoryTask($taskUser);

            $taskAll[] = [
                'title' => $task->getTitle(),
                'description' => $task->getDescription(),
                'state' => $historyTask ? \intval($historyTask->getState()) : 0,
                'updatedAt' => $historyTask ? $historyTask->getUpdatedAt() : '',
            ];
        }

        return $taskAll;
    }

    private function getUserTasks(TaskCourse $task, User $user): ?TaskUser
    {
        return $this->em->getRepository(TaskUser::class)->findOneBy(['task' => $task, 'user' => $user]);
    }

    private function getHistoryTask(?TaskUser $taskUser): ?HistoryDeliveryTask
    {
        return $taskUser ? $this->em->getRepository(HistoryDeliveryTask::class)->findOneBy(['taskUser' => $taskUser]) : null;
    }

    public function generatePdfReportUser(AnnouncementUser $announcementUser, ?User $userResume = null)
    {
        $mPdf = $this->MpdfFactory->createMpdfObject([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_header' => 5,
            'margin_footer' => 5,
            'orientation' => 'P',
        ]);
        $user = $userResume ?? $announcementUser->getUser();
        $mPdf->SetTopMargin('30');
        if (!file_exists($this->pdfCssPath)) {
            $isTest = true;
            $css = 'body { font-family: sans-serif; }';
        } else {
            $isTest = false;
            $css = file_get_contents($this->pdfCssPath);
        }
        $mPdf->WriteHTML($css, \Mpdf\HTMLParserMode::HEADER_CSS);
        $headerImage = $this->kernelInterface->getProjectDir() . DIRECTORY_SEPARATOR . 'public/assets/login/logo.png';
        $title = $this->translator->trans('menu.title_platform', [], 'messages', $user->getLocale());
        $mPdf->SetHTMLHeader('
                <table style="border-bottom: solid 3px #CED4DAFF;" style="width: 100%">
                    <tr>
                        <td width="20%">
                            <img  style="width: 80px;  height:25px; padding:0px" src="' . $headerImage . '">
                        </td>
                        <td ><p style="font-size: 16px; margin-bottom: 1rem; margin-left: 8rem">' . $title . '</p></td>
                    </tr>
                </table>
                <hr>
            ');
        $mPdf->SetFooter('{PAGENO}');
        $dataBase = $this->reportActivitiesService->getHeadReportUser($announcementUser, $user);
        $dataMessages = $this->chatService->getUserChat($announcementUser);
        $dataExtra = [
            'conexions' => $this->reportConexionsService->getConexionsUser($announcementUser),
            'taskUser' => $this->reportActivitiesService->fetchTaskAnnouncement($announcementUser->getUser(), $announcementUser->getAnnouncement()),
            'resultsTypes' => [Games::QUIZ_TYPE, Games::WHEEL_TYPE, Games::PUZZLE_TYPE],
        ];

        $dataReport = array_merge($dataBase, $dataExtra, $dataMessages);

        $mPdf->WriteHTML($isTest ? '<div></div>' : $this->twig->render(
            'fundae/report_pdf_by_user/main.html.twig',
            $dataReport
        ));

        return $mPdf;
        // return $this->MpdfFactory->createDownloadResponse($mPdf, "{$announcementUser->getUser()->getFirstName()}.pdf");
    }
}
