<?php

declare(strict_types=1);

namespace App\Service\Permission;

use App\Entity\Course;
use App\Entity\User;
use App\Repository\CourseRepository;
use App\V2\Domain\Course\Creator\CourseCreatorCriteria;
use App\V2\Domain\Course\Creator\CourseCreatorNotFoundException;
use App\V2\Domain\Course\Creator\CourseCreatorRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\ORM\NonUniqueResultException;

readonly class CoursePermissionService
{
    public function __construct(
        private CourseCreatorRepository $courseCreatorRepository,
        private CourseRepository $courseRepository,
    ) {
    }

    public function canCreate(User $user): bool
    {
        return $this->hasAnyRole($user, [
            User::ROLE_SUPER_ADMIN,
            User::ROLE_ADMIN,
            User::ROLE_CREATOR,
        ]);
    }

    /**
     * @throws InfrastructureException
     * @throws NonUniqueResultException
     */
    public function canEdit(User $user, Course $course): bool
    {
        if ($this->hasAnyRole($user, [User::ROLE_SUPER_ADMIN, User::ROLE_ADMIN])) {
            return true;
        }

        if ($user->isManager()) {
            return $this->isCourseSharedWithManager($user, $course);
        }

        return $this->hasRole($user, User::ROLE_CREATOR)
            && $this->isCreatorOrSharedWith($user, $course);
    }

    /**
     * @throws InfrastructureException
     */
    public function canManageCourseSeason(User $user, Course $course): bool
    {
        if ($this->hasAnyRole($user, [User::ROLE_SUPER_ADMIN, User::ROLE_ADMIN])) {
            return true;
        }

        return $this->hasRole($user, User::ROLE_CREATOR)
            && $this->isCreatorOrSharedWith($user, $course);
    }

    public function canDelete(User $user, Course $course): bool
    {
        return $this->canEdit($user, $course);
    }

    /**
     * @throws InfrastructureException
     */
    public function isCreatorOrSharedWith(User $user, Course $course): bool
    {
        if (!$user->isCreator()) {
            return false;
        }

        if ($course->getCreatedBy()?->getId() === $user->getId()) {
            return true;
        }

        try {
            $this->courseCreatorRepository->findOneBy(
                CourseCreatorCriteria::createEmpty()
                    ->filterByUserId(new Id($user->getId()))
                    ->filterByCourseId(new Id($course->getId()))
            );

            return true;
        } catch (CourseCreatorNotFoundException) {
            return false;
        }
    }

    /**
     * @throws NonUniqueResultException
     */
    public function isCourseSharedWithManager(User $user, Course $course): bool
    {
        if (!$user->isManager()) {
            return false;
        }

        if (0 === $user->getManagedCourses()->count()) {
            return true;
        }

        return $this->courseRepository->canManagerAccessCourse(course: $course, user: $user);
    }

    private function hasRole(User $user, string $role): bool
    {
        return \in_array($role, $user->getRoles(), true);
    }

    private function hasAnyRole(User $user, array $roles): bool
    {
        return [] !== array_intersect($user->getRoles(), $roles);
    }
}
