<?php

declare(strict_types=1);

namespace App\Service\Nps;

use App\Entity\Nps;
use App\Entity\ZipFileTask;
use App\Enum\StatsReportType;
use App\Utils\SpreadsheetUtil;
use App\Utils\ToolsUtils;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class ZipFileOpinionsService
{
    private LoggerInterface $logger;
    protected EntityManagerInterface $em;
    private ZipFileOpinionsService $opinionsService;
    protected TranslatorInterface $trans;
    private Security $security;

    private string $baseDir;

    private $locale = 'es';
    private array $params = [];

    private array $saveReport = [];

    public function __construct(
        LoggerInterface $logger,
        TranslatorInterface $trans,
        Security $security,
        EntityManagerInterface $em,
        KernelInterface $kernel
    ) {
        $this->logger = $logger;
        $this->trans = $trans;
        $this->security = $security;
        $this->em = $em;

        $this->baseDir = $kernel->getProjectDir() . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'opinions-stats-reports';
    }

    private function getUser()
    {
        return $this->security->getUser();
    }

    public function setLocale($locale)
    {
        $this->locale = $locale;
    }

    public function setParams(array $params)
    {
        $this->params = $params;
    }

    public function getParams(): array
    {
        return $this->params;
    }

    public function setSaveReport(string $namefile)
    {
        $this->saveReport[] = $this->getBaseDir() . DIRECTORY_SEPARATOR . $namefile;
    }

    public function getSaveReport(): array
    {
        return $this->saveReport;
    }

    public function createSpreadSheet(string $fileName, string $title = 'DEFAULT'): SpreadsheetUtil
    {
        return new SpreadsheetUtil(
            $this->getTransReport($fileName),
            $this->getTransReport($title)
        );
    }

    public function getTransReport($wordTranslate, $tag = 'reports'): string
    {
        return $this->trans->trans($wordTranslate, [], $tag, $this->locale);
    }

    public function getBaseDir(): string
    {
        $baseDir = $this->baseDir;
        if (isset($this->params['baseDir'])) {
            $baseDir = $this->params['baseDir'];
        }

        if (!file_exists($baseDir)) {
            mkdir($baseDir);
        }

        return $baseDir;
    }

    public function generateZipFileTaskOpinion(Request $request): ZipFileTask
    {
        $content = json_decode($request->getContent(), true);
        $announcementId = $content['announcementId'] ?? null;
        $courseIds = $content['courseIds'] ?? null;

        $content['userId'] = $this->getUser()->getId();

        if (empty($courseIds) && empty($announcementId)) {
            throw new \Exception('courses or announcement must be defined');
        }

        if (!empty($announcementId)) {
            unset($content['announcementId']);
        }

        $zipFileTask = new ZipFileTask();
        $zipFileTask
            ->setEntityId(empty($announcementId) ? '1' : (string) $announcementId)
            ->setType(StatsReportType::OPINIONS)
            ->setParams($content)
            ->setTask('opinion_report')
            ->setStatus(ZipFileTask::STATUS_PENDING)
            ->setOriginalName('opinions-stats-report - ' . (new \DateTimeImmutable())->format('YmdHis'));

        return $zipFileTask;
    }

    public function createQueryOpinions(array $params = []): QueryBuilder
    {
        $qb = $this->em->getRepository(Nps::class)
            ->createQueryBuilder('nps')
            ->join('nps.course', 'uc')
            ->join('uc.course', 'c')
            ->join('nps.question', 'nq')
        ;

        return $qb;
    }

    public function queryParamsOpinions(QueryBuilder $qb, array $params = []): QueryBuilder
    {
        $queryParams = 0 != \count($params) ? $params : $this->getParams();

        if (!empty($queryParams['announcementId'])) {
            $qb->andWhere('nps.announcement = :announcementId')
                ->setParameter('announcementId', $queryParams['announcementId']);
        } else {
            $qb->andWhere('nps.announcement IS NULL');
        }

        if (!empty($queryParams['start'])) {
            $qb->andWhere('nps.createdAt >= :start')
                ->setParameter('start', new \DateTime($queryParams['start']));
        }

        if (!empty($queryParams['end'])) {
            $qb->andWhere('nps.createdAt <= :end')
                ->setParameter('end', new \DateTime($queryParams['end']));
        }

        if (!empty($queryParams['isEmptyComment'])) {
            $qb->andWhere(
                $qb->expr()->andX(
                    $qb->expr()->isNotNull('nps.value'),
                    $qb->expr()->neq('nps.value', ':emptyString')
                )
            )
                ->setParameter('emptyString', '');
        }

        if (!empty($queryParams['surveyId'])) {
            $qb->join('nps.question', 'q')
                ->where('q.survey = :surveyId')
                ->setParameter('surveyId', $queryParams['surveyId']);
        }

        if (!empty($queryParams['query'])) {
            $qb->andWhere('c.name LIKE :query OR c.code LIKE :query')
                ->setParameter('query', $queryParams['query']);
        }

        if (!empty($queryParams['courseIds'])) {
            if (\is_array($queryParams['courseIds'])) {
                $qb->andWhere($qb->expr()->in('c.id', $queryParams['courseIds']));
            } else {
                $qb->where('uc.course = :courseId')
                    ->setParameter('courseId', $queryParams['courseIds']);
            }
        }

        return $qb;
    }

    private function addSelectionFielsCourseOpinions(QueryBuilder $qb): QueryBuilder
    {
        return $qb->select(
            'c.id',
            'c.code',
            'c.name as courseName',
        );
    }

    public function courseFormativeOpinionsPagination(int $offset, $pageSize): array
    {
        $query = $this->createQueryOpinions();
        $query = $this->queryParamsOpinions($query);
        $query = $this->addSelectionFielsCourseOpinions($query);
        $query->groupBy('c.id');

        $qb = clone $query;
        $qb->setMaxResults($pageSize)
            ->setFirstResult($offset * $pageSize);

        return $qb->getQuery()->getResult(AbstractQuery::HYDRATE_ARRAY);
    }

    public function npsFormativeOpinions(int $courseId): array
    {
        $query = $this->createQueryOpinions();
        $query->andWhere('c.id = :id')->setParameter('id', $courseId);
        $query->select(
            'nq.id',
            'nq.question',
            'nq.type',
            'concat(u.firstName,\' \', u.lastName) as autor',
            'nq.active'
        );
        $query->join('nps.createdBy', 'u');
        $qb = clone $query;

        return $qb->getQuery()->getResult(AbstractQuery::HYDRATE_ARRAY);
    }

    public function npsQuestionCourseData(int $courseId): array
    {
        $query = $this->createQueryOpinions();
        $query->andWhere('c.id = :id')->setParameter('id', $courseId);
        $query->select(
            'u.id as userId',
            'u.code',
            'u.firstName',
            'u.lastName',
            'u.email',
            'nq.id surveyId',
            'nq.question',
            'nq.type',
            'nps.value',
            'nps.createdAt'
        );
        $query->join('nps.user', 'u');
        $qb = clone $query;

        return $qb->getQuery()->getResult(AbstractQuery::HYDRATE_ARRAY);
    }

    public function countOpinionsCourse($course, $question): int
    {
        $qb = $this->createQueryOpinions();
        $qb->andWhere('uc.course = :course')
            ->andWhere('nps.question = :question')
            ->setParameter('course', $course)
            ->setParameter('question', $question);

        return (int) $qb->select('COUNT(DISTINCT nps.id) as total')
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function getSaveFileDirectoryNameOpinions(array $courseInfo): string
    {
        $completName = trim($courseInfo['code']) . ToolsUtils::str_without_accents(trim($courseInfo['courseName']));

        return $this->getBaseDir() . DIRECTORY_SEPARATOR . $completName;
    }

    public function validateCourse()
    {
        if (!isset($this->params['courseIds'])) {
            throw new \Exception('courseIds is required');
        }
    }

    public function validateAnnouncementId()
    {
        if (isset($this->params['announcementId'])) {
            if (1 !== $this->getCountCourseOpinionsAnnouncement()) {
                throw new \Exception('The number of courses in the call is different from 1');
            }
        }
    }

    public function getCountCourseOpinionsAnnouncement(): int
    {
        $query = $this->createQueryOpinions();
        $query->andWhere('nps.announcement = :announcementId')
            ->setParameter('announcementId', $this->params['announcementId']);

        return (int) $query->select('COUNT(DISTINCT c.id) as total')
            ->getQuery()
            ->getSingleScalarResult();
    }
}
