<?php

declare(strict_types=1);

namespace App\Service\Course;

use App\Entity\Course;
use App\Entity\RoleplayProject;
use App\Entity\RoleplaySequence;
use Doctrine\ORM\EntityManagerInterface;

class CloneRolePlayService
{
    private EntityManagerInterface $em;

    public function __construct(EntityManagerInterface $entityManagerInterface)
    {
        $this->em = $entityManagerInterface;
    }

    public function cloneRolePlay(Course $course): void
    {
        $chapters = $course->getChapters();
        if (\count($chapters)) {
            foreach ($chapters as $chapter) {
                if ($chapter->getRoleplayProject()) {
                    $roleplay = clone $chapter->getRoleplayProject();
                    $chapter->setRoleplayProject($roleplay);
                    $this->cloneRolePlayParts($roleplay);
                }
            }
        }
    }

    public function cloneRolePlayParts(RoleplayProject $roleplayProject)
    {
        $sceneMap = [];
        $endingMap = [];

        $endings = $roleplayProject->getEndings();
        $roleplayProject->initializeEndings();
        foreach ($endings as $ending) {
            $newEnding = clone $ending;
            $this->em->persist($newEnding);
            $this->em->flush();

            $endingMap[$ending->getId()] = $newEnding->getId();

            $roleplayProject->addEnding($newEnding);
        }

        $sequences = $roleplayProject->getSequences();
        $roleplayProject->initializeSequences();
        foreach ($sequences as $sequence) {
            $newSequence = clone $sequence;
            $this->em->persist($newSequence);
            $this->em->flush();
            $roleplayProject->addSequence($newSequence);
            $this->cloneScenes($newSequence, $sceneMap);
            $newSequences[] = $this->em->getRepository(RoleplaySequence::class)->find($newSequence->getId());
        }

        foreach ($newSequences as $ns) {
            $this->cloneAnswers($ns, $sceneMap, $endingMap);
        }

        $beginnings = $roleplayProject->getBeginnings();
        $roleplayProject->initializeBeginnings();
        foreach ($beginnings as $beginning) {
            $newBeginning = clone $beginning;
            $roleplayProject->addBeginning($newBeginning);
        }
    }

    public function cloneScenes(RoleplaySequence $sequence, array &$sceneMap)
    {
        $scenes = $sequence->getScenes();
        $sequence->initializeScenes();

        foreach ($scenes as $scene) {
            $newScene = clone $scene;
            $this->em->persist($newScene);
            $this->em->flush();

            $sceneMap[$scene->getId()] = $newScene->getId();
            $sequence->addScene($newScene);
        }
    }

    public function cloneAnswers(RoleplaySequence $sequence, &$mapOfScene, &$endingMap)
    {
        $scenes = $sequence->getScenes();
        foreach ($scenes as $scene) {
            $newAnswers = [];
            $answers = $scene->getAnswers();
            foreach ($answers as $answer) {
                $linkedId = $answer['linkedScene'];
                $sceneLinkId = substr($linkedId, 1);
                if (str_starts_with($linkedId, 's')) {
                    $newLinkedId = isset($mapOfScene[$sceneLinkId]) ? 's' . $mapOfScene[$sceneLinkId] : $linkedId;
                } elseif (str_starts_with($linkedId, 'e')) {
                    $newLinkedId = isset($endingMap[$sceneLinkId]) ? 'e' . $endingMap[$sceneLinkId] : $linkedId;
                }
                $newAnswers[] = [
                    'id' => $answer['id'],
                    'text' => $answer['text'],
                    'feedback' => $answer['feedback'],
                    'points' => $answer['points'],
                    'linkedScene' => $newLinkedId,
                    'order' => $answer['order'],
                ];
                $scene->setAnswers($newAnswers);
                $this->em->persist($scene);
            }
            $this->em->flush();
        }
    }
}
