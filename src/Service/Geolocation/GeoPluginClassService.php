<?php

declare(strict_types=1);

namespace App\Service\Geolocation;

class GeoPluginClassService
{
    // the geoPlugin server
    public $host = 'http://ip-api.com/php/{IP}?fields=status,message,continent,continentCode,country,countryCode,region,regionName,city,zip,lat,lon,timezone,currency';

    // the default base currency
    public $currency = 'USD';

    // the default language
    public $lang = 'en';
    /*
    supported languages:
    de
    en
    es
    fr
    ja
    pt-BR
    ru
    zh-CN
    */

    // initiate the geoPlugin vars
    public $ip;
    public $city;
    public $region;
    public $regionCode;
    public $regionName;
    public $dmaCode;
    public $countryCode;
    public $countryName;
    public $inEU;
    public $euVATrate = false;
    public $continentCode;
    public $continentName;
    public $latitude;
    public $longitude;
    public $locationAccuracyRadius;
    public $timezone;
    public $currencyCode;
    public $currencySymbol;
    public $currencyConverter;

    public function __construct()
    {
    }

    public function locate($ip = null)
    {
        global $_SERVER;

        try {
            if (\is_null($ip)) {
                $ip = $_SERVER['REMOTE_ADDR'] ?? '';
            }

            $host = str_replace('{IP}', $ip, $this->host);

            $response = $this->fetch($host);

            if (false === $response) {
                // Puedes establecer valores predeterminados en caso de error.
                $this->setDefaultValues();

                return;
            }

            $data = unserialize($response);

            if (
                !\is_array($data)
                || !isset($data['status'])
                || 'success' !== $data['status']
            ) {
                $this->setDefaultValues();

                return;
            }

            // set the geoPlugin vars
            $this->ip = $ip;
            $this->city = $data['city'];
            $this->region = $data['regionName'];
            $this->regionCode = $data['region'];
            $this->regionName = $data['regionName'];
            $this->dmaCode = null;
            $this->countryCode = $data['countryCode'];
            $this->countryName = $data['country'];
            $this->inEU = false;
            $this->continentCode = $data['continentCode'];
            $this->continentName = $data['continent'];
            $this->latitude = $data['lat'];
            $this->longitude = $data['lon'];
            $this->locationAccuracyRadius = null;
            $this->timezone = $data['timezone'];
            $this->currencyCode = $data['currency'];
            $this->currencySymbol = null;
            $this->currencyConverter = null;
        } catch (\Exception $e) {
            // Manejo de excepciones
            $this->setDefaultValues();
        }
    }

    private function setDefaultValues()
    {
        // Establecer valores por defecto en caso de error.
        $this->ip = null;
        $this->city = 'Madrid';
        $this->region = 'Madrid'; // Otra opción podría ser 'Community of Madrid' dependiendo de tus necesidades
        $this->regionCode = 'MD';  // Código de región para Madrid (puedes ajustarlo según necesites)
        $this->regionName = 'Madrid';
        $this->dmaCode = null;
        $this->countryCode = 'ES'; // Código de país para España (puedes ajustarlo según necesites)
        $this->countryName = 'Spain';
        $this->inEU = true; // Madrid está en la Unión Europea (puedes ajustarlo según necesites)
        // $this->euVATrate = false;
        $this->continentCode = 'EU'; // Código de continente para Europa (puedes ajustarlo según necesites)
        $this->continentName = 'Europe';
        $this->latitude = 40.4168; // Latitud de Madrid (puedes ajustarlo según necesites)
        $this->longitude = -3.7038; // Longitud de Madrid (puedes ajustarlo según necesites)
        $this->locationAccuracyRadius = null;
        $this->timezone = 'Europe/Madrid'; // Zona horaria para Madrid (puedes ajustarlo según necesites)
        $this->currencyCode = 'EUR'; // Código de moneda para Euro (puedes ajustarlo según necesites)
        $this->currencySymbol = '€'; // Símbolo de moneda para Euro (puedes ajustarlo según necesites)
        $this->currencyConverter = 1.0; // Tasa de conversión (puedes ajustarlo según necesites)
    }

    public function fetch($host)
    {
        if (\function_exists('curl_init')) {
            // use cURL to fetch data
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $host);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            $response = curl_exec($ch);
            curl_close($ch);
        } elseif (\ini_get('allow_url_fopen')) {
            // fall back to fopen()
            $response = file_get_contents($host);
        } else {
            trigger_error('geoPlugin class Error: Cannot retrieve data. Either compile PHP with cURL support or enable allow_url_fopen in php.ini ', E_USER_ERROR);

            return false;
        }

        return $response;
    }

    public function convert($amount, $float = 2, $symbol = true)
    {
        // easily convert amounts to geolocated currency.
        if (!is_numeric($this->currencyConverter) || 0 == $this->currencyConverter) {
            trigger_error('geoPlugin class Notice: currencyConverter has no value.', E_USER_NOTICE);

            return $amount;
        }
        if (!is_numeric($amount)) {
            trigger_error('geoPlugin class Warning: The amount passed to geoPlugin::convert is not numeric.', E_USER_WARNING);

            return $amount;
        }
        if (true === $symbol) {
            return $this->currencySymbol . round($amount * $this->currencyConverter, $float);
        } else {
            return round($amount * $this->currencyConverter, $float);
        }
    }
}
