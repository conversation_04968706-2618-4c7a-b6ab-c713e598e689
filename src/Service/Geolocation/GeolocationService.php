<?php

declare(strict_types=1);

namespace App\Service\Geolocation;

use App\Entity\UserLogin;
use App\Service\General\IpService;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;

class GeolocationService
{
    private $em;
    private $requestStack;
    private $security;
    private IpService $ipService;

    public function __construct(EntityManagerInterface $em, RequestStack $requestStack, Security $security, IpService $ipService)
    {
        $this->em = $em;
        $this->requestStack = $requestStack;
        $this->security = $security;
        $this->ipService = $ipService;
    }

    public function getGeoplugin()
    {
        $geoplugin = new GeoPluginClassService();
        $request = $this->requestStack->getCurrentRequest();
        $ip = $request ? $request->getClientIp() : null;

        // $geoplugin->locate("*************"); //para prueba local (cambiar la IP)
        $geoplugin->locate($ip); // para prueba en un servidor real

        return $geoplugin;
    }

    public function getTimeZoneConnection()
    {
        $ip = $this->ipService->getClientIp();

        $defaultTimezone = 'Europe/Madrid';

        if ('127.0.0.1' === $ip || '::1' === $ip || 'localhost' === $ip) {
            return $defaultTimezone;
        }

        $timezone = $this->getTimezoneConnectionUser();

        return $timezone ?? $defaultTimezone;
    }

    public function getTimeActualInTheZone()
    {
        $ip = $this->ipService->getClientIp();
        $timezone = $this->getTimeZoneConnection();
        $dateTimeZone = new \DateTimeZone($timezone);
        $country = $dateTimeZone->getLocation()['country_code'];

        $timeActual = new \stdClass();
        $timeActual->timezone = $timezone;
        $timeActual->ip = $ip;
        $timeActual->country = $country;
        $timeActual->hourActual = new \DateTime('now', new \DateTimeZone($timeActual->timezone));

        return $timeActual;
    }

    /**
     * Convert the #date to a new timezone.
     *
     * @param \DateTime|\DateTimeImmutable $date
     *
     * @return \DateTimeImmutable Return a datetime object with the new timezone
     *
     * @throws \Exception
     */
    public function fromTimezoneToTimezone($date, string $sourceTz, string $dstTimezone): \DateTimeImmutable
    {
        if ($date instanceof \DateTime || $date instanceof \DateTimeImmutable) {
            $srcTz = new \DateTimeZone($sourceTz);
            $dstTz = new \DateTimeZone($dstTimezone);
            $tmp = new \DateTimeImmutable($date->format('Y-m-d H:i:s'), $srcTz);

            return $tmp->setTimezone($dstTz);
        }

        return $date;
    }

    public function convertTimeZone($date, $originTimeZone, $destinationTimeZone)
    {
        $dateToDateTime = $date->format('Y-m-d H:i:s');

        $originalDateTime = new \DateTime($dateToDateTime, new \DateTimeZone($originTimeZone));

        $destinationDateTime = $originalDateTime->setTimezone(new \DateTimeZone($destinationTimeZone));

        $convertedDate = $destinationDateTime->format($this->getFormatDate());

        return $convertedDate;
    }

    private function getFormatDate()
    {
        $locale = $this->security->getUser() && $this->security->getUser()->getLocale() ? $this->security->getUser()->getLocale() : 'es';

        return 'en' === $locale ? 'Y-m-d H:i:s' : 'd-m-Y H:i:s';
    }

    private function getTimezoneConnectionUser()
    {
        $user = $this->security->getUser();
        if (!$user) {
            return $this->getGeoplugin()->timezone;
        }

        $userLoginRepository = $this->em->getRepository(UserLogin::class);
        $count = $userLoginRepository->countByUserFromDate($user, new \DateTime('-2 hour'));
        if ($count) {
            $userLogin = $this->em->getRepository(UserLogin::class)->findOneBy(['user' => $user], ['createdAt' => 'DESC']);

            return $userLogin->getTimezone() ?? 'Europe/Madrid';
        }

        return $this->getGeoplugin()->timezone;
    }
}
