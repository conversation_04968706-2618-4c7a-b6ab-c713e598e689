<?php

declare(strict_types=1);

namespace App\Service\StatsUser;

use App\Controller\Admin\CourseCrudController;
use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\CourseSection;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Enum\AnnouncementState;
use App\Repository\AnnouncementRepository;
use App\Service\Annoucement\Admin\AnnouncementUserService;
use App\Service\Announcement\Stats\AnnouncementStatsService;
use App\Service\Api\AbstractBaseService;
use App\Service\Api\ApiCourseService;
use App\Service\Api\ItineraryUserService;
use App\Service\Api\TeleformationDetailService;
use App\Service\Course\DT0\UserCourseStatsDTO;
use App\Service\SettingsService;
use App\Service\UserCourse\UserCourseService;
use App\Utils\TimeUtils;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\RequestStack;

class StatsUserService extends AbstractBaseService
{
    private const string COURSE_ID_FIELD = 'id';
    private const string SORT_FIELD = 'sort';
    private const string SORT_ORDER_ASC = 'ASC';
    private const array PROPERTIES_TO_REMOVE = ['tags', 'segments', 'courseCurrentlyViewed', 'description'];
    private const string ORPHANED_COURSE_INDICATOR = 'orphaned';

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        private AnnouncementRepository $announcementRepository,
        private ApiCourseService $apiCourseService,
        private ItineraryUserService $itineraryUserService,
        private AnnouncementUserService $announcementUserService,
        private ResultGameService $resultGameService,
        private AdminUrlGenerator $adminUrlGenerator,
        private RequestStack $requestStack,
        private TeleformationDetailService $teleformationDetailService,
        private ParameterBagInterface $params,
        private UserCourseService $userCourseService,
        private AnnouncementStatsService $announcementStatsService
    ) {
        parent::__construct($em, $settings);
    }

    public function getVoluntaryTrainingUser(User $user, ?CourseSection $courseSection = null): array
    {
        $locale = $user->getLocale() ?? $this->settings->get('app.defaultLanguage');

        if ($courseSection) {
            $categories = $courseSection->getCategories();
        } else {
            $categories = $this->em->getRepository(CourseCategory::class)->findBy([], [self::SORT_FIELD => self::SORT_ORDER_ASC]);
        }

        $courses = [];

        foreach ($categories as $category) {
            $categoryData = $this->apiCourseService->getCoursesUsersOpenVisibles($user, $category, $locale);
            if (!empty($categoryData['courses'])) {
                $courses[] = $this->getStructureCourse($categoryData['courses'], $user);
            }
        }

        $orphanedCourses = $this->getOrphanedCoursesForUser($user, $locale, $courses);
        if (!empty($orphanedCourses)) {
            $courses[] = $orphanedCourses;
        }

        $coursesAssigned = array_merge($this->getItinerariesUser($user), $this->getCoursesByFilter($user));

        $courses = array_merge(...$courses);

        $filteredCourses = array_filter($courses, function ($course) use ($coursesAssigned) {
            if (!isset($course['id'])) {
                return false;
            }

            return !\in_array($course['id'], array_column($coursesAssigned, 'id'));
        });

        return array_merge($filteredCourses);
    }

    /**
     * Get orphaned courses for user - courses with progress but no current assignment.
     * This method extracts course IDs from already assigned courses to avoid duplicates.
     */
    private function getOrphanedCoursesForUser(User $user, string $locale, array $assignedCoursesArrays): array
    {
        // Extract course IDs from all assigned course arrays to avoid duplicates
        $assignedCourseIds = [];
        foreach ($assignedCoursesArrays as $coursesArray) {
            foreach ($coursesArray as $courseData) {
                if (isset($courseData[self::COURSE_ID_FIELD])) {
                    $assignedCourseIds[] = $courseData[self::COURSE_ID_FIELD];
                }
            }
        }

        // Use the centralized repository method with exclusion list
        $courses = $this->em->getRepository(UserCourse::class)
            ->findOrphanedCoursesWithProgressForUser($user, $assignedCourseIds, $locale);

        $orphanedCoursesData = [];

        foreach ($courses as $course) {
            // Convert course to API format
            $courseData = $this->apiCourseService->courseToSend($user, $course);
            if (!empty($courseData)) {
                // Mark as orphaned course for frontend identification
                $courseData[self::ORPHANED_COURSE_INDICATOR] = true;
                $orphanedCoursesData[] = $courseData;
            }
        }

        // Apply the same structure transformation as other courses
        $structuredCourses = $this->getStructureCourse($orphanedCoursesData, $user);

        // Ensure the orphaned flag is preserved after structure transformation
        foreach ($structuredCourses as &$structuredCourse) {
            $structuredCourse[self::ORPHANED_COURSE_INDICATOR] = true;
        }

        return $structuredCourses;
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getAnnouncementUser(User $user, ?bool $isDiplomaReport = false): array
    {
        $announcements = $this->announcementRepository->findAnnouncementByUser($user);
        $courses = [];
        foreach ($announcements as $announcement) {
            $announcementToSend = $this->apiCourseService
                ->courseToSendAnnouncement($user, $announcement, $isDiplomaReport);
            $courses[] = $this->getStructureAnnouncement([$announcementToSend], $user, $announcement, $isDiplomaReport);
        }

        return array_merge(...$courses);
    }

    public function getItinerariesUser(User $user): array
    {
        $itineraries = $this->itineraryUserService->formatItinerariesData($user);
        $courses = [];
        foreach ($itineraries as $itinerary) {
            $courses[] = $this->getStructureCourse($itinerary['courses'], $user);
        }

        return array_merge(...$courses);
    }

    public function getCoursesByFilter(User $user): array
    {
        $itineraries = $this->getItinerariesUser($user);
        $coursesByFilter = $this->apiCourseService->getCoursesUserFilterAccess($user);

        $filteredCourses = array_filter($coursesByFilter, function ($course) use ($itineraries) {
            if (!isset($course[self::COURSE_ID_FIELD])) {
                return false;
            }

            return !\in_array($course[self::COURSE_ID_FIELD], array_column($itineraries, self::COURSE_ID_FIELD));
        });

        if (empty($filteredCourses)) {
            return [];
        }

        return $this->getStructureCourse($filteredCourses, $user);
    }

    /**
     * @throws NonUniqueResultException
     */
    private function getStructure(array $items, User $user, $announcement = null, ?bool $isDiplomaReport = false): array
    {
        $structuredData = [];
        $locale = $user->getLocale() ?? $this->settings->get('app.defaultLanguage');

        foreach ($items as $item) {
            foreach (self::PROPERTIES_TO_REMOVE as $property) {
                unset($item[$property]);
            }

            $course = isset($item[self::COURSE_ID_FIELD]) ? $this->em->getRepository(Course::class)->find($item[self::COURSE_ID_FIELD]) : null;

            if ($course) {
                $item['state'] = $announcement ? $this->getStateAnnouncement($announcement, $user) : $this->getStateFromCourse($item);
                $item['image'] = $item['image'] ? $this->settings->get('app.course_uploads_path') . '/' . $item['image'] : null;
                $item['type'] = [
                    'id' => $course->getTypeCourse() ? $course->getTypeCourse()->getId() : null,
                    'name' => $course->getTypeCourse() ? $course->getTypeCourse()->getName() : null,
                ];
                $item['startAt'] = !\is_null($item['startAt']) ? $item['startAt']->format('c') : null;
                $item['finishAt'] = !\is_null($item['finishAt']) ? $item['finishAt']->format('c') : null;
                $item['category'] = [
                    'id' => $course->getCategory() ? $course->getCategory()->getId() : null,
                    'name' => $course->getCategory() ? $this->apiCourseService->getCategoryName($course->getCategory(), $locale) : null, // $course->getCategory() ? $course->getCategory()->getName() : null//,
                ];

                $finishedChapters = 0;
                foreach ($this->getUserChapterData($user, $course, $announcement) as $status) {
                    if (UserCourse::STATUS_FINISHED == $status['status']) {
                        ++$finishedChapters;
                    }
                }

                if ($announcement) {
                    $chapterSessionFinished = $this->announcementStatsService->getTotalFinishedAnnoucementSessions($announcement);
                    $totalChapters = $this->announcementStatsService->getTotalAnnoucementSessions($announcement, true);
                    if (TypeCourse::TYPE_MIXTO == $announcement->getCourse()->getTypeCourse()->getId() || TypeCourse::TYPE_TELEFORMACION == $announcement->getCourse()->getTypeCourse()->getId()) {
                        $totalChapters += $course->getTotalChapter();
                    }
                    $survey = $this->announcementStatsService->getAnnouncementUserValuedCourse($announcement, $user);
                    $item['timeSpent'] = $this->getTimeSpentAnnouncement($announcement, $user);
                    $item['chaptersFinished'] = $chapterSessionFinished + $finishedChapters;
                    $item['chapterDetails'] = !$isDiplomaReport ? $this->getStructureDetailCourseAnnouncement($announcement, $user) : [];
                    $item['totalChapters'] = $totalChapters;
                    $item['survey'] = $survey ? $survey->format('c') : null;
                } else {
                    $userCourse = $this->getUserCourse($user, $course);
                    $item['timeSpent'] = $this->getTimeUserCourse($user, $course);
                    $item['chaptersFinished'] = $finishedChapters;
                    $item['chapterDetails'] = $this->getUserChapterData($user, $course);
                    $item['totalChapters'] = $course->getTotalChapter();
                    $item['survey'] = $userCourse && $userCourse->getValuedAt() ? $userCourse->getValuedAt()->format('c') : null;
                }

                $item['urlCourse'] = $this->generateUrlByEasyAdmin($course);
                $structuredData[] = $item;
            }
        }

        return $structuredData;
    }

    private function getUserChapterData($user, $course, $announcement = null): array
    {
        $request = $this->requestStack->getCurrentRequest();
        $content = !\is_null($request) ? json_decode($request->getContent(), true) : [];
        $chapters = $course->getChapters();
        $courseFinishedIntime = filter_var($content['courseFinishedIntime'] ?? false, FILTER_VALIDATE_BOOLEAN);
        $courseStartedIntime = filter_var($content['courseStartedIntime'] ?? false, FILTER_VALIDATE_BOOLEAN);
        $dateFrom = null;
        $dateTo = null;
        if (!empty($content['dateFrom'])) {
            $dateFrom = \DateTimeImmutable::createFromFormat('Y-m-d', $content['dateFrom']);
        }

        if (!empty($content['dateTo'])) {
            $dateTo = \DateTimeImmutable::createFromFormat('Y-m-d', $content['dateTo']);
        }
        /** @var UserCourse|null $userCourse */
        $userCourseStatsDTO = UserCourseStatsDTO::create([
            'course' => $course,
            'user' => $user,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'announcementId' => $announcement ? $announcement->getId() : null,
            'courseStartedOnTime' => $courseStartedIntime,
            'courseFinishedOnTime' => $courseFinishedIntime,
        ]);
        $userCourse = $this->em->getRepository(UserCourse::class)->userCourseGetData($userCourseStatsDTO);
        $result = [];
        $started = 0;
        foreach ($chapters as $chapter) {
            $result[] = $this->userCourseService->getUserChaptersData($chapter, $userCourse, $started, $this->params);
        }

        return $result;
    }

    private function getTimeSpentAnnouncement(Announcement $announcement, User $user)
    {
        $course = $announcement->getCourse();

        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
            'user' => $user,
            'announcement' => $announcement,
        ]);

        if (!$course->getTypeCourse()) {
            return 0;
        }

        if (
            TypeCourse::TYPE_PRESENCIAL == $course->getTypeCourse()->getId()
            || TypeCourse::TYPE_AULA_VIRTUAL == $course->getTypeCourse()->getId()
        ) {
            return $announcement->getTotalHours() > 0
                ? TimeUtils::formatTime($announcement->getTotalHours() * 3600)
                : TimeUtils::formatTime(3600);
        }

        return $announcementUser ? $this->announcementUserService->getTotalInTheCourse($announcementUser) : '00:00:00';
    }

    private function getStructureCourse(array $courses, User $user): array
    {
        return $this->getStructure($courses, $user);
    }

    /**
     * @throws NonUniqueResultException
     */
    private function getStructureAnnouncement(
        array $courses,
        User $user,
        Announcement $announcement,
        ?bool $isDiplomaReport = false,
    ): array {
        return $this->getStructure($courses, $user, $announcement, $isDiplomaReport);
    }

    private function getStateFromCourse($course): string
    {
        // Check finished status first (most reliable indicator)
        if (isset($course['finished']) && $course['finished']) {
            return AnnouncementState::STATE_FINISHED;
        }

        // Check dates to determine state - use null coalescing for safety
        $startAt = $course['startAt'] ?? null;
        $finishAt = $course['finishAt'] ?? null;

        if (null === $startAt) {
            return AnnouncementState::STATE_NOT_STARTED;
        }

        if (null !== $finishAt) {
            return AnnouncementState::STATE_FINISHED;
        }

        // If started but not finished
        return AnnouncementState::STATE_IN_PROGRESS;
    }

    private function getUserCourse(User $user, Course $course, ?Announcement $announcement = null): ?UserCourse
    {
        if (null != $announcement) {
            return $this->em->getRepository(UserCourse::class)->findOneBy([
                'user' => $user,
                'announcement' => $announcement,
            ]);
        }

        // For orphaned courses, we want the UserCourse without announcement
        return $this->em->getRepository(UserCourse::class)->findOneBy([
            'user' => $user,
            'course' => $course,
            'announcement' => null,
        ]);
    }

    private function getTimeUserCourse(User $user, Course $course): string
    {
        $userCourse = $this->getUserCourse($user, $course);

        if (!$userCourse) {
            return '00:00:00';
        }

        $time = 0;
        foreach ($userCourse->getChapters() as $chapter) {
            $time += $chapter->getTimeSpent();
        }

        return $userCourse ? TimeUtils::formatTime($time) : '00:00:00';
    }

    private function fetchUserCourseChapter(?UserCourse $userCourse = null, $chapter): ?UserCourseChapter
    {
        return $this->em->getRepository(UserCourseChapter::class)->findOneBy([
            'userCourse' => $userCourse,
            'chapter' => $chapter,
        ]);
    }

    private function getStateUserChapter(?UserCourse $userCourse = null, $chapter): string
    {
        $userCourseChapter = $this->fetchUserCourseChapter($userCourse, $chapter);
        if (null == $userCourseChapter) {
            return AnnouncementState::STATE_NOT_STARTED;
        }

        if (null != $userCourseChapter->getFinishedAt()) {
            return AnnouncementState::STATE_FINISHED;
        }
        if (null != $userCourseChapter->getStartedAt() && null == $userCourseChapter->getFinishedAt()) {
            return AnnouncementState::STATE_IN_PROGRESS;
        }
        if (null == $userCourseChapter->getStartedAt()) {
            return AnnouncementState::STATE_NOT_STARTED;
        }

        return AnnouncementState::STATE_NOT_STARTED;
    }

    private function getTimeSpentUserChapter(?UserCourse $userCourse = null, $chapter): string
    {
        $userCourseChapter = $this->fetchUserCourseChapter($userCourse, $chapter);

        return $userCourseChapter ? TimeUtils::formatTime($userCourseChapter->getTimeSpent()) : '00:00:00';
    }

    private function generateUrlByEasyAdmin(Course $course): string
    {
        return $this->adminUrlGenerator
            ->setController(CourseCrudController::class)
            ->setAction(Action::DETAIL)
            ->setEntityId($course->getId())
            ->generateUrl();
    }

    private function getStructureCourseAnnouncement(Announcement $announcement, User $user): array
    {
        $dataCourseAnnouncement = $this->teleformationDetailService->seasonDataAnnouncement($announcement, $user);

        $course = $announcement->getCourse();

        if (!$course->getTypeCourse()) {
            return [];
        }

        switch ($course->getTypeCourse()->getId()) {
            case TypeCourse::TYPE_PRESENCIAL:
            case TypeCourse::TYPE_AULA_VIRTUAL:
                return $this->getDetailCoursePresential($dataCourseAnnouncement);
            case TypeCourse::TYPE_TELEFORMACION:
                return $this->getDetailCourseOnline($user, $course);
            case TypeCourse::TYPE_MIXTO:
                return $this->getDetailCourseMixture($dataCourseAnnouncement, $course, $user);
            default:
                return [];
        }
    }

    private function getDetailCourseOnline(User $user, Course $course): array
    {
        $userCourse = $this->getUserCourse($user, $course);
        $usersChapters = [];

        $chapters = $course->getChapters();
        $host = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost();

        foreach ($chapters as $chapter) {
            $userCourseChapter = $this->fetchUserCourseChapter($userCourse, $chapter);

            $usersChapters[] = [
                'id' => $chapter->getId(),
                'idUserCourseChapter' => $userCourseChapter ? $userCourseChapter->getId() : null,
                'image' => $chapter->getImage() ? $this->settings->get('app.chapter_uploads_path') . '/' . $chapter->getImage() : null,
                'title' => $chapter->getTitle(),
                'state' => $this->getStateUserChapter($userCourse, $chapter),
                'startedAt' => $userCourseChapter ? $userCourseChapter->getStartedAt() : null,
                'finishedAt' => $userCourseChapter ? $userCourseChapter->getFinishedAt() : null,
                'timeSpent' => $this->getTimeSpentUserChapter($userCourse, $chapter),
                'isGame' => $chapter->getType()->isGame(),
                'type' => 'Sesión',
                'resultsGame' => $userCourseChapter ? $this->resultGameService->getResultGameAttempts($userCourseChapter) : [],
            ];
        }

        return $usersChapters;
    }

    private function getDetailCoursePresential($dataCoursePresential): array
    {
        if (!$dataCoursePresential) {
            return [];
        }

        $chapters = $dataCoursePresential[0]['chapters'];
        $host = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost();

        $sesions = [];
        foreach ($chapters as $session) {
            $sesions[] = [
                'id' => $session['id'],
                'title' => $session['name'],
                'image' => null,
                'state' => $this->getStateSessionOnSite($session['assistance']),
                'startedAt' => $session['assistance'] ? $session['startAt']->format('c') : null,
                'finishedAt' => $session['assistance'] ? $session['finishAt']->format('c') : null,
                'timeSpent' => $session['assistance'] ? TimeUtils::formatTime($session['sessionTimeInMinutes'] * 60) : '00:00:00',
                'sessionTimeInMinutes' => $session['sessionTimeInMinutes'],
                'isGame' => false,
                'type' => 'Sesión',
            ];
        }

        return $sesions;
    }

    private function getDetailCourseMixture($dataCourseMixture, Course $course, User $user): array
    {
        if (!$dataCourseMixture) {
            return [];
        }

        $userCourse = $this->getUserCourse($user, $course);

        $chapters = $dataCourseMixture[0]['chapters'];
        $host = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost();

        $sesions = [];
        foreach ($chapters as $session) {
            $chapter = isset($session['title']) ? $this->em->getRepository(Chapter::class)->find($session['id']) : null;

            if (isset($session['assistance'])) {
                $stateSessionStarted = $session['assistance'] ? $session['startAt']->format('c') : null;
                $stateSessionFinished = $session['assistance'] ? $session['finishAt']->format('c') : null;
                $timeSession = $session['assistance'] ? TimeUtils::formatTime($session['sessionTimeInMinutes'] * 60) : '00:00:00';
            }

            $sesions[] = [
                'id' => $session['id'],
                'title' => $chapter ? $chapter->getTitle() : $session['name'],
                'image' => $chapter ? $this->settings->get('app.chapter_uploads_path') . '/' . $session['image'] : null,
                'state' => $chapter ? '' : $this->getStateSessionOnSite($session['assistance']),
                'startedAt' => $chapter ? $session['startAt']->format('c') : $stateSessionStarted,
                'finishedAt' => $chapter ? $session['finishAt']->format('c') : $stateSessionFinished,
                'timeSpent' => $chapter ? $this->getTimeSpentUserChapter($userCourse, $chapter) : $timeSession,
                'isGame' => $chapter ? $chapter->getType()->isGame() : false,
                'type' => 'Sesión',
            ];
        }

        return $sesions;
    }

    private function getStructureDetailCourseAnnouncement(Announcement $announcement, User $user): array
    {
        $dataCourseAnnouncement = $this->teleformationDetailService->seasonDataAnnouncement($announcement, $user);

        $course = $announcement->getCourse();

        if (!$course->getTypeCourse()) {
            return [];
        }

        $total = 0;
        $started = 0;
        switch ($course->getTypeCourse()->getId()) {
            case TypeCourse::TYPE_PRESENCIAL:
            case TypeCourse::TYPE_AULA_VIRTUAL:
                return $this->getCourseOnSiteDetail($dataCourseAnnouncement);
            case TypeCourse::TYPE_TELEFORMACION:
                return $this->getCourseOnlineDetail($user, $course, $started, $total, $announcement);
            case TypeCourse::TYPE_MIXTO:
                return $this->getCourseMixtureDetail($dataCourseAnnouncement, $course, $user);
            default:
                return [];
        }
    }

    /**
     * @throws NonUniqueResultException
     */
    private function getCourseOnlineDetail(User $user, Course $course, &$started, &$totalChapters, Announcement $announcement): array
    {
        $userCourse = $this->getUserCourse($user, $course, $announcement);
        $usersChapters = [];

        $chapters = $course->getChapters();

        foreach ($chapters as $chapter) {
            ++$totalChapters;
            $userCourseChapter = $this->fetchUserCourseChapter($userCourse, $chapter);
            $chapterStatus = $userCourseChapter ? $userCourseChapter->getStatus() : UserCourseChapter::STATUS_NO_STARTED;
            if (UserCourseChapter::STATUS_NO_STARTED !== $chapterStatus) {
                ++$started;
            }

            $chapterType = $chapter->getType();
            $usersChapters[] = [
                'id' => $chapter->getId(),
                'name' => $chapter->getTitle(),
                'type' => empty($chapterType->getNormalized()) ? $chapterType->getName() : $chapterType->getNormalized(),
                'icon' => $chapterType->getIcon(),
                'start' => $userCourseChapter ? $userCourseChapter->getStartedAt()->format('c') : null,
                'end' => $userCourseChapter && $userCourseChapter->getFinishedAt() ? $userCourseChapter->getFinishedAt()->format('c') : null,
                'status' => $this->getStateUserChapter($userCourse, $chapter),
                'image' => $chapter->getImage() ? $chapter->getImage() : null,
                // 'timeSpent' => $this->getTimeSpentUserChapter($userCourse, $chapter),
                'attempts' => $userCourseChapter ? array_map(function ($result) {
                    $date = $result['date'] instanceof \DateTimeInterface ? $result['date'] : \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $result['date']);

                    return [
                        'questions' => $result['questions'],
                        'state' => $result['state'],
                        'start' => $date->format('c'),
                        'end' => $date->modify("+{$result['timeTotal']} seconds")->format('c'),
                        'timeTotal' => $result['timeTotal'],
                    ];
                }, $this->resultGameService->getResultGameAttempts($userCourseChapter)) : [],
            ];
        }

        return $usersChapters;
    }

    private function getCourseOnSiteDetail($dataCourseOnsite): array
    {
        if (!$dataCourseOnsite) {
            return [];
        }

        $chapters = $dataCourseOnsite[0]['chapters'];
        $sesions = [];

        foreach ($chapters as $session) {
            $startAt = $session['assistance'] && $session['startAt'] ? $session['startAt']->format('c') : null;
            $finishAt = $session['assistance'] && $session['finishAt'] ? $session['finishAt']->format('c') : null;

            $sesions[] = [
                'id' => $session['id'],
                'name' => $session['name'],
                'type' => 'SESSION',
                'icon' => TypeCourse::TYPE_NAME_ON_SITE . '.png',
                'start' => $startAt,
                'end' => $finishAt,
                'status' => $this->getStateSessionOnSite($session['assistance']),
                'image' => null,
                // 'timeSpent' => $session['assistance'] ? TimeUtils::formatTime($session['sessionTimeInMinutes'] * 60) : '00:00:00',
                // 'sessionTimeInMinutes' => $session['sessionTimeInMinutes'],
                // 'isGame' => false,
                'attempts' => [],
            ];
        }

        return $sesions;
    }

    private function getCourseMixtureDetail($dataCourseMixture, Course $course, User $user): array
    {
        if (!$dataCourseMixture) {
            return [];
        }

        $userCourse = $this->getUserCourse($user, $course);

        $chapters = $dataCourseMixture[0]['chapters'];

        $sesions = [];
        foreach ($chapters as $session) {
            // Fix: Handle both Chapter objects and session arrays
            if ($session instanceof Chapter) {
                $chapter = $session;
                $sessionData = [
                    'id' => $chapter->getId(),
                    'name' => $chapter->getTitle(),
                    'startAt' => null,
                    'finishAt' => null,
                    'assistance' => false,
                ];
            } else {
                $chapter = isset($session['title']) ? $this->em->getRepository(Chapter::class)->find($session['id']) : null;
                $sessionData = $session;
            }

            $userCourseChapter = $chapter ? $this->fetchUserCourseChapter($userCourse, $chapter) : null;
            if (isset($sessionData['assistance'])) {
                $stateSessionStarted = $sessionData['assistance'] ? $sessionData['startAt']->format('c') : null;
                $stateSessionFinished = $sessionData['assistance'] ? $sessionData['finishAt']->format('c') : null;
                $timeSession = $sessionData['assistance'] ? TimeUtils::formatTime($sessionData['sessionTimeInMinutes'] * 60) : '00:00:00';
            }

            $sesions[] = [
                'id' => $sessionData['id'],
                'name' => $chapter ? $chapter->getTitle() : $sessionData['name'],
                'type' => TypeCourse::CODE_MIXED,
                'icon' => $chapter ? $chapter->getType()->getIcon() : TypeCourse::TYPE_NAME_MIXED . '.png',
                'start' => $chapter ? ($sessionData['startAt'] ? $sessionData['startAt']->format('c') : null) : $stateSessionStarted,
                'end' => $chapter ? ($sessionData['finishAt'] ? $sessionData['finishAt']->format('c') : null) : $stateSessionFinished,
                'status' => $chapter ? $this->getStateUserChapter($userCourse, $chapter) : $this->getStateSessionOnSite($sessionData['assistance']),
                'image' => $chapter ? $chapter->getImage() : 'on-site.png',
                // 'timeSpent' => $chapter ? $this->getTimeSpentUserChapter($userCourse, $chapter) : $timeSession,
                // 'isGame' => $chapter ? $chapter->getType()->isGame() : false,
                'attempts' => $userCourseChapter ? array_map(function ($result) {
                    $date = $result['date'] instanceof \DateTimeInterface ? $result['date'] : \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $result['date']);

                    return [
                        'questions' => $result['questions'],
                        'state' => $result['state'],
                        'start' => $date->format('c'),
                        'end' => (new \DateTimeImmutable('@' . ($date->getTimestamp() + $result['timeTotal'])))->format('c'),
                        'timeTotal' => $result['timeTotal'],
                    ];
                }, $this->resultGameService->getResultGameAttempts($userCourseChapter)) : [],
            ];
        }

        return $sesions;
    }

    private function getStateSessionOnSite($assistance = false): string
    {
        if ($assistance) {
            return AnnouncementState::STATE_FINISHED;
        }

        return AnnouncementState::STATE_NOT_STARTED;
    }

    private function getStateAnnouncement(Announcement $announcement, User $user): ?string
    {
        $course = $announcement->getCourse();
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
            'user' => $user,
            'announcement' => $announcement,
        ]);

        if (!$course->getTypeCourse()) {
            return AnnouncementState::STATE_NOT_STARTED;
        }

        switch ($course->getTypeCourse()->getId()) {
            case TypeCourse::TYPE_PRESENCIAL:
            case TypeCourse::TYPE_AULA_VIRTUAL:
                return $this->getStateAnnouncementOnSite($announcementUser);
            case TypeCourse::TYPE_TELEFORMACION:
                return $this->getStateAnnouncementTeleformation($announcementUser);
            case TypeCourse::TYPE_MIXTO:
                return $this->getStateAnnouncementMixte($announcementUser);
            default:
                return AnnouncementState::STATE_NOT_STARTED;
        }
    }

    private function getStateAnnouncementOnSite(AnnouncementUser $announcementUser): string
    {
        if (null != $announcementUser->getDateApproved()) {
            return AnnouncementState::STATE_FINISHED;
        }
        if ($this->announcementUserService->getProgressTotalAssistance($announcementUser) > 0) {
            return AnnouncementState::STATE_IN_PROGRESS;
        }

        return AnnouncementState::STATE_NOT_STARTED;
    }

    private function getStateAnnouncementTeleformation(AnnouncementUser $announcementUser): string
    {
        if (null != $announcementUser->getDateApproved()) {
            return AnnouncementState::STATE_FINISHED;
        }
        $user = $announcementUser->getUser();
        $announcement = $announcementUser->getAnnouncement();

        $userCourses = $this->em->getRepository(UserCourse::class)->findBy([
            'user' => $user,
            'course' => $announcement->getCourse(),
        ]);

        $userCourse = null;
        foreach ($userCourses as $item) {
            if ($item->getAnnouncement() === $announcement) {
                $userCourse = $item;
            }
        }

        if ($userCourse && null != $userCourse->getStartedAt() && null == $userCourse->getFinishedAt()) {
            return AnnouncementState::STATE_IN_PROGRESS;
        }
        if ($userCourse && null == $userCourse->getStartedAt()) {
            return AnnouncementState::STATE_NOT_STARTED;
        }
        if ($userCourse && null != $userCourse->getFinishedAt()) {
            return AnnouncementState::STATE_FINISHED;
        }

        return AnnouncementState::STATE_NOT_STARTED;
    }

    private function getStateAnnouncementMixte(AnnouncementUser $announcementUser): string
    {
        if (null != $announcementUser->getDateApproved()) {
            return AnnouncementState::STATE_FINISHED;
        }
        $user = $announcementUser->getUser();
        $announcement = $announcementUser->getAnnouncement();

        $userCourse = $this->em->getRepository(UserCourse::class)->findOneBy([
            'user' => $user,
            'course' => $announcement->getCourse(),
        ]);

        if ($this->announcementUserService->getProgressTotalAssistance($announcementUser) > 0) {
            return AnnouncementState::STATE_IN_PROGRESS;
        }
        if (0 == $this->announcementUserService->getProgressTotalAssistance($announcementUser)) {
            return AnnouncementState::STATE_NOT_STARTED;
        }

        if ($userCourse && null != $userCourse->getStartedAt() && null == $userCourse->getFinishedAt()) {
            return AnnouncementState::STATE_IN_PROGRESS;
        }
        if ($userCourse && null == $userCourse->getStartedAt()) {
            return AnnouncementState::STATE_NOT_STARTED;
        }
        if ($userCourse && null != $userCourse->getFinishedAt()) {
            return AnnouncementState::STATE_FINISHED;
        }

        return AnnouncementState::STATE_NOT_STARTED;
    }
}
