<?php

declare(strict_types=1);

namespace App\Service\Notification;

use App\Admin\Traits\EmailNotificationTrait;
use App\Entity\EmailNotification;
use App\Entity\Itinerary;
use App\Entity\User;
use App\Enum\EmailNotificacion;
use App\Service\Geolocation\GeolocationService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

class EmailNotificationService implements EmailNotificacion
{
    use EmailNotificationTrait;
    private $em;
    private GeolocationService $geolocationService;
    protected TranslatorInterface $translator;
    protected SettingsService $settings;

    public function __construct(EntityManagerInterface $em, GeolocationService $geolocationService, TranslatorInterface $translator, SettingsService $settings)
    {
        $this->em = $em;
        $this->geolocationService = $geolocationService;
        $this->translator = $translator;
        $this->settings = $settings;
    }

    public function insertNotification($title, $message, $type, $userId, $attributes = null, $extra = null): array
    {
        try {
            $emailNotification = new EmailNotification();

            if (!empty($title)) {
                $emailNotification->setTitle($title);
            }
            if (!empty($message)) {
                $emailNotification->setMessage($message);
            }

            $user = $this->em->getRepository(User::class)->find($userId);

            $emailNotification->setUser($user);
            $emailNotification->setSent(false);
            $emailNotification->setType($type);
            $emailNotification->setAttributes($attributes);
            $emailNotification->setExtra($extra);

            $this->em->persist($emailNotification);
            $this->em->flush();

            return [
                'status' => Response::HTTP_OK
            ];
        } catch (\Exception $e) {
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => $e->getMessage()
            ];
        }
    }

    public function insertNotificationWithTranslation(array $parameters): array
    {
        try {
            $user = $this->em->getRepository(User::class)->find($parameters['userId']);

            if (!$user) {
                return [
                    'status' => Response::HTTP_NOT_FOUND,
                    'message' => 'User not found'
                ];
            }

            $emailNotification = new EmailNotification();
            $emailNotification->setUser($user);
            $emailNotification->setSent(false);
            $emailNotification->setType($parameters['type']);
            $emailNotification->setTranslationText($parameters['translationText']);
            $emailNotification->setTranslationTitle($parameters['translationTitle']);

            if (isset($parameters['attributes'])) {
                $emailNotification->setAttributes($parameters['attributes']);
            }

            if (isset($parameters['itineraryId'])) {
                $itinerary = $this->em->getRepository(Itinerary::class)->find($parameters['itineraryId']);
                $emailNotification->setItinerary($itinerary);
            }

            if (isset($parameters['extra'])) {
                $emailNotification->setExtra($parameters['extra']);
            }

            $this->em->persist($emailNotification);
            $this->em->flush();

            return [
                'status' => Response::HTTP_OK
            ];
        } catch (\Exception $e) {
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => $e->getMessage()
            ];
        }
    }

    public function listUserEmailNotifications(User $user, Request $request): array
    {
        if ($request->get('lang')) {
            $lang = $request->get('lang');
        } else {
            $lang = $user->getLocaleCampus();
        }

        $emailNotificationRepository = $this->em->getRepository(EmailNotification::class);
        $notifications = $emailNotificationRepository->listAllPending($user->getId(), $request);

        foreach ($notifications as &$valor) {
            if (\in_array($valor['type'], self::EMAIL_NOTIFICATIONS)) {
                $attributes = \is_null($valor['attributes']) ? [] : $valor['attributes'];

                if (empty($valor['message']) && empty($attributes)) {
                    $valor['title'] = $this->translator->trans($valor['translationTitle'], [], 'emailNotification', $lang);
                    $valor['message'] = $this->translator->trans($valor['translationText'], [], 'emailNotification', $lang);
                } elseif (empty($valor['message']) && !empty($attributes)) {
                    $attributesTrans = [];
                    if (count($valor['attributes']) > 0) {
                        $attributesTrans = current($valor['attributes']);
                    }

                    $valor['title'] = $this->translator->trans($valor['translationTitle'], $attributesTrans, 'emailNotification', $lang);
                    $valor['message'] = $this->translator->trans($valor['translationText'], $attributesTrans, 'emailNotification', $lang);
                }
                unset($valor['attributes']);
            }
        }

        $message = [];
        $timezoneUser = $this->geolocationService->getTimeZoneConnection();
        foreach ($notifications as $item) {
            // $createdAt = new DateTime($item['createdAt']);
            $createdAt = $this->geolocationService->convertTimeZone($item['createdAt'], $this->settings->get('app.default_timezone'), $timezoneUser);
            $newDate = new \DateTime($createdAt);

            // Formatear la fecha
            $locale = 'en'; // Puedes obtener el idioma del usuario desde tu aplicación
            $formatter = new \IntlDateFormatter($locale, \IntlDateFormatter::SHORT, \IntlDateFormatter::SHORT);

            // Formatear la fecha
            $formattedDate = $formatter->format($newDate);

            $createdAtNewTz = $this->geolocationService->fromTimezoneToTimezone($item['createdAt'], $this->settings->get('app.default_timezone'), $timezoneUser);
            $message[] = [
                'id' => $item['id'],
                'type' => $item['type'],
                'sent' => $item['sent'],
                'createdAt' => $item['createdAt'],
                'title' => $item['title'],
                'message' => $item['message'],
                'date' => $formattedDate,

                'createdAtNotFormatted' => $item['createdAt']->format('c'),
                'dateNotFormatted' => $createdAtNewTz->format('c')
            ];
        }

        return $message;
    }
}
