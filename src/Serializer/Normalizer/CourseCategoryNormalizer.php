<?php

namespace App\Serializer\Normalizer;

use App\Entity\CourseCategory;
use Psr\Container\ContainerInterface;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Contracts\Translation\TranslatorInterface;

class CourseCategoryNormalizer implements NormalizerInterface
{
    private ObjectNormalizer $normalizer;
    private TranslatorInterface $translator;


    public function __construct (ObjectNormalizer $normalizer, TranslatorInterface $translator)
    {
        $this->normalizer = $normalizer;
        $this->translator = $translator;
    }


    /**
     * @param mixed $courseCategory
     * @param string|null $format
     * @param array $context
     * @return array
     * @throws ExceptionInterface
     */
    public function normalize ($courseCategory, string $format = null, array $context = []): array
    {
        /**
         * @var CourseCategory $courseCategory
         */
        $data = $this->normalizer->normalize($courseCategory, $format, $context);

        $user = (isset($context['user']) and $context['user'] instanceof \App\Entity\User) ? $context['user'] : null;

        if($user)
        {
            $data['name'] = ($courseCategory->getTranslations()->get($user->getLocale())) ? $courseCategory->getTranslations()->get($user->getLocale())->getName() : $courseCategory->getName();
        }

        return $data;
    }



    public function supportsNormalization ($data, string $format = null): bool
    {
        return $data instanceof \App\Entity\CourseCategory;
    }


}
