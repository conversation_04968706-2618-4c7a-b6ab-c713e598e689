<?php

namespace App\Serializer\Normalizer;

use App\Entity\CourseSection;
use App\Entity\User;
use S<PERSON>fony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class CourseSectionNormalizer implements NormalizerInterface
{
    private ObjectNormalizer $normalizer;
    private CourseCategoryNormalizer $courseCategoryNormalizer;


    public function __construct (ObjectNormalizer $normalizer, CourseCategoryNormalizer $courseCategoryNormalizer)
    {
        $this->normalizer               = $normalizer;
        $this->courseCategoryNormalizer = $courseCategoryNormalizer;
    }


    /**
     * @param $courseSection
     * @param string|null $format
     * @param array $context
     * @return array
     * @throws ExceptionInterface
     */
    public function normalize ($courseSection, string $format = null, array $context = []): array
    {
        /**
         * @var CourseSection $courseSection
         */
        $data = $this->normalizer->normalize($courseSection, $format, $context);


        $user = (isset($context['user']) and $context['user'] instanceof User) ? $context['user'] : null;

        if ($user)
        {
            $data['name'] = ($courseSection->getTranslations()->get($user->getLocale())) ? $courseSection->getTranslations()->get($user->getLocale())->getName() : $courseSection->getName();
            $data['description'] = ($courseSection->getTranslations()->get($user->getLocale()) && $courseSection->getTranslations()->get($user->getLocale())->getDescription()) ? $courseSection->getTranslations()->get($user->getLocale())->getDescription() : $courseSection->getDescription();
        }

        $data['categories'] = [];
        foreach ($courseSection->getCategories() as $category)
        {
            $data['categories'][] = $this->courseCategoryNormalizer->normalize($category, $format, $context);
        }


        return $data;
    }


    public function supportsNormalization ($data, string $format = null): bool
    {
        return $data instanceof CourseSection;
    }


}
