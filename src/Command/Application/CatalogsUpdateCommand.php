<?php

declare(strict_types=1);

namespace App\Command\Application;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Process\Process;

class CatalogsUpdateCommand extends Command
{
    protected static $defaultName = 'catalogs:update';
    protected static $defaultDescription = 'Actualiza varios catálogos en la base de datos utilizando datos de prueba predefinidos';

    public function __construct()
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument(
                name: 'arg1',
                mode: InputArgument::OPTIONAL,
                description: 'Argument description'
            )
            ->addOption(
                name: 'new',
                shortcut: null,
                mode: InputOption::VALUE_NONE,
                description: 'Busca aquellas configuraciones necesarias para un nueve cliente pueda usar el producto'
            )
            ->addOption(
                name: 'list',
                shortcut: null,
                mode: InputOption::VALUE_NONE,
                description: 'Muestra los catálogos que se pueden actualizar'
            )
            ->addOption(
                name: 'skip-previews',
                shortcut: null,
                mode: InputOption::VALUE_NONE,
                description: 'Omite la generación automática de previews de diplomas'
            )
            ->addOption(
                name: 'php-binary',
                shortcut: null,
                mode: InputOption::VALUE_REQUIRED,
                description: 'Especifica el binario de PHP a usar (por defecto: php)',
                default: 'php'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $arg1 = $input->getArgument('arg1');
        $phpBinary = $input->getOption('php-binary');

        if ($input->getOption('new')) {
            if (!$io->confirm('¿Estás seguro de que esta configuración es para un nuevo cliente?')) {
                $io->warning('Operación cancelada.');

                return Command::SUCCESS;
            }
            $io->note('Creando configuración del cliente...');
        } else {
            if (!$io->confirm('¿Estás seguro que quieres actualizar los catálogos?')) {
                $io->warning('Operación cancelada.');

                return Command::SUCCESS;
            }

            if ('migrate' == $arg1) {
                // ejecutar migraciones de symfony
                $process = $this->createProcess([
                    $phpBinary,
                    'bin/console',
                    'doctrine:migrations:migrate',
                ]);
            }
            $io->note('Actualizando catálogos...');
        }

        $fixtures = [
            'TypeIdentificationFixtures',
            'CatalogFixtures',
            'TypeCourseFixtures',
            'AlertTypeTutorFixtures',
            'AnnouncementCriteriaFixtures',
            'ConfigurationClientAnnouncementFixtures',
            'ChapterTypeFixtures',
            'AnnouncementConfigurationTypeFixtures',
            'TypeDiplomaFixtures',
            'CronJobFixtures',
            'ClassroomvirtualTypeFixtures',
            'TypeMoneyFixtures',
            'ExtraDataFixtures',
            'AnnouncementStepCreationFixtures',
            'TypeCourseAnnouncementStepCreationFixtures',
            'SectionDefaultFixtures',
            'SettingFixtures',
            'TranslationsAdminFixtures',
            'UserFixtures',
            'NpsQuestionFixtures',
        ];

        if ($input->getOption('list')) {
            $io->title('<info>Catálogos disponibles para actualizar</info>');
            $io->listing(array_map(function ($fixture) {
                return "<fg=cyan>$fixture</>";
            }, $fixtures));

            return Command::SUCCESS;
        }

        if ($input->getOption('new')) {
            $fixtures = array_merge([
                'TypeVideoFixtures',
                'CourseCategoryFixtures',
                'CatalogFixtures',
            ], $fixtures);
        }

        foreach ($fixtures as $fixture) {
            $process = $this->createProcess([
                $phpBinary,
                '-d memory_limit=512M',
                'bin/console',
                'doctrine:fixtures:load',
                '--group',
                $fixture,
                '--append',
            ]);

            $process->run();

            if ($process->isSuccessful()) {
                $output->writeln(\sprintf('[<info>✓</info>] Se actualizó el catálogo: %s', "[<info>$fixture</info>]"));
            } else {
                $output->writeln(\sprintf('[<fg=red>✗</>] No se pudo actualizar el catálogo: %s', $fixture));
                $io->error('No se pudo actualizar el catálogo: ' . $fixture);
                $io->error($process->getErrorOutput());

                return Command::FAILURE;
            }
        }

        $io->success('Los catálogos han sido actualizados correctamente.');

        if (!$input->getOption('skip-previews')) {
            $this->generateDiplomaPreviews($io, $phpBinary);
        }

        return Command::SUCCESS;
    }

    private function generateDiplomaPreviews(SymfonyStyle $io, string $phpBinary): void
    {
        $io->note('Generando previews de diplomas...');

        $process = $this->createProcess([
            $phpBinary,
            'bin/console',
            'app:generate-preview-diploma',
            '--all',
        ]);

        $process->run();

        if ($process->isSuccessful()) {
            $io->success('Previews de diplomas generadas correctamente.');
        } else {
            $io->error('Error al generar previews de diplomas');
            $io->error($process->getErrorOutput());
        }
    }

    /**
     * Creates a new Process instance. This method can be overridden in tests for mocking.
     */
    protected function createProcess(array $command): Process
    {
        return new Process($command);
    }
}
