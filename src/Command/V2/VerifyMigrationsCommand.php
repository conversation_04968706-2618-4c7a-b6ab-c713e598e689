<?php

declare(strict_types=1);

namespace App\Command\V2;

use App\V2\Infrastructure\Persistence\MigrationVerificationService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class VerifyMigrationsCommand extends Command
{
    public function __construct(
        private readonly MigrationVerificationService $migrationVerificationService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('database:verify-migrations')
            ->setDescription('Verify database structure against migrations')
            ->addOption(
                'detailed',
                'd',
                InputOption::VALUE_NONE,
                'Show detailed information about differences'
            )
            ->addOption(
                'table',
                't',
                InputOption::VALUE_OPTIONAL,
                'Verify only the specified table'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Verification of Migrations vs. Database');

        try {
            $detailed = $input->getOption('detailed');
            $specificTable = $input->getOption('table');

            $io->section('Reading migrations...');
            $expectedStructure = $this->migrationVerificationService->getExpectedDatabaseStructure();
            $io->success(\sprintf('Processed %d migrations', \count($expectedStructure['migrations'])));

            $io->section('Getting current database structure...');
            $currentStructure = $this->migrationVerificationService->getCurrentDatabaseStructure();
            $io->success(\sprintf('Found %d tables in the database', \count($currentStructure['tables'])));

            $io->section('Comparing structures...');
            $differences = $this->migrationVerificationService->compareStructures(
                $expectedStructure,
                $currentStructure,
                $specificTable
            );

            if (empty($differences)) {
                $io->success('✅ The database structure matches the migrations perfectly.');

                return Command::SUCCESS;
            }

            $this->displayDifferences($io, $differences, $detailed);

            return Command::FAILURE;
        } catch (\Exception $e) {
            $io->error('Error on verification: ' . $e->getMessage());

            if ($output->isVerbose()) {
                $io->text('Stack trace:');
                $io->text($e->getTraceAsString());
            }

            return Command::FAILURE;
        }
    }

    private function displayDifferences(SymfonyStyle $io, array $differences, bool $detailed): void
    {
        $io->error(\sprintf('❌ Found %d differences:', \count($differences)));

        $groupedDifferences = $this->groupDifferencesByTable($differences);

        foreach ($groupedDifferences as $tableName => $tableDifferences) {
            $io->section("Table: {$tableName}");

            // Separar por tipos de diferencias
            $missingColumns = [];
            $extraColumns = [];
            $differentColumns = [];
            $missingForeignKeys = [];
            $extraForeignKeys = [];
            $missingIndexes = [];
            $extraIndexes = [];
            $tableIssues = [];

            foreach ($tableDifferences as $diff) {
                switch ($diff['type']) {
                    case 'missing_table':
                        $tableIssues[] = ['type' => 'missing', 'data' => $diff];
                        break;
                    case 'extra_table':
                        $tableIssues[] = ['type' => 'extra', 'data' => $diff];
                        break;
                    case 'missing_column':
                        $missingColumns[] = $diff;
                        break;
                    case 'extra_column':
                        $extraColumns[] = $diff;
                        break;
                    case 'column_mismatch':
                        $differentColumns[] = $diff;
                        break;
                    case 'missing_foreign_key':
                        $missingForeignKeys[] = $diff;
                        break;
                    case 'extra_foreign_key':
                        $extraForeignKeys[] = $diff;
                        break;
                    case 'missing_index':
                        $missingIndexes[] = $diff;
                        break;
                    case 'extra_index':
                        $extraIndexes[] = $diff;
                        break;
                }
            }

            foreach ($tableIssues as $issue) {
                if ('missing' === $issue['type']) {
                    $io->text('🔴 Missing table in database');
                    if ($detailed && isset($issue['data']['expected_structure'])) {
                        $io->text('Expected structure:');
                        $formatted = $this->formatTableStructure($issue['data']['expected_structure']);
                        if (!empty($formatted)) {
                            $io->listing($formatted);
                        }
                    }
                } else {
                    $io->text('🟡 Extra table in database');
                }
            }

            if (!empty($missingColumns)) {
                $io->text('🔴 Missing columns:');
                $columnList = [];
                foreach ($missingColumns as $diff) {
                    $columnInfo = $diff['column'];
                    if ($detailed && isset($diff['expected_definition'])) {
                        $columnInfo .= ' (' . $diff['expected_definition'] . ')';
                    }
                    $columnList[] = $columnInfo;
                }
                $io->listing($columnList);
            }

            if (!empty($extraColumns)) {
                $io->text('🟡 Extra columns:');
                $columnList = [];
                foreach ($extraColumns as $diff) {
                    $columnList[] = $diff['column'];
                }
                $io->listing($columnList);
            }

            if (!empty($differentColumns)) {
                $io->text('🟠 Differences in columns:');
                if ($detailed) {
                    foreach ($differentColumns as $diff) {
                        $io->text('  • ' . $diff['column']);
                        $io->text('    Expected: ' . $diff['expected']);
                        $io->text('    Actual: ' . $diff['actual']);
                    }
                } else {
                    $columnList = [];
                    foreach ($differentColumns as $diff) {
                        $columnList[] = $diff['column'];
                    }
                    $io->listing($columnList);
                }
            }

            if (!empty($missingForeignKeys)) {
                $io->text('🔴 Foreign keys faltantes:');
                $fkList = [];
                foreach ($missingForeignKeys as $diff) {
                    $fkInfo = $diff['constraint'];
                    if ($detailed && isset($diff['expected_definition'])) {
                        $fkInfo .= ' (' . $diff['expected_definition'] . ')';
                    }
                    $fkList[] = $fkInfo;
                }
                $io->listing($fkList);
            }

            if (!empty($extraForeignKeys)) {
                $io->text('🟡 Foreign keys extra:');
                $fkList = [];
                foreach ($extraForeignKeys as $diff) {
                    $fkList[] = $diff['constraint'];
                }
                $io->listing($fkList);
            }

            if (!empty($missingIndexes)) {
                $io->text('🔴 Missing indexes:');
                $indexList = [];
                foreach ($missingIndexes as $diff) {
                    $indexInfo = $diff['index'];
                    if ($detailed && isset($diff['expected_definition'])) {
                        $indexInfo .= ' (' . $diff['expected_definition'] . ')';
                    }
                    $indexList[] = $indexInfo;
                }
                $io->listing($indexList);
            }

            if (!empty($extraIndexes)) {
                $io->text('🟡 Extra indexes:');
                $indexList = [];
                foreach ($extraIndexes as $diff) {
                    $indexList[] = $diff['index'];
                }
                $io->listing($indexList);
            }

            $io->newLine();
        }

        // Resumen
        $summary = $this->generateSummary($differences);
        $io->section('Summary');
        foreach ($summary as $type => $count) {
            $io->text(\sprintf('%s: %d', $type, $count));
        }
    }

    private function formatTableStructure(array $structure): array
    {
        $formatted = [];

        if (isset($structure['columns']) && \is_array($structure['columns'])) {
            $formatted[] = 'Columns:';
            foreach ($structure['columns'] as $column => $definition) {
                $formatted[] = "  - {$column}: {$definition}";
            }
        }

        if (isset($structure['foreign_keys']) && \is_array($structure['foreign_keys'])) {
            $formatted[] = 'Foreign Keys:';
            foreach ($structure['foreign_keys'] as $fk) {
                if (\is_array($fk)) {
                    $fkString = \sprintf(
                        'FOREIGN KEY (%s) REFERENCES %s (%s)',
                        $fk['local_column'] ?? 'unknown',
                        $fk['foreign_table'] ?? 'unknown',
                        $fk['foreign_column'] ?? 'unknown'
                    );
                    $formatted[] = "  - {$fkString}";
                } else {
                    $formatted[] = "  - {$fk}";
                }
            }
        }

        if (isset($structure['indexes']) && \is_array($structure['indexes'])) {
            $formatted[] = 'Indexes:';
            foreach ($structure['indexes'] as $index) {
                if (\is_array($index)) {
                    $indexString = \sprintf(
                        '%s (%s)',
                        $index['unique'] ? 'UNIQUE INDEX' : 'INDEX',
                        implode(', ', $index['columns'] ?? [])
                    );
                    $formatted[] = "  - {$indexString}";
                } else {
                    $formatted[] = "  - {$index}";
                }
            }
        }

        return $formatted;
    }

    private function generateSummary(array $differences): array
    {
        $summary = [
            'Missing tables' => 0,
            'Extra tables' => 0,
            'Missing columns' => 0,
            'Extra columns' => 0,
            'Column mismatches' => 0,
            'Missing foreign keys' => 0,
            'Extra foreign keys' => 0,
            'Missing indexes' => 0,
            'Extra indexes' => 0,
        ];

        foreach ($differences as $difference) {
            switch ($difference['type']) {
                case 'missing_table':
                    $summary['Missing tables']++;
                    break;
                case 'extra_table':
                    $summary['Extra tables']++;
                    break;
                case 'missing_column':
                    $summary['Missing columns']++;
                    break;
                case 'extra_column':
                    $summary['Extra columns']++;
                    break;
                case 'column_mismatch':
                    $summary['Column mismatches']++;
                    break;
                case 'missing_foreign_key':
                    $summary['Missing foreign keys']++;
                    break;
                case 'extra_foreign_key':
                    $summary['Extra foreign keys']++;
                    break;
                case 'missing_index':
                    $summary['Missing indexes']++;
                    break;
                case 'extra_index':
                    $summary['Extra indexes']++;
                    break;
            }
        }

        return array_filter($summary);
    }

    private function groupDifferencesByTable(array $differences): array
    {
        $grouped = [];

        foreach ($differences as $difference) {
            $tableName = $difference['table'];
            if (!isset($grouped[$tableName])) {
                $grouped[$tableName] = [];
            }
            $grouped[$tableName][] = $difference;
        }

        ksort($grouped);

        return $grouped;
    }
}
