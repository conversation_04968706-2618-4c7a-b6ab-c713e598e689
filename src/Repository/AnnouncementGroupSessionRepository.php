<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Announcement;
use App\Entity\AnnouncementConfiguration;
use App\Entity\AnnouncementConfigurationType;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementUser;
use App\Entity\GenericToken;
use App\Entity\User;
use App\Service\General\IpService;
use App\Service\SettingsService;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @extends ServiceEntityRepository<AnnouncementGroupSession>
 *
 * @method AnnouncementGroupSession|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementGroupSession|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementGroupSession[]    findAll()
 * @method AnnouncementGroupSession[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementGroupSessionRepository extends ServiceEntityRepository
{
    private TranslatorInterface $translator;
    private RequestStack $requestStack;
    private SettingsService $settings;
    private IpService $ipService;

    public function __construct(ManagerRegistry $registry, TranslatorInterface $translator, RequestStack $requestStack, SettingsService $settings, IpService $ipService)
    {
        parent::__construct($registry, AnnouncementGroupSession::class);
        $this->translator = $translator;
        $this->requestStack = $requestStack;
        $this->settings = $settings;
        $this->ipService = $ipService;
    }

    public function add(AnnouncementGroupSession $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementGroupSession $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function handleToken(GenericToken $token, User $user): array
    {
        $userTimezone = empty($user->getTimezone())
            ? new \DateTimeZone(date_default_timezone_get())
            : new \DateTimeZone($user->getTimezone());

        $session = $this->findOneBy(['id' => $token->getEntityId()]);
        if (!$session) {
            return [
                'error' => true,
                'data' => 'Not found',
            ];
        }

        $announcement = $session->getAnnouncementGroup()->getAnnouncement();
        $course = $session->getAnnouncementGroup()->getAnnouncement()->getCourse();
        if (Announcement::STATUS_ACTIVE !== $announcement->getStatus()) {
            return [
                'error' => false,
                'data' => [
                    'type' => 'redirect',
                    'value' => '/campus/course/' . $course->getId() . '?announcementId=' . $announcement->getId() . '&state=' . $this->getMessageQr($user, $token) . '&validate=false',
                    'message' => 'COURSE.ANNOUNCEMENT.NOT_ACTIVE',
                ],
            ];
        }

        $userInGroup = $this->createQueryBuilder('s')
            ->select('s.id as sessionId', 'u.id as userId')
            ->join('s.announcementGroup', 'g')
            ->join('g.announcementUsers', 'au')
            ->join('au.user', 'u')
            ->where('s = :session')
            ->andWhere('u.id = :userId')
            ->setParameter('session', $session)
            ->setParameter('userId', $user->getId())
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        $registerOnQr = false;

        if (!$userInGroup || (int) $userInGroup['userId'] !== $user->getId()) {
            if (!$this->settings->get('app.fundae.register_on_qr')) {
                // Exit procedure
                return [
                    'error' => true,
                    'data' => $this->translator->trans('generic_token.assistance.user_not_in_group', [], 'messages', $user->getLocale()),
                ];
            }
            $registerOnQr = true;
        }

        $message = null;
        $course = $session->getAnnouncementGroup()->getAnnouncement()->getCourse();

        if ($token->isDateValid()) {
            if ($registerOnQr) {
                $group = $session->getAnnouncementGroup();
                $announcement = $group->getAnnouncement();
                $bonificable = $this->_em->getRepository(AnnouncementConfiguration::class)->findOneBy([
                    'announcement' => $announcement,
                    'configuration' => $this->_em->getRepository(AnnouncementConfigurationType::class)
                        ->find(AnnouncementConfigurationType::ID_ENABLE_SUBSIDIZED_COURSE),
                ]);
                if ($bonificable) { // Exit procedure
                    return [
                        'error' => true,
                        'data' => $this->translator->trans('generic_token.assistance.user_not_in_group', [], 'messages', $user->getLocale()),
                    ];
                }

                // Register user
                $announcementUser = new AnnouncementUser();
                $announcementUser->setUser($user)
                    ->setAnnouncementGroup($group)
                    ->setAnnouncement($announcement)
                    ->setExternal(true);
                $this->_em->persist($announcementUser);
                $this->_em->flush();
            }

            $assistance = $session->getStudentAssistance() ?? [];

            $totalElements = \count($assistance);
            $found = false;
            $foundIndex = -1;
            $entryExitInfo = [];
            for ($i = 0; $i < $totalElements; ++$i) {
                if ($assistance[$i]['id'] === $user->getId()) {
                    $assistance[$i]['assistance'] = true;
                    $found = true;
                    $foundIndex = $i;
                    $entryExitInfo = $assistance[$i]['info'] ?? [];
                    break;
                }
            }

            $ip = $this->ipService->getClientIp();
            $currentDateTime = new \DateTime('now', $userTimezone); // Apply timezone

            if (GenericToken::TYPE_ANNOUNCEMENT_SESSION_ENTRY_QR === $token->getType()) {
                $entryExitInfo[] = [
                    'entry' => $currentDateTime->format('c'), // Saved with timezone
                    'ip' => $ip,
                    'inTime' => null,
                ];
            } elseif (GenericToken::TYPE_ANNOUNCEMENT_SESSION_EXIT_QR === $token->getType()) {
                $entryExitInfo[] = [
                    'exit' => $currentDateTime->format('c'), // Saved with timezone
                    'ip' => $ip,
                    'inTime' => null,
                ];
            }

            $entryExitInfo = array_map(function ($element) {
                $element['inTime'] = null;

                return $element;
            }, $entryExitInfo);

            /**
             * Validate entryExitInfo.
             */
            $tInfo = \count($entryExitInfo);

            /*
             * Find the first 'entry' element to check if is in time or not
             */
            for ($i = 0; $i < $tInfo; ++$i) {
                if (\array_key_exists('entry', $entryExitInfo[$i])) {
                    $datetime = new \DateTimeImmutable($entryExitInfo[$i]['entry']); // Entry saved format 2010-01-28T15:00:00+02:00
                    $entryExitInfo[$i]['inTime'] = $session->isEntryUserInTime($datetime);
                    break;
                }
            }

            /*
             * Find the last 'exit' element
             */
            for ($i = ($tInfo - 1); $i >= 0; --$i) {
                if (\array_key_exists('exit', $entryExitInfo[$i])) {
                    $datetime = new \DateTimeImmutable($entryExitInfo[$i]['exit']); // Exit saved format 2010-01-28T15:00:00+02:00
                    $entryExitInfo[$i]['inTime'] = $session->isExitUserInTime($datetime);
                    break;
                }
            }

            if (!$found) {
                $assistance[] = [
                    'id' => $user->getId(),
                    'assistance' => true,
                    'info' => $entryExitInfo,
                ];
            } else {
                $assistance[$foundIndex]['info'] = $entryExitInfo;
            }

            $session->setStudentAssistance($assistance);

            $this->_em->persist($session);
            $this->_em->flush();

            $message = $this->translator->trans('generic_token.assistance.success', [], 'messages', $user->getLocale());
        }

        $tokeValid = $token->isDateValid() ? 'true' : 'false';

        return [
            'error' => false,
            'data' => [
                'type' => 'redirect',
                'value' => '/campus/course/' . $course->getId() . '?announcementId=' . $announcement->getId() . '&state=' . $this->getMessageQr($user, $token) . '&validate=' . $tokeValid,
                'message' => $message,
            ],
        ];
    }

    private function getMessageQr(User $user, GenericToken $token): string
    {
        if (!$token) {
            return 'SESSION_NOT_FOUND';
        }

        $typeCode = GenericToken::TYPE_ANNOUNCEMENT_SESSION_ENTRY_QR === $token->getType() ? 'QR_ENTRY' : 'QR_EXIT';

        $session = $this->findOneBy(['id' => $token->getEntityId()]);
        if (!$session) {
            return 'SESSION_NOT_FOUND';
        }

        $userTimezone = new \DateTimeZone($user->getTimezone() ?: date_default_timezone_get());
        $currentDateTime = new \DateTime('now', $userTimezone);

        $announcement = $session->getAnnouncementGroup()->getAnnouncement();

        if (Announcement::STATUS_CONFIGURATION === $announcement->getStatus()) {
            return $typeCode . '.ANNOUNCEMENT_NOT_ACTIVE';
        }

        $announcementUser = $this->_em->getRepository(AnnouncementUser::class)->findOneBy([
            'user' => $user,
            'announcement' => $announcement,
        ]);

        if (!$announcementUser) {
            return $typeCode . '.ANNOUNCEMENT_FINISHED';
        }

        $finishSession = $session->getFinishAt();
        $finishSession->setTimezone($userTimezone);

        if ($currentDateTime < $session->getStartAt()) {
            return $typeCode . '.SESSION_NOT_ACTIVE';
        }

        if ($currentDateTime > $finishSession) {
            return $typeCode . '.SESSION_FINISHED';
        }

        return $typeCode . '.SESSION_REGISTERED';
    }

    /**
     * @return AnnouncementGroupSession[]
     */
    public function getSessions(AnnouncementGroup $group): array
    {
        return $this->createQueryBuilder('s')
            ->select('s')
            ->where('s.announcementGroup =:group')
            ->setParameter('group', $group)
            ->orderBy('s.session_number', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findSessionFinishAndRangeDate($dateInitial, $dateFinish)
    {
        return $this->createQueryBuilder('s')

            ->andWhere('s.finishAt >= :dateInial')
            ->andWhere('s.finishAt <= :dateFinish')
            ->setParameter('dateInial', $dateInitial)
            ->setParameter('dateFinish', $dateFinish)
            ->getQuery()->getResult();
    }

    public function getTotalNumberSessionGroup(Announcement $announcement, bool $isUserView = false)
    {
        $result = $this->createQueryBuilder('ags')
            ->select('COUNT(ags.id) as total')
            ->join('ags.announcementGroup', 'ag')
            ->join('ag.announcement', 'a')
            ->where('a =:announcement')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getSingleScalarResult();
        return $isUserView ? \intval($result) : $result;
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function getTotalStartedSessionGroup(Announcement $announcement): int
    {
        $result = $this->createQueryBuilder('ags')
            ->select('COUNT(ags.id) as total')
            ->join('ags.announcementGroup', 'ag')
            ->join('ag.announcement', 'a')
            ->where('a =:announcement')
            ->andWhere('ags.startAt IS NOT NULL')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getSingleScalarResult();

        return \intval($result);
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function getTotalFinishedSessionGroup(Announcement $announcement): int
    {
        $result = $this->createQueryBuilder('ags')
            ->select('COUNT(ags.id) as total')
            ->join('ags.announcementGroup', 'ag')
            ->join('ag.announcement', 'a')
            ->where('a =:announcement')
            ->andWhere('ags.finishAt IS NOT NULL')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getSingleScalarResult();

        return \intval($result);
    }
}
