<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\CourseSectionTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method CourseSectionTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method CourseSectionTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method CourseSectionTranslation[]    findAll()
 * @method CourseSectionTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CourseSectionTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CourseSectionTranslation::class);
    }
}
