<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementInspectorAccess;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementInspectorAccess>
 *
 * @method AnnouncementInspectorAccess|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementInspectorAccess|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementInspectorAccess[]    findAll()
 * @method AnnouncementInspectorAccess[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementInspectorAccessRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementInspectorAccess::class);
    }

    public function add(AnnouncementInspectorAccess $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementInspectorAccess $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
