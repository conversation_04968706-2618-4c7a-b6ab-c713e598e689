<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Announcement;
use App\Entity\AnnouncementAprovedCriteria;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementAprovedCriteria>
 *
 * @method AnnouncementAprovedCriteria|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementAprovedCriteria|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementAprovedCriteria[]    findAll()
 * @method AnnouncementAprovedCriteria[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementAprovedCriteriaRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementAprovedCriteria::class);
    }

    public function add(AnnouncementAprovedCriteria $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementAprovedCriteria $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getAnnouncementAprovedCriteria(Announcement $announcement){
        return $this->createQueryBuilder('a')
            ->select('a.id', 'a.value', 'a.extra')
            ->addSelect('c.id as criteriaId')
            ->join('a.announcementCriteria', 'c')
            ->where('a.announcement =:announcement')->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();
    }
}
