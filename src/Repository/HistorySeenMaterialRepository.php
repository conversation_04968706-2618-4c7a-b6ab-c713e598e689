<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\HistorySeenMaterial;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<HistorySeenMaterial>
 *
 * @method HistorySeenMaterial|null find($id, $lockMode = null, $lockVersion = null)
 * @method HistorySeenMaterial|null findOneBy(array $criteria, array $orderBy = null)
 * @method HistorySeenMaterial[]    findAll()
 * @method HistorySeenMaterial[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class HistorySeenMaterialRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, HistorySeenMaterial::class);
    }

    public function add(HistorySeenMaterial $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(HistorySeenMaterial $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
