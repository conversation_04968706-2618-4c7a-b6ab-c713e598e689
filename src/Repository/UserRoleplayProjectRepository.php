<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\RoleplayProject;
use App\Entity\User;
use App\Entity\UserRoleplayProject;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<UserRoleplayProject>
 *
 * @method UserRoleplayProject|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserRoleplayProject|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserRoleplayProject[]    findAll()
 * @method UserRoleplayProject[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserRoleplayProjectRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserRoleplayProject::class);
    }

    public function add(UserRoleplayProject $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(UserRoleplayProject $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getByUserAndProject(User $user, RoleplayProject $project)
    {
        return $this->createQueryBuilder('urp')
            ->andWhere('urp.user = :user')
            ->andWhere('urp.project = :project')
            ->setParameters([
                'user' => $user,
                'project' => $project,
            ])
            ->getQuery()
            ->getOneOrNullResult();
    }
}
