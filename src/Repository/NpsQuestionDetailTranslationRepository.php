<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\NpsQuestionDetailTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<NpsQuestionDetailTranslation>
 *
 * @method NpsQuestionDetailTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method NpsQuestionDetailTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method NpsQuestionDetailTranslation[]    findAll()
 * @method NpsQuestionDetailTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class NpsQuestionDetailTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, NpsQuestionDetailTranslation::class);
    }

    public function add(NpsQuestionDetailTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(NpsQuestionDetailTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
