<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\NpsQuestionTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method NpsQuestionTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method NpsQuestionTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method NpsQuestionTranslation[]    findAll()
 * @method NpsQuestionTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class NpsQuestionTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, NpsQuestionTranslation::class);
    }

    /**
     * @return int Returns all total answers by challenge_questionid
     */
    public function getQuestionCourse($course)
    {
        $sql = 'SELECT nq.id, nq.type, nq.position, nq.main, nq.question
            FROM nps_question_course  as nqc    
            JOIN nps_question as nq 
            ON nqc.nps_question_id = nq.id 
            WHERE nqc.course_id = :course
            AND nq.deleted_at IS NULL ';

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery(['course' => $course])->fetchAllAssociative();
        return $result;
    }

    /**
     * @return int Returns all total answers by challenge_questionid
     */
    public function getQuestionAnnouncement($announcement)
    {
        $sql = 'SELECT nq.id, nq.type, nq.position, nq.main, nq.question
            FROM nps_question_announcement  as nqa    
            JOIN nps_question as nq 
            ON nqa.nps_question_id = nq.id 
            WHERE nqa.announcement_id = :announcement
            AND nq.deleted_at IS NULL ';

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery(['announcement' => $announcement])->fetchAllAssociative();
       
        return $result;
    }
}
