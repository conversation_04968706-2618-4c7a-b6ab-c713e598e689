<?php

namespace App\Repository;

use App\Entity\AnnouncementModality;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementModality>
 *
 * @method AnnouncementModality|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementModality|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementModality[]    findAll()
 * @method AnnouncementModality[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementModalityRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementModality::class);
    }

    public function add(AnnouncementModality $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementModality $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }


    public function getAnnouncementModalities($locale = null)
    {
        $announcementModalities = $this->findBy(['isActive' => true]);

        if (!$announcementModalities) {
            return [];
        }

        $result = [];

        foreach ($announcementModalities as $announcementModality) {
            $result[] = $this->getAnnouncementModality($announcementModality, $locale);
        }

        return $result;
    }

    private function getAnnouncementModality(AnnouncementModality $announcementModality, $locale = null)
    {
        $name = null;

        if (!empty($locale)) {
            /** @var AnnouncementModaliy $translation */
            $translation = $announcementModality->translate($locale);
            $name = $translation->getName();
        }

        return [
            'id' => $announcementModality->getId(),
            'name' => $name ?? $announcementModality->getName()
        ];
    }
}
