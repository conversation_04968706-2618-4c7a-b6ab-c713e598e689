<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementConfigurationType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementConfigurationType>
 *
 * @method AnnouncementConfigurationType|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementConfigurationType|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementConfigurationType[]    findAll()
 * @method AnnouncementConfigurationType[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementConfigurationTypeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementConfigurationType::class);
    }

    public function add(AnnouncementConfigurationType $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementConfigurationType $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getConfigurationAnnouncement()
    {
        return $this->createQueryBuilder('ac')
            ->select('ac')
            ->where('ac.active = true')
            ->getQuery()
            ->getResult();
    }
}
