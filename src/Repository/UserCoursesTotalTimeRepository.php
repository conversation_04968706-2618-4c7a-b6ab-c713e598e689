<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Entity\UserCoursesTotalTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;

/**
 * @extends ServiceEntityRepository<UserCoursesTotalTime>
 *
 * @method UserCoursesTotalTime|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserCoursesTotalTime|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserCoursesTotalTime[]    findAll()
 * @method UserCoursesTotalTime[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserCoursesTotalTimeRepository extends ServiceEntityRepository
{
    private LoggerInterface $logger;

    public function __construct(ManagerRegistry $registry, LoggerInterface $logger)
    {
        $this->logger = $logger;
        parent::__construct($registry, UserCoursesTotalTime::class);
    }

    public function add(UserCoursesTotalTime $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(UserCoursesTotalTime $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function updateUserTime(UserCourse $userCourse): bool
    {
        $user = $userCourse->getUser();
        if (!$user) {
            return false;
        }
        /** @var UserCourseRepository $userCourseRepository */
        $userCourseRepository = $this->_em->getRepository(UserCourse::class);

        /** @var UserCourse[] $userCourses */
        $userCourses = $userCourseRepository->createQueryBuilder('uc')->select('uc')
            ->where('uc.finishedAt is not null')
            ->andWhere('uc.timeSpent is null')
            ->getQuery()
            ->getResult();
        if (\count($userCourses) > 0) {
            // Calculate spent time in courses with value null
            /** @var UserCourseChapterRepository $userCourseChapterRepository */
            $userCourseChapterRepository = $this->_em->getRepository(UserCourseChapter::class);
            foreach ($userCourses as $uc) {
                $chapterSumTime = $userCourseChapterRepository->createQueryBuilder('ucc')
                    ->select('sum(ucc.timeSpent) as time')
                    ->where('ucc.userCourse = :userCourse')
                    ->setParameter('userCourse', $uc)
                    ->getQuery()
                    ->getSingleScalarResult()
                ;
                $uc->setTimeSpent($chapterSumTime);
                $this->_em->persist($uc);
            }
            $this->_em->flush();
        }

        $totalTime = $userCourseRepository
            ->createQueryBuilder('uc')
            ->select('SUM(uc.timeSpent) as timeSpent')
            ->where('uc.user =:user')
            ->andWhere('uc.finishedAt is not null')
            ->setParameter('user', $user)
            ->getQuery()
            ->getSingleScalarResult()
        ;

        $userCourseTotalTime = $this->_em->getRepository(UserCoursesTotalTime::class)->findOneBy(['user' => $user]);
        if (!$userCourseTotalTime) {
            $userCourseTotalTime = new UserCoursesTotalTime();
            $userCourseTotalTime->setUser($user)
                ->setTime(0);
        }
        $userCourseTotalTime->setTime($totalTime)
            ->setUpdatedAt(new \DateTimeImmutable());

        $this->_em->persist($userCourseTotalTime);
        $this->_em->flush();

        return true;
    }
}
