<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\TypeMoneyTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TypeMoneyTranslation>
 *
 * @method TypeMoneyTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method TypeMoneyTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method TypeMoneyTranslation[]    findAll()
 * @method TypeMoneyTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TypeMoneyTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TypeMoneyTranslation::class);
    }

    public function add(TypeMoneyTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(TypeMoneyTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
