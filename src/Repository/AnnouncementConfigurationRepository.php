<?php

namespace App\Repository;

use App\Entity\Announcement;
use App\Entity\AnnouncementConfiguration;
use App\Entity\AnnouncementConfigurationType;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementUser;
use App\Entity\ChatChannel;
use App\Entity\ChatChannelUser;
use App\Entity\ChatServer;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementConfiguration>
 *
 * @method AnnouncementConfiguration|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementConfiguration|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementConfiguration[]    findAll()
 * @method AnnouncementConfiguration[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementConfigurationRepository extends ServiceEntityRepository
{
    private AnnouncementConfigurationTypeRepository $configurationTypeRepository;
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementConfiguration::class);
        $this->configurationTypeRepository = $this->_em->getRepository(AnnouncementConfigurationType::class);
    }

    public function add(AnnouncementConfiguration $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementConfiguration $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function enableConfiguration(Announcement $announcement, int $id, bool $enabled = false): ?AnnouncementConfiguration
    {
        $configuration = $this->configurationTypeRepository->find($id);
        $announcementConfiguration = $this->findOneBy([
            'announcement' => $announcement,
            'configuration' => $configuration
        ]);
        if ($enabled) {
            if (!$announcementConfiguration) {
                $announcementConfiguration = new AnnouncementConfiguration();
                $announcementConfiguration->setAnnouncement($announcement)
                    ->setConfiguration($configuration);

                $this->_em->persist($announcementConfiguration);
            }
            return $announcementConfiguration;
        } else {
            if ($announcementConfiguration) {
                $this->_em->remove($announcementConfiguration);
            }
            return null;
        }
    }

    public function enableTemporalization(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_ENABLE_TEMPORALIZATION, $enabled);
    }

    public function enableSubsidizedCourse(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_ENABLE_SUBSIDIZED_COURSE, $enabled);
    }

    public function enableChat(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        $configuration = $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_ENABLE_CHAT, $enabled);

        if ($enabled) {
            // Enable the channel
            $server = $this->_em->getRepository(ChatServer::class)->getServer(ChatServer::TYPE_ANNOUNCEMENT, $announcement->getId(), true);
            $this->_em->getRepository(ChatChannel::class)->createDirectChannel(
                $server,
                null,
                'CHAT',
                [],
                $announcement->getStartAt(),
                $announcement->getFinishAt(),
                AnnouncementGroup::TYPE_ANNOUNCEMENT_DIRECT
            );

            /**
             * Create group chat
             */
            foreach ($announcement->getAnnouncementGroups() as $g) {
                $groupChannel = $this->_em->getRepository(ChatChannel::class)->getChannelByEntityType(
                    $server,
                    Announcement::CHAT_CHANNEL_GROUP_CHAT,
                    $g->getId(),
                    AnnouncementGroup::TYPE_ANNOUNCEMENT_GROUP,
                    true
                );

                $this->_em->getRepository(AnnouncementUser::class)->addUsersToChannel($g, $groupChannel);
            }
        }
        return $configuration;
    }

    public function enableNotification(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_ENABLE_NOTIFICATION, $enabled);
    }

    public function enableSMS(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_ENABLE_SMS, $enabled);
    }
    public function enableForum(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        $configuration = $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_ENABLE_FORUM, $enabled);

        if ($enabled) {
            $server = $this->_em->getRepository(ChatServer::class)->getServer(ChatServer::TYPE_ANNOUNCEMENT, $announcement->getId(), true);
            $groups = $announcement->getAnnouncementGroups();
            foreach ($groups as $g) {
                // Create a forum channel per group
                $channel = $this->_em->getRepository(ChatChannel::class)->createNormalChannel($server, null, 'FORO', Announcement::CHAT_CHANNEL_FORUM);
                $channel->setEntityId($g->getId())
                    ->setEntityType(AnnouncementGroup::TYPE_ANNOUNCEMENT_GROUP);
                $this->_em->persist($channel);
            }
            $this->_em->flush();
        }

        return $configuration;
    }

    public function enableCertificate(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_ENABLE_CERTIFICATE, $enabled);
    }

    public function enableObjetiveAndContentCertificate(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_INCLUDE_OBJ_CONTENT_CERTIFICATE, $enabled);
    }

    public function enableDniInCertificate(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_INCLUDE_DNI_IN_CERTIFICATE, $enabled);
    }

    public function enableAlertsTutor(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_ENABLE_ALERTS_TUTOR, $enabled);
    }

    public function enableSurvey(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_ENABLE_SURVEY, $enabled);
    }

    public function enableAllowCourseAtEnd(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_ALLOW_ACTIVE_COURSE_AT_END, $enabled);
    }

    public function enableDigitalSignature(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_DIGITAL_SIGNATURE, $enabled);
    }

    public function enableCost(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_COST, $enabled);
    }

    public function enableEmailNotificationOnAnnouncement(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_ENABLE_EMAIL_NOTIFICATION_ON_ANNOUNCEMENT, $enabled);
    }

    public function enableNotificationOnAnnouncement(Announcement $announcement, bool $enabled = false): ?AnnouncementConfiguration
    {
        return $this->enableConfiguration($announcement, AnnouncementConfigurationType::ID_ENABLE_NOTIFICATION_ON_ANNOUNCEMENT, $enabled);
    }
}
