<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ClassroomvirtualType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ClassroomvirtualType>
 *
 * @method ClassroomvirtualType|null find($id, $lockMode = null, $lockVersion = null)
 * @method ClassroomvirtualType|null findOneBy(array $criteria, array $orderBy = null)
 * @method ClassroomvirtualType[]    findAll()
 * @method ClassroomvirtualType[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ClassroomvirtualTypeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ClassroomvirtualType::class);
    }

    public function add(ClassroomvirtualType $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ClassroomvirtualType $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
