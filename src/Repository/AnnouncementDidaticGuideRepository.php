<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementDidaticGuide;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementDidaticGuide>
 *
 * @method AnnouncementDidaticGuide|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementDidaticGuide|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementDidaticGuide[]    findAll()
 * @method AnnouncementDidaticGuide[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementDidaticGuideRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementDidaticGuide::class);
    }

    public function add(AnnouncementDidaticGuide $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementDidaticGuide $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
