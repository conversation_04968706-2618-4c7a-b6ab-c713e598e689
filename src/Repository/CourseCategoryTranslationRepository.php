<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\CourseCategoryTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method CourseCategoryTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method CourseCategoryTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method CourseCategoryTranslation[]    findAll()
 * @method CourseCategoryTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CourseCategoryTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CourseCategoryTranslation::class);
    }
}
