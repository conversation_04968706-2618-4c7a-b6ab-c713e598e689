<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementUser;
use App\Entity\ChatChannel;
use App\Entity\ChatChannelUser;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method AnnouncementUser|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementUser|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementUser[]    findAll()
 * @method AnnouncementUser[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementUserRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementUser::class);
    }

    public function delete(AnnouncementUser $announcementUser): void
    {
        $this->_em->remove($announcementUser);
        $this->_em->flush();
    }

    public function persist(AnnouncementUser $announcementUser): void
    {
        $this->_em->persist($announcementUser);
        $this->_em->flush();
    }

    /**
     * @return User[]
     */
    public function getUsers(AnnouncementGroup $announcementGroup): array
    {
        return $this->_em->getRepository(User::class)->createQueryBuilder('u')
            ->select('u')
            ->join('u.announcements', 'au')
            ->where('au.announcementGroup = :group')
            ->andWhere('u.deletedAt IS NULL')
            ->setParameter('group', $announcementGroup)
            ->groupBy('u')
            ->getQuery()
            ->getResult();
    }

    public function addUsersToChannel(AnnouncementGroup $announcementGroup, ChatChannel $channel)
    {
        /**
         * Add all users to the group.
         */
        $users = $this->getUsers($announcementGroup);
        $chatChannelUsers = [];
        foreach ($users as $u) {
            $chatChannelUser = new ChatChannelUser();
            $chatChannelUser->setUser($u)
                ->setChannel($channel);
            $chatChannelUsers[] = $chatChannelUser;
        }
        $channel->setChatChannelUsers($chatChannelUsers);
        $this->_em->persist($channel);
        $this->_em->flush();
    }

    public function getUsersByAnnouncement(Announcement $announcement)
    {
        return $this->createQueryBuilder('au')
        ->select('au')
        ->leftJoin('au.user', 'u')
        ->andWhere('au.announcement = :announcement')
        ->andWhere('u.id IS NOT NULL')  // Agregar verificación de usuario existente
        ->andWhere('u.deletedAt IS NULL')
        ->setParameter('announcement', $announcement)
        ->getQuery()
        ->getResult();
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getUserByAnnouncement(Announcement $announcement, User $user)
    {
        return $this->createQueryBuilder('au')
        ->select('au')
        ->leftJoin('au.user', 'u')
        ->andWhere('au.announcement = :announcement')
        ->andWhere('u = :user')
        ->andWhere('u.deletedAt IS NULL')
        ->setParameter('announcement', $announcement)
        ->setParameter('user', $user)
        ->getQuery()
        ->getOneOrNullResult();
    }

    public function getUserCalledPaginated(Announcement $announcement)
    {
        $query = $this->createQueryBuilder('au')
            ->join('au.user', 'u')
            ->where('au.announcement = :announcement')
            ->setParameter('announcement', $announcement);
        $totalItemsQuery = clone $query;
        $totalItems = $totalItemsQuery->select('count(u.id) as total')->getQuery()->getSingleScalarResult();

        return ['data' => $query
            ->select('au.id AS callId', 'au.notified', 'u.id', 'u.email', 'u.firstName', 'u.lastName', 'u.avatar')
            ->getQuery()
            ->getResult(), 'totalItems' => $totalItems];
    }

    public function getSelectedUsersIds(Announcement $announcement)
    {
        return $this->createQueryBuilder('au')
            ->select('u.id')
            ->join('au.user', 'u')
            ->where('au.announcement = :announcement')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();
    }

    public function getAnnouncementUserCall(Announcement $announcement)
    {
        return $this->createQueryBuilder('au')
            ->select('au.id AS callId', 'au.notified', 'u.id', 'u.email', 'u.firstName', 'u.lastName', 'u.avatar')
            ->join('au.user', 'u')
            ->where('au.announcement = :announcement')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();
    }

    public function getAnnouncementUser(Announcement $announcement, User $user)
    {
        return $this->createQueryBuilder('au')
            ->select('au.id AS callId', 'au.notified', 'u.id', 'u.email', 'u.firstName', 'u.lastName', 'u.avatar')
            ->join('au.user', 'u')
            ->where('au.announcement = :announcement')
            ->andWhere('au.user =:user')
            ->setParameter('announcement', $announcement)
            ->setParameter('user', $user)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function getAnnouncementTotal(int $announcementId, User $user)
    {
        return $this->createQueryBuilder('au')
            ->select('count(au.id) as total')
            ->join('au.announcementGroup', 'g')
            ->join('g.announcementTutor', 't')
            ->join('au.announcement', 'a')
            ->where('t.tutor =:user_tutor')
            ->andWhere('a.id =:announcement_id')
            ->setParameter('user_tutor', $user)
            ->setParameter('announcement_id', $announcementId)
            ->getQuery()
            ->getSingleScalarResult();
    }
}
