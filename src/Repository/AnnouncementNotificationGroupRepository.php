<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementNotificationGroup;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementNotificationGroup>
 *
 * @method AnnouncementNotificationGroup|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementNotificationGroup|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementNotificationGroup[]    findAll()
 * @method AnnouncementNotificationGroup[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementNotificationGroupRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementNotificationGroup::class);
    }

    public function add(AnnouncementNotificationGroup $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementNotificationGroup $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
