<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ClassroomVirtualResult;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ClassroomVirtualResult>
 *
 * @method ClassroomVirtualResult|null find($id, $lockMode = null, $lockVersion = null)
 * @method ClassroomVirtualResult|null findOneBy(array $criteria, array $orderBy = null)
 * @method ClassroomVirtualResult[]    findAll()
 * @method ClassroomVirtualResult[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ClassroomVirtualResultRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ClassroomVirtualResult::class);
    }

    public function add(ClassroomVirtualResult $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ClassroomVirtualResult $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
