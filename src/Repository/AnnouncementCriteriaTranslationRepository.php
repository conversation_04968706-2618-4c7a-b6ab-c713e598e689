<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementCriteriaTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementCriteriaTranslation>
 *
 * @method AnnouncementCriteriaTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementCriteriaTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementCriteriaTranslation[]    findAll()
 * @method AnnouncementCriteriaTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementCriteriaTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementCriteriaTranslation::class);
    }

    public function add(AnnouncementCriteriaTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementCriteriaTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
