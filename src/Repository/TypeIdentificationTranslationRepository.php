<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\TypeIdentificationTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TypeIdentificationTranslation>
 *
 * @method TypeIdentificationTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method TypeIdentificationTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method TypeIdentificationTranslation[]    findAll()
 * @method TypeIdentificationTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TypeIdentificationTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TypeIdentificationTranslation::class);
    }

    public function add(TypeIdentificationTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(TypeIdentificationTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
