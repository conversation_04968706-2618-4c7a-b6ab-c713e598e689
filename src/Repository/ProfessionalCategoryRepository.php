<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ProfessionalCategory;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method ProfessionalCategory|null find($id, $lockMode = null, $lockVersion = null)
 * @method ProfessionalCategory|null findOneBy(array $criteria, array $orderBy = null)
 * @method ProfessionalCategory[]    findAll()
 * @method ProfessionalCategory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProfessionalCategoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProfessionalCategory::class);
    }

    public function getList()
    {
        $categories = $this->findBy([], ['code' => 'ASC', 'name' => 'ASC']);
        $list = [];
        foreach ($categories as $category) {
            $list[$category->getId()] = $category->getFullName();
        }

        return $list;
    }

    public function findList($categories = [])
    {
        $query = $this->createQueryBuilder('c');

        if (!empty($categories)) {
            $query->andWhere($query->expr()->in('c.id', $categories));
        }

        return $query
            ->orderBy('c.name', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }

    public function getProfesionalCategoryUser()
    {
        return $this->createQueryBuilder('pc')
//            ->andWhere('pc.code LIKE :code')
            ->orderBy('pc.name', 'ASC')
//            ->setParameter('code','400%')
            ->getQuery()
            ->getResult();
    }
}
