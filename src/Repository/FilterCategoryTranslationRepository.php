<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\FilterCategoryTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<FilterCategoryTranslation>
 *
 * @method FilterCategoryTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method FilterCategoryTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method FilterCategoryTranslation[]    findAll()
 * @method FilterCategoryTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FilterCategoryTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, FilterCategoryTranslation::class);
    }

    public function add(FilterCategoryTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(FilterCategoryTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
