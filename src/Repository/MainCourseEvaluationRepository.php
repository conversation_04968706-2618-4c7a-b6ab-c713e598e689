<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\MainCourseEvaluation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<MainCourseEvaluation>
 *
 * @method MainCourseEvaluation|null find($id, $lockMode = null, $lockVersion = null)
 * @method MainCourseEvaluation|null findOneBy(array $criteria, array $orderBy = null)
 * @method MainCourseEvaluation[]    findAll()
 * @method MainCourseEvaluation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MainCourseEvaluationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MainCourseEvaluation::class);
    }

    public function add(MainCourseEvaluation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(MainCourseEvaluation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
