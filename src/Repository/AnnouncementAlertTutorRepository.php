<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementAlertTutor;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementAlertTutor>
 *
 * @method AnnouncementAlertTutor|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementAlertTutor|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementAlertTutor[]    findAll()
 * @method AnnouncementAlertTutor[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementAlertTutorRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementAlertTutor::class);
    }

    public function add(AnnouncementAlertTutor $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementAlertTutor $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
