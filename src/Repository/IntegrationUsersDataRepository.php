<?php

namespace App\Repository;

use App\Entity\IntegrationUsersData;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<IntegrationUsersData>
 *
 * @method IntegrationUsersData|null find($id, $lockMode = null, $lockVersion = null)
 * @method IntegrationUsersData|null findOneBy(array $criteria, array $orderBy = null)
 * @method IntegrationUsersData[]    findAll()
 * @method IntegrationUsersData[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class IntegrationUsersDataRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, IntegrationUsersData::class);
    }

    public function add(IntegrationUsersData $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(IntegrationUsersData $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return IntegrationUsersData[] Returns an array of IntegrationUsersData objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('i')
//            ->andWhere('i.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('i.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?IntegrationUsersData
//    {
//        return $this->createQueryBuilder('i')
//            ->andWhere('i.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
