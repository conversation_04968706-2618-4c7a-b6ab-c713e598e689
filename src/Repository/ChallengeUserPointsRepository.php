<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChallengeUserPoints;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method ChallengeUserPoints|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChallengeUserPoints|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChallengeUserPoints[]    findAll()
 * @method ChallengeUserPoints[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChallengeUserPointsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ChallengeUserPoints::class);
    }
}
