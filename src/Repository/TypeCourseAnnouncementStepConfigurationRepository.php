<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\TypeCourseAnnouncementStepConfiguration;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TypeCourseAnnouncementStepConfiguration>
 *
 * @method TypeCourseAnnouncementStepConfiguration|null find($id, $lockMode = null, $lockVersion = null)
 * @method TypeCourseAnnouncementStepConfiguration|null findOneBy(array $criteria, array $orderBy = null)
 * @method TypeCourseAnnouncementStepConfiguration[]    findAll()
 * @method TypeCourseAnnouncementStepConfiguration[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TypeCourseAnnouncementStepConfigurationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TypeCourseAnnouncementStepConfiguration::class);
    }

    public function add(TypeCourseAnnouncementStepConfiguration $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(TypeCourseAnnouncementStepConfiguration $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
