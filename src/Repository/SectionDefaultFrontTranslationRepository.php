<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\SectionDefaultFrontTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SectionDefaultFrontTranslation>
 *
 * @method SectionDefaultFrontTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method SectionDefaultFrontTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method SectionDefaultFrontTranslation[]    findAll()
 * @method SectionDefaultFrontTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SectionDefaultFrontTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SectionDefaultFrontTranslation::class);
    }

    public function add(SectionDefaultFrontTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(SectionDefaultFrontTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
