<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementUserDigitalSignature;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementUserDigitalSignature>
 *
 * @method AnnouncementUserDigitalSignature|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementUserDigitalSignature|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementUserDigitalSignature[]    findAll()
 * @method AnnouncementUserDigitalSignature[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementUserDigitalSignatureRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementUserDigitalSignature::class);
    }

    public function add(AnnouncementUserDigitalSignature $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementUserDigitalSignature $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
