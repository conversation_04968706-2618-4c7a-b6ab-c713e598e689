<?php

namespace App\Repository;

use App\Entity\CourseSegmentCategory;
use App\Entity\CourseSegmentCategoryTranslation;
use App\Entity\CourseSegmentTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method CourseSegmentCategory|null find($id, $lockMode = null, $lockVersion = null)
 * @method CourseSegmentCategory|null findOneBy(array $criteria, array $orderBy = null)
 * @method CourseSegmentCategory[]    findAll()
 * @method CourseSegmentCategory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CourseSegmentCategoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CourseSegmentCategory::class);
   }

    public function getCourseSegmentCategoryTranslation($courseSegmentCategory, $locale){
        $query = $this->_em->createQueryBuilder();

        $query->select('cst.name as name');
        $query->from(CourseSegmentCategoryTranslation::class, 'cst');
        $query->where('cst.translatable = :courseSegmentCategory');
        $query->andWhere('cst.locale = :locale');

        $query
        ->setParameter('courseSegmentCategory', $courseSegmentCategory)
        ->setParameter('locale', $locale);
       return  $query->getQuery()->getOneOrNullResult();
       
    }

    public function getCourseSegmentTranslation($courseSegment, $locale){
        $query = $this->_em->createQueryBuilder();

        $query->select('cst.name as name');
        $query->from(CourseSegmentTranslation::class, 'cst');
        $query->where('cst.translatable = :courseSegment');
        $query->andWhere('cst.locale = :locale');

        $query
        ->setParameter('courseSegment', $courseSegment)
        ->setParameter('locale', $locale);
       return  $query->getQuery()->getOneOrNullResult();
       
    }
}
