<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Classroomvirtual;
use App\Entity\ClassroomvirtualUser;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ClassroomvirtualUser>
 *
 * @method ClassroomvirtualUser|null find($id, $lockMode = null, $lockVersion = null)
 * @method ClassroomvirtualUser|null findOneBy(array $criteria, array $orderBy = null)
 * @method ClassroomvirtualUser[]    findAll()
 * @method ClassroomvirtualUser[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ClassroomvirtualUserRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ClassroomvirtualUser::class);
    }

    public function add(ClassroomvirtualUser $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ClassroomvirtualUser $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function deleteClassroomvirtualUser(Classroomvirtual $classroomvirtual): void
    {
        $builder = $this->createQueryBuilder('c');
        $builder->delete()
                ->where('c.classroomvirtual = :classroomvirtual')
                ->setParameter('classroomvirtual', $classroomvirtual);
        $query = $builder->getQuery();
        $query->execute();
    }
}
