<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChallengeDuelQuestions;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method ChallengeDuelQuestions|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChallengeDuelQuestions|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChallengeDuelQuestions[]    findAll()
 * @method ChallengeDuelQuestions[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChallengeDuelQuestionsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ChallengeDuelQuestions::class);
    }

    /**
     * @return int Returns all total correct answers by challenge id
     */
    public function getAllCorrectAnswers1ByIdChallenge($challenge)
    {
        return $this->createQueryBuilder('c')
            ->select('count(c.id)')
            ->join('c.duel', 'cd')
            ->Where('cd.challenge = :challenge')
            ->join('c.answerUser1', 'ca')
            ->andWhere('ca.correct = 1')
            ->setParameter('challenge', $challenge)
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    /**
     * @return int Returns all total correct answers by challenge id
     */
    public function getAllCorrectAnswers2ByIdChallenge($challenge)
    {
        return $this->createQueryBuilder('c')
            ->select('count(c.id)')
            ->join('c.duel', 'cd')
            ->Where('cd.challenge = :challenge')
            ->join('c.answerUser2', 'ca')
            ->andWhere('ca.correct = 1')
            ->setParameter('challenge', $challenge)
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    /**
     * @return int Returns all total correct answers by challenge id
     */
    public function getUniqueQuestionForChallenge()
    {
        return $this->createQueryBuilder('cdq')
            ->select('IDENTITY(cdq.question) as questionId')
            ->distinct()
            ->join('cdq.question', 'cq')
            ->Where('cq.desafio = :challenge')
            ->setParameter('challenge', 1)
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @return int Returns all total answers by challenge_questionid
     */
    public function getAllAnswersWithSumAnswersByChallengeQuestionId($challengeQuestionId)
    {
        $sql = 'SELECT challenge_questions.id as questionId, challenge_answers.id as answerId,
                (
                (SELECT count(answer_user1_id) FROM challenge_duel_questions  where answer_user1_id = challenge_answers.id )
                +
                (SELECT count(answer_user2_id) FROM challenge_duel_questions  where answer_user2_id = challenge_answers.id )
                ) as SumAnswers
                FROM challenge_questions
                JOIN challenge_answers
                ON challenge_answers.pregunta_id = challenge_questions.id
                WHERE challenge_questions.id = :challengeQuestionId
                ';
        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);        
        $result = $stmt->executeQuery(['challengeQuestionId' => $challengeQuestionId])->fetchAllAssociative();
       
        return $result;
    }

    /**
     * @return int Returns all SUM of total answers by challenge_questionid
     */
    public function getAllSumAnswerByChallengeQuestionId($challengeQuestionId)
    {
        $sql = 'SELECT SUM(
                (SELECT count(answer_user1_id) FROM challenge_duel_questions  where answer_user1_id = challenge_answers.id )
                +
                (SELECT count(answer_user2_id) FROM challenge_duel_questions  where answer_user2_id = challenge_answers.id )
                ) as totalAnswers
                FROM challenge_questions
                JOIN challenge_answers
                ON challenge_answers.pregunta_id = challenge_questions.id
                WHERE challenge_questions.id = :challengeQuestionId
                ';
        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery(['challengeQuestionId' => $challengeQuestionId])->fetchAllAssociative();
      
        return $result;
    }
}
