<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\UserProfessionalCategory;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<UserProfessionalCategory>
 *
 * @method UserProfessionalCategory|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserProfessionalCategory|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserProfessionalCategory[]    findAll()
 * @method UserProfessionalCategory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserProfessionalCategoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserProfessionalCategory::class);
    }

    public function add(UserProfessionalCategory $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(UserProfessionalCategory $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findAllOrderByName(): array
    {
        $query = $this->createQueryBuilder('s')
            ->select('s')
            ->orderBy('s.name', 'ASC');

        return $query->getQuery()->getResult();
    }
}
