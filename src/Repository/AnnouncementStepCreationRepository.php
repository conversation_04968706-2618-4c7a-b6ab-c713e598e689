<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementStepCreation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementStepCreation>
 *
 * @method AnnouncementStepCreation|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementStepCreation|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementStepCreation[]    findAll()
 * @method AnnouncementStepCreation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementStepCreationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementStepCreation::class);
    }

    public function add(AnnouncementStepCreation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementStepCreation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
