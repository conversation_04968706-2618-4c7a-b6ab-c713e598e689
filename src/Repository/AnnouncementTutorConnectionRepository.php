<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementTutorConnection;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementTutorConnection>
 *
 * @method AnnouncementTutorConnection|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementTutorConnection|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementTutorConnection[]    findAll()
 * @method AnnouncementTutorConnection[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementTutorConnectionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementTutorConnection::class);
    }

    public function add(AnnouncementTutorConnection $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementTutorConnection $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
