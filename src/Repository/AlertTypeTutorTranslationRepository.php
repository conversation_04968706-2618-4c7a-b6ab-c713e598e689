<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AlertTypeTutorTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AlertTypeTutorTranslation>
 *
 * @method AlertTypeTutorTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method AlertTypeTutorTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method AlertTypeTutorTranslation[]    findAll()
 * @method AlertTypeTutorTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AlertTypeTutorTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AlertTypeTutorTranslation::class);
    }

    public function add(AlertTypeTutorTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AlertTypeTutorTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
