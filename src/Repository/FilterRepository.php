<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @deprecated For creation and reading use V2 FilterRepository
 * @see \App\V2\Domain\Filter\FilterRepository
 *
 * @method Filter|null find($id, $lockMode = null, $lockVersion = null)
 * @method Filter|null findOneBy(array $criteria, array $orderBy = null)
 * @method Filter[]    findAll()
 * @method Filter[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FilterRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Filter::class);
    }

    public function getParents($notIn = [])
    {
        $query = $this->createQueryBuilder('c')
            ->andWhere('c.parent IS NULL');

        if (!empty($notIn)) {
            $query->andWhere($query->expr()->notIn('c.id', $notIn));
        }

        return $query->orderBy('c.id', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function getList()
    {
        $filters = $this->findBy([], ['name' => 'ASC']);
        $filterList = [];
        foreach ($filters as $filter) {
            $filterList[$filter->getFilterCategory()->getName() . ': ' . $filter->getName()] = $filter->getId();
        }

        return $filterList;
    }

    public function getFilter()
    {
        $query = $this->createQueryBuilder('c')
            ->andWhere('c.filterCategory = c.filterCategory')
            ->orderBy('c.filterCategory', 'ASC')
            ->getQuery()
            ->getResult();

        return $query;
    }

    public function getCourseFilters($id_course, $id_category)
    {
        return $this->createQueryBuilder('c')
            ->andWhere('cf.id = :id_course')
            ->andWhere('c.filterCategory = :id_category')
            ->join('c.courses', 'cf')
            ->setParameter('id_category', $id_category)
            ->setParameter('id_course', $id_course)
            ->getQuery()
            ->getResult();
    }

    public function getManagerFilters($id_user, $id_category)
    {
        return $this->createQueryBuilder('f')
            ->join('f.managerFilters', 'managerFilters')
            ->andWhere('managerFilters.id = :id_user')
            ->andWhere('f.filterCategory = :id_category')
            ->setParameter('id_category', $id_category)
            ->setParameter('id_user', $id_user)
            ->orderBy('f.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function getFilterCategory($filter_category_id)
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.filterCategory = :filter_category_id')
            ->setParameter('filter_category_id', $filter_category_id)
            ->getQuery()
            ->getResult();
    }

    public function getFilterCategoryManager($filters_id, $locale = 'en')
    {
        $query = $this->createQueryBuilder('f');
        $query->select('DISTINCT(fc.id) as id, COALESCE(t.name, fc.name) AS name');
        $query->leftJoin('f.translations', 't', 'WITH', 't.locale = :locale');
        $query->leftJoin('f.filterCategory', 'fc');

        if ($filters_id) {
            $query->andWhere($query->expr()->in('f.id', $filters_id));
        }

        $query->setParameter('locale', $locale);

        return $query->getQuery()
            ->getResult();
    }

    public function getFiltersByArray(array $filters_ids)
    {
        $qb = $this->createQueryBuilder('f');
        $qb->andWhere($qb->expr()->in('f.id', $filters_ids));

        return $qb->getQuery()->getResult();
    }

    public function getFilterDataExport($filters = [])
    {
        $query = $this->createQueryBuilder('f');

        $query->andWhere($query->expr()->in('f.id', $filters));

        return $query->getQuery()->getResult();
    }

    public function countUsersByFilterCategory(FilterCategory $category)
    {
        $query = $this->createQueryBuilder('f')
            ->select('COUNT(DISTINCT(u.id)) as count, f.name, f.id')
            ->leftJoin('f.users', 'u')
            ->where('f.filterCategory = :category')
            ->setParameter('category', $category)
            ->andWhere('u.isActive = 1')
            ->groupBy('f.id')
            ->orderBy('f.name', 'ASC')
        ;

        return $query->getQuery()->getResult();
    }

    public function fetchAllFiltersForUsers(array $userIds, array $categoryIds): array
    {
        if (empty($userIds) || empty($categoryIds)) {
            return [];
        }

        $qb = $this->getEntityManager()->createQueryBuilder();

        $qb->select('u.id AS userId', 'f.name AS filterName', 'fc.id AS categoryId')
            ->from(User::class, 'u')
            ->innerJoin('u.filter', 'f')
            ->innerJoin('f.filterCategory', 'fc')
            ->where($qb->expr()->in('u.id', ':userIds'))
            ->andWhere($qb->expr()->in('fc.id', ':categoryIds'))
            ->setParameter('userIds', $userIds)
            ->setParameter('categoryIds', $categoryIds);

        return $qb->getQuery()->getResult(Query::HYDRATE_ARRAY);
    }

    public function fetchFiltersUsers($idUser, $filterCategory, $bGetAllResults = false)
    {
        $sql = 'SELECT uf.user_id, uf.filter_id,  
        f.name, f.filter_category_id  as category
        FROM user_filter uf
        LEFT JOIN user u on uf.user_id = u.id
        LEFT JOIN filter f on uf.filter_id = f.id
        WHERE u.id = :user
        AND f.filter_category_id = :filterCategory';

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery(['user' => $idUser, 'filterCategory' => $filterCategory])->fetchAllAssociative();

        $data = null;
        if ($bGetAllResults) {
            $data = $result;
        } else {
            $data = array_pop($result);
        }

        return $data;
    }

    public function fetchFiltersUsersAll($idUser, $filterCategory)
    {
        $sql = 'SELECT uf.user_id, uf.filter_id,  
           f.name, f.filter_category_id  as category
           FROM user_filter uf
           LEFT JOIN user u on uf.user_id = u.id
           LEFT JOIN filter f on uf.filter_id = f.id
           WHERE u.id = :user
           AND f.filter_category_id = :filterCategory';

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery(['user' => $idUser, 'filterCategory' => $filterCategory])->fetchAllAssociative();

        return $result;
    }

    public function segmentedTotalByUserQuery($conditions = [])
    {
        $query = $this->createQueryBuilder('uc')
            ->select('count(uc.id) as tota')

        ;

        // $this->setFinishedCoursesFilters($query, $conditions);

        return $query->getQuery()->getResult();
    }

    public function findOrCreateFilter(FilterCategory $category, string $name, ?string $code = null, string $source = Filter::SOURCE_REMOTE): Filter
    {
        if (empty($code)) {
            $code = $name;
        }
        $filter = $this->_em->getRepository(Filter::class)->findOneBy([
            'code' => $name,
            'name' => $name,
            'filterCategory' => $category,
        ]);
        if ($filter) {
            return $filter;
        }
        $filter = new Filter();
        $filter->setFilterCategory($category)
            ->setCode($name)
            ->setName($name)
            ->setSort(0)
            ->setSource($source)
        ;
        $this->_em->persist($filter);

        return $filter;
    }

    public function getFiltersByItinerary($itinerary)
    {
        return $this->createQueryBuilder('f')
            ->select('f.id as filterId', 'f.name', 'cat.id as categoryId')
            ->join('f.itineraries', 'i')
            ->join('f.filterCategory', 'cat')
            ->where('i = :itinerary')
            ->setParameter('itinerary', $itinerary)
            ->getQuery()
            ->getResult();
    }

    public function getFiltersByCourse($course)
    {
        return $this->createQueryBuilder('f')
            ->select('f.id', 'cat.id as categoryId')
            ->join('f.courses', 'c')
            ->join('f.filterCategory', 'cat')
            ->where('c = :course')
            ->setParameter('course', $course)
            ->getQuery()
            ->getResult();
    }

    public function findAllOrderBySort(FilterCategory $filterCategory): array
    {
        $query = $this->createQueryBuilder('s')
            ->select('s')
            ->where('s.filterCategory = :filterCategory')
            ->setParameter('filterCategory', $filterCategory)
            ->orderBy('s.sort', 'ASC');

        return $query->getQuery()->getResult();
    }

    public function getNextSort(FilterCategory $filterCategory)
    {
        $maxSort = $this->createQueryBuilder('s')
            ->select('s')
            ->where('s.filterCategory = :filterCategory')
            ->setParameter('filterCategory', $filterCategory)
            ->orderBy('s.sort', 'DESC')
            ->getQuery()->getResult();

        $nextSort = $maxSort ? $maxSort[0]->getSort() + 1 : 1;

        return $nextSort;
    }

    public function getFiltersTranslatedByFilterCategory($filterCategory, $locale)
    {
        $query = $this->createQueryBuilder('f')
            ->select('f.id, COALESCE(ft.name, f.name) as name')
            ->leftJoin('f.translations', 'ft', 'WITH', 'ft.locale = :locale')
            ->where('f.filterCategory = :category')
            ->andWhere('f.name != :emptyName')
            ->setParameter('category', $filterCategory)
            ->setParameter('locale', $locale)
            ->setParameter('emptyName', '');

        return $query->getQuery()->getArrayResult();
    }
}
