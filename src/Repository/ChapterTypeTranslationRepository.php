<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChapterTypeTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ChapterTypeTranslation>
 *
 * @method ChapterTypeTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChapterTypeTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChapterTypeTranslation[]    findAll()
 * @method ChapterTypeTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChapterTypeTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ChapterTypeTranslation::class);
    }

    public function add(ChapterTypeTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ChapterTypeTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
