<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChallengeAnswersTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ChallengeAnswersTranslation>
 *
 * @method ChallengeAnswersTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChallengeAnswersTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChallengeAnswersTranslation[]    findAll()
 * @method ChallengeAnswersTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChallengeAnswersTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ChallengeAnswersTranslation::class);
    }

    public function add(ChallengeAnswersTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ChallengeAnswersTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
