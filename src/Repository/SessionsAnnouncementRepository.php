<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\SessionsAnnouncement;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SessionsAnnouncement>
 *
 * @method SessionsAnnouncement|null find($id, $lockMode = null, $lockVersion = null)
 * @method SessionsAnnouncement|null findOneBy(array $criteria, array $orderBy = null)
 * @method SessionsAnnouncement[]    findAll()
 * @method SessionsAnnouncement[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SessionsAnnouncementRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SessionsAnnouncement::class);
    }

    public function add(SessionsAnnouncement $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(SessionsAnnouncement $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
