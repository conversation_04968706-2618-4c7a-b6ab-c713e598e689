<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ItineraryTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ItineraryTranslation>
 *
 * @method ItineraryTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method ItineraryTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method ItineraryTranslation[]    findAll()
 * @method ItineraryTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ItineraryTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ItineraryTranslation::class);
    }

    public function add(ItineraryTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ItineraryTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
