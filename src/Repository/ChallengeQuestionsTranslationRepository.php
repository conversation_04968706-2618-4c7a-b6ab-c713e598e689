<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChallengeQuestionsTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ChallengeQuestionsTranslation>
 *
 * @method ChallengeQuestionsTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChallengeQuestionsTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChallengeQuestionsTranslation[]    findAll()
 * @method ChallengeQuestionsTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChallengeQuestionsTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ChallengeQuestionsTranslation::class);
    }

    public function add(ChallengeQuestionsTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ChallengeQuestionsTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
