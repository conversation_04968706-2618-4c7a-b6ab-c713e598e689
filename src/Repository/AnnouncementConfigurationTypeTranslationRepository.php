<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementConfigurationTypeTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementConfigurationTypeTranslation>
 *
 * @method AnnouncementConfigurationTypeTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementConfigurationTypeTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementConfigurationTypeTranslation[]    findAll()
 * @method AnnouncementConfigurationTypeTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementConfigurationTypeTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementConfigurationTypeTranslation::class);
    }

    public function add(AnnouncementConfigurationTypeTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementConfigurationTypeTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
