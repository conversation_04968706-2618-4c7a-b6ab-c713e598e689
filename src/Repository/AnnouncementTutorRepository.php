<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementTutor;
use App\Entity\FilesManager;
use App\Entity\TypeIdentification;
use App\Entity\User;
use App\Entity\UserCompany;
use App\Entity\UserFieldsFundae;
use App\Entity\UserIdentification;
use App\Service\FilesManager\FilesManagerService;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * @method AnnouncementTutor|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementTutor|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementTutor[]    findAll()
 * @method AnnouncementTutor[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementTutorRepository extends ServiceEntityRepository
{
    private string $baseCvPath;
    private FilesManagerService $filesManagerService;

    public function __construct(ManagerRegistry $registry, ParameterBagInterface $params, FilesManagerService $filesManagerService)
    {
        parent::__construct($registry, AnnouncementTutor::class);
        $this->baseCvPath = '/' . $params->get('app.pdf_cv_file_uploads_path');
        $this->filesManagerService = $filesManagerService;
    }

    public function getTutorByGroup(AnnouncementGroup $group, bool $cvAsFile = false)
    {
        $tutor = $this->createQueryBuilder('at')
            ->select('at.dni as identificationValue', 'at.email', 'at.telephone', 'at.filename', 'at.originalName', 'at.tutoringTime')
            ->addSelect("CONCAT(u.firstName, ' ', u.lastName) as name", 'u.id as tutorId')
            ->addSelect('cv_info.id as cv_filesManagerId')
            ->addSelect('uc.id as idCompany')
            ->join('at.tutor', 'u')
            ->leftJoin('at.cv_filesManager', 'cv_info')
            ->leftJoin('at.userCompany', 'uc')
            ->where('at.announcementGroup =:group')
            ->setParameter('group', $group)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
        if (!$tutor) {
            return [];
        }

        /** @var FilesManager|null $cv_filesManager */
        $cv_filesManager = empty($tutor['cv_filesManagerId']) ? null : $this->_em->getRepository(FilesManager::class)->find($tutor['cv_filesManagerId']);

        $tutor['filename'] = $cv_filesManager ? ('/files/' . $cv_filesManager->getFilename()) : ($this->baseCvPath . '/' . $tutor['filename']);

        if ($cvAsFile) {
            $tutor['cv'] = [
                'name' => $cv_filesManager ? $cv_filesManager->getOriginalName() : $tutor['originalName'],
                'filename' => $cv_filesManager ? $cv_filesManager->getFilename() : $tutor['filename'],
                'url' => $cv_filesManager ? $this->filesManagerService->getUrl($cv_filesManager) : ($this->baseCvPath . '/' . $tutor['filename']),
            ];
            if (!empty($tutor['identificationValue'])) {
                /** @var TypeIdentification|null $identificationType */
                $identificationType = $this->_em->getRepository(TypeIdentification::class)
                    ->createQueryBuilder('ti')
                    ->select('ti')
                    ->join('ti.userIdentifications', 'ui')
                    ->where('ui.identificationValue = :value')
                    ->setParameter('value', $tutor['identificationValue'])
                    ->setMaxResults(1)
                    ->getQuery()
                    ->getOneOrNullResult();
                if ($identificationType) {
                    $tutor['identification'] = [
                        'id' => $identificationType->getId(),
                        'name' => $identificationType->getName(),
                        'description' => $identificationType->getDescription(),
                        'mask' => $identificationType->getMask(),
                    ];
                }
            }
        }

        if ($tutor['idCompany']) {
            $userCompany = $this->_em->getRepository(UserCompany::class)->find($tutor['idCompany']);
            $tutor['company'] = [
                'id' => $userCompany->getId(),
                'name' => $userCompany->getName(),
            ];
        }

        unset($tutor['originalName']);

        return $tutor;
    }

    public function setTutorToAnnouncementGroup($group, array $data, $cv = null)
    {
        if (!($group instanceof AnnouncementGroup)) {
            $announcementGroup = $this->_em->getRepository(AnnouncementGroup::class)->find($group);
        } else {
            $announcementGroup = $group;
        }
        if (!$announcementGroup) {
            throw new \RuntimeException('Group not found');
        }

        $tutorId = $data['tutorId'] ?? null;
        if (empty($tutorId)) {
            throw new \RuntimeException('Tutor Id is required');
        }
        $user = $this->_em->getRepository(User::class)->find($tutorId);
        if (!$user) {
            throw new \RuntimeException('User not found');
        }
        $this->fillAnnouncementTutorData($announcementGroup, $user, $data, $cv);

        return true;
    }

    /**
     * @param int|AnnouncementGroup $group
     * @param UploadedFile|array    $cv
     *
     * @return true
     *
     * @throws \Exception
     */
    public function saveNewTutorUser($group, array $data, $cv = null)
    {
        if (!($group instanceof AnnouncementGroup)) {
            $announcementGroup = $this->_em->getRepository(AnnouncementGroup::class)->find($group);
        } else {
            $announcementGroup = $group;
        }
        if (!$announcementGroup) {
            throw new \RuntimeException('Group not found');
        }

        $email = $data['email'] ?? null;
        $firstName = $data['firstName'] ?? null;
        $lastName = $data['lastName'] ?? null;
        if (empty($firstName) || empty($lastName) || empty($email)) {
            throw new \RuntimeException('email, firstName, lastName required');
        }

        /*   if ($this->_em->getRepository(User::class)->findOneBy(['email' => $email])) {
              throw new \RuntimeException("Wrong email address. Not provided or user already exits");
          } */

        $user = $this->_em->getRepository(User::class)->findOneBy(['email' => $email]);

        if ($user) {
            // verify if user has rol tutor
            if (!\in_array(User::ROLE_TUTOR, $user->getRoles())) {
                $user->setRoles(array_merge($user->getRoles(), [User::ROLE_TUTOR]));
            }
        } else {
            $user = new User();
            $user->setEmail($email)
                ->setFirstName($firstName)
                ->setLastName($lastName)
                ->setPassword(bin2hex(random_bytes(5)))
                ->setOpen(false)
                ->setLocale($data['locale'] ?? 'es')
                ->setLocaleCampus($data['localeCampus' ?? 'es'])
                ->setIsActive(true)
                ->setValidated(true)
                ->setRoles([User::ROLE_TUTOR])
            ;
        }

        $this->fillAnnouncementTutorData($announcementGroup, $user, $data, $cv);

        return true;
    }

    private function fillAnnouncementTutorData(AnnouncementGroup $announcementGroup, User $user, array $data, $cv)
    {
        if ($cv instanceof UploadedFile && !$this->filesManagerService->validateFile($cv, ['application/pdf'])) {
            throw new \RuntimeException(
                json_encode([
                    'cv' => 'FILE_UPLOAD.FILE_TYPE_REQUIRED',
                ])
            );
        }

        $email = $data['email'] ?? null;
        $telephone = $data['telephone'] ?? null;
        $tutoringTime = $data['tutoringTime'] ?? null;
        $identificationValue = $data['identificationValue'] ?? null;
        $identification = $data['identification'] ?? null; // ['id' => value, 'name' => '']
        $company = $data['company'] ?? null;
        $announcement = $announcementGroup->getAnnouncement();

        $hasBonification = $this->_em->getRepository(Announcement::class)->hasBonification($announcement);

        if (empty($email)) {
            throw new \RuntimeException(
                json_encode([
                    'email' => 'REQUIRED_FIELD',
                ])
            );
        }
        if ($hasBonification && (empty($telephone) || empty($identificationValue) || empty($cv))) {
            throw new \RuntimeException(
                json_encode([
                    'telephone' => 'REQUIRED_FIELD',
                    'identification' => 'REQUIRED_FIELD',
                    'cv' => 'REQUIRED_FIELD',
                ])
            );
        }

        $tutor = $announcementGroup->getAnnouncementTutor();
        if (!$tutor) {
            $tutor = new AnnouncementTutor();
            $tutor->setAnnouncement($announcement)
                ->setAnnouncementGroup($announcementGroup);
        }
        // Update user fields fundae
        $userFieldsFundae = $user->getUserFieldsFundae();
        if (!$userFieldsFundae) {
            $userFieldsFundae = new UserFieldsFundae();
            $userFieldsFundae->setUser($user);
            $user->setUserFieldsFundae($userFieldsFundae);
        }
        $tutor->setTutor($user)
            ->setDni($identificationValue)
            ->setEmail($email)
            ->setTelephone($telephone)
            ->setTutoringTime($tutoringTime);

        if ($company) {
            $company = $this->_em->getRepository(UserCompany::class)->find($company['id']);
            $tutor->setUserCompany($company);
            $userFieldsFundae->setUserCompany($company);
        }

        $userFieldsFundae->setEmailWork($tutor->getEmail())
            ->setTelephone($tutor->getTelephone())->setDni($tutor->getDni());

        if ($cv instanceof UploadedFile) {
            $this->uploadTutorNewCv($tutor, $cv, $userFieldsFundae);
        } elseif (\is_array($cv)) {
            $this->uploadCvFromReference($tutor, $cv);
        }

        if (!empty($identificationValue)) {
            if (!empty($identification['id'])) {
                $typeIdentification = $this->_em->getRepository(TypeIdentification::class)
                    ->find($identification['id']);
            } else {
                $typeIdentification = $this->_em->getRepository(TypeIdentification::class)->findOneBy([
                    'main' => true,
                    'active' => true,
                ]);
            }
            if (!$typeIdentification) {
                throw new \RuntimeException('No identification types available. Please define at least one main identification');
            }

            $userIdentification = $this->_em->getRepository(UserIdentification::class)->findOneBy([
                'typeIdentification' => $typeIdentification,
                'user' => $user,
            ]);
            if (!$userIdentification) {
                $userIdentification = new UserIdentification();
                $userIdentification->setTypeIdentification($typeIdentification)
                    ->setUser($user);
            }
            $userIdentification->setIdentificationValue($identificationValue);
            $this->_em->persist($userIdentification);
        }

        $this->_em->persist($user);
        $this->_em->persist($userFieldsFundae);
        $this->_em->persist($tutor);
        $this->_em->flush();
    }

    /**
     * @return void
     *
     * @throws \Exception
     */
    public function uploadTutorNewCv(AnnouncementTutor $tutor, UploadedFile $cv, ?UserFieldsFundae $userFieldsFundae = null)
    {
        $previousCv = $tutor->getCvFilesManager();
        if ($previousCv) {
            $this->_em->remove($previousCv);
            $tutor->setCvFilesManager(null);
        }
        $filesManager = $this->filesManagerService->upload(
            $cv,
            'announcement' . DIRECTORY_SEPARATOR . 'tutorCV',
            User::ROLE_TUTOR
        );
        $tutor->setCvFilesManager($filesManager);

        if ($userFieldsFundae) {
            $cloneCv = $this->filesManagerService->clone($filesManager, 'users/cv');
            $userFieldsFundae->setCvFilesManager($cloneCv);
        }
    }

    /**
     * @param array $reference ['source' => 'value', 'filename' => "", ]
     *
     * @return false|FilesManager
     */
    public function uploadCvFromReference(AnnouncementTutor $tutor, array $reference = []): bool
    {
        if (empty($reference)) {
            return false;
        }
        $filesManagerResult = $this->filesManagerService->generateFilesManagerFromSource($reference, 'announcement' . DIRECTORY_SEPARATOR . 'tutorCV');
        if (false === $filesManagerResult) {
            return false;
        }
        $tutor->setCvFilesManager($filesManagerResult);

        return $filesManagerResult;
    }

    public function findAnnouncementTutor(Announcement $announcement, User $user)
    {
        return $this->createQueryBuilder('at')
            ->select('tutor')
            ->join('at.tutor', 'tutor')
            ->where('at.announcement =:announcement')
            ->andWhere('at.tutor =:tutor')
            ->setParameter('announcement', $announcement)
            ->setParameter('tutor', $user)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function findAnnouncementTutors(Announcement $announcement)
    {
        return $this->createQueryBuilder('at')
            ->select('t.id', 'CONCAT(t.firstName, \' \', t.lastName) as name', 't.email', 't.avatar', 'e.pdfCv as filename')
            ->join('at.tutor', 't')
            ->leftJoin('t.extra', 'e')
            ->where('at.announcement = :announcement')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();
    }

    public function isTutorSharedWithAnnouncement(User $tutor, Announcement $announcement): bool
    {
        $result = $this->createQueryBuilder('at')
            ->select('COUNT(at.id)')
            ->where('at.tutor = :tutor')
            ->andWhere('at.announcement = :announcement')
            ->setParameter('tutor', $tutor)
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getSingleScalarResult();

        return $result > 0;
    }
}
