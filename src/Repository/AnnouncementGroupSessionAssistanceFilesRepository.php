<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementGroupSessionAssistanceFiles;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementGroupSessionAssistanceFiles>
 *
 * @method AnnouncementGroupSessionAssistanceFiles|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementGroupSessionAssistanceFiles|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementGroupSessionAssistanceFiles[]    findAll()
 * @method AnnouncementGroupSessionAssistanceFiles[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementGroupSessionAssistanceFilesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementGroupSessionAssistanceFiles::class);
    }

    public function add(AnnouncementGroupSessionAssistanceFiles $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementGroupSessionAssistanceFiles $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
