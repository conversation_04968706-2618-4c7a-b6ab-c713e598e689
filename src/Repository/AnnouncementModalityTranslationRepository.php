<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementModalityTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementModalityTranslation>
 *
 * @method AnnouncementModalityTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementModalityTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementModalityTranslation[]    findAll()
 * @method AnnouncementModalityTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementModalityTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementModalityTranslation::class);
    }

    public function add(AnnouncementModalityTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementModalityTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
