<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ClassroomVirtualResult1;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ClassroomVirtualResult1>
 *
 * @method ClassroomVirtualResult1|null find($id, $lockMode = null, $lockVersion = null)
 * @method ClassroomVirtualResult1|null findOneBy(array $criteria, array $orderBy = null)
 * @method ClassroomVirtualResult1[]    findAll()
 * @method ClassroomVirtualResult1[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ClassroomVirtualResult1Repository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ClassroomVirtualResult1::class);
    }

    public function add(ClassroomVirtualResult1 $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ClassroomVirtualResult1 $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
