<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementObservationDocument;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementObservationDocument>
 *
 * @method AnnouncementObservationDocument|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementObservationDocument|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementObservationDocument[]    findAll()
 * @method AnnouncementObservationDocument[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementObservationDocumentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementObservationDocument::class);
    }

    public function add(AnnouncementObservationDocument $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementObservationDocument $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
