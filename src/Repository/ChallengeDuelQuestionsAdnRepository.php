<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChallengeDuelQuestionsAdn;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method ChallengeDuelQuestionsAdn|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChallengeDuelQuestionsAdn|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChallengeDuelQuestionsAdn[]    findAll()
 * @method ChallengeDuelQuestionsAdn[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChallengeDuelQuestionsAdnRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ChallengeDuelQuestionsAdn::class);
    }
}
