<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\TypeCourseAnnouncementStepCreationTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TypeCourseAnnouncementStepCreationTranslation>
 *
 * @method TypeCourseAnnouncementStepCreationTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method TypeCourseAnnouncementStepCreationTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method TypeCourseAnnouncementStepCreationTranslation[]    findAll()
 * @method TypeCourseAnnouncementStepCreationTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TypeCourseAnnouncementStepCreationTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TypeCourseAnnouncementStepCreationTranslation::class);
    }

    public function add(TypeCourseAnnouncementStepCreationTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(TypeCourseAnnouncementStepCreationTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
