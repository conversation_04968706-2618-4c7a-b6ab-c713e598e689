<?php

namespace App\Repository;

use App\Entity\TranslationsAdminTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TranslationsAdminTranslation>
 *
 * @method TranslationsAdminTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method TranslationsAdminTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method TranslationsAdminTranslation[]    findAll()
 * @method TranslationsAdminTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TranslationsAdminTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TranslationsAdminTranslation::class);
    }

    public function add(TranslationsAdminTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(TranslationsAdminTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return TranslationsAdminTranslation[] Returns an array of TranslationsAdminTranslation objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('t')
//            ->andWhere('t.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('t.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?TranslationsAdminTranslation
//    {
//        return $this->createQueryBuilder('t')
//            ->andWhere('t.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}
