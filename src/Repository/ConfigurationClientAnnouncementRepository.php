<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ConfigurationClientAnnouncement;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ConfigurationClientAnnouncement>
 *
 * @method ConfigurationClientAnnouncement|null find($id, $lockMode = null, $lockVersion = null)
 * @method ConfigurationClientAnnouncement|null findOneBy(array $criteria, array $orderBy = null)
 * @method ConfigurationClientAnnouncement[]    findAll()
 * @method ConfigurationClientAnnouncement[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ConfigurationClientAnnouncementRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ConfigurationClientAnnouncement::class);
    }

    public function add(ConfigurationClientAnnouncement $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ConfigurationClientAnnouncement $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
