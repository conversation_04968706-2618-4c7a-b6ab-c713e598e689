<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\QuestionsAnnouncement;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<QuestionsAnnouncement>
 *
 * @method QuestionsAnnouncement|null find($id, $lockMode = null, $lockVersion = null)
 * @method QuestionsAnnouncement|null findOneBy(array $criteria, array $orderBy = null)
 * @method QuestionsAnnouncement[]    findAll()
 * @method QuestionsAnnouncement[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class QuestionsAnnouncementRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, QuestionsAnnouncement::class);
    }

    public function add(QuestionsAnnouncement $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(QuestionsAnnouncement $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
