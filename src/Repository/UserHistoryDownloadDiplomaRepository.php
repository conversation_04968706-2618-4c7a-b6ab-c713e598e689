<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\UserHistoryDownloadDiploma;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<UserHistoryDownloadDiploma>
 *
 * @method UserHistoryDownloadDiploma|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserHistoryDownloadDiploma|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserHistoryDownloadDiploma[]    findAll()
 * @method UserHistoryDownloadDiploma[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserHistoryDownloadDiplomaRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserHistoryDownloadDiploma::class);
    }

    public function add(UserHistoryDownloadDiploma $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(UserHistoryDownloadDiploma $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
