<?php

namespace App\Repository;

use App\Entity\SectionDefaultFront;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SectionDefaultFront>
 *
 * @method SectionDefaultFront|null find($id, $lockMode = null, $lockVersion = null)
 * @method SectionDefaultFront|null findOneBy(array $criteria, array $orderBy = null)
 * @method SectionDefaultFront[]    findAll()
 * @method SectionDefaultFront[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SectionDefaultFrontRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SectionDefaultFront::class);
    }

    public function add(SectionDefaultFront $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(SectionDefaultFront $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }


    public function getSectionTranslatedList(?string $locale = null, $idSection = null)  {
        if ($idSection === null) {
            return null;
        }
    
        $sectionDefaultFront = $this->findOneBy(['idSection' => $idSection]);
    
        if (!$sectionDefaultFront) {
            return null;
        }
    
        $name = $sectionDefaultFront->getName();
        $description = $sectionDefaultFront->getDescription();
    
        if (!empty($locale)) {
            /** @var TypeCourseTranslation $translation */
            $translation = $sectionDefaultFront->translate($locale);
            $name = $translation->getName() ?: $name;
            $description = $translation->getDescription() ?: $description;
        }
    
        return [
            'id' => $sectionDefaultFront->getId(),
            'name' => $name,
            'description' => $description,
            'idSection' => $sectionDefaultFront->getIdSection()
        ];
      
    }

    public function getStateSectionDefaultFront($idSection = null) {
        if ($idSection === null) {
            return null;
        }
    
        $sectionDefaultFront = $this->findOneBy(['idSection' => $idSection]);
    
        if (!$sectionDefaultFront) {
            return null;
        }
    
        return $sectionDefaultFront->isIsActive();
    }
}
