<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\MaterialDownloadHistory;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<MaterialDownloadHistory>
 *
 * @method MaterialDownloadHistory|null find($id, $lockMode = null, $lockVersion = null)
 * @method MaterialDownloadHistory|null findOneBy(array $criteria, array $orderBy = null)
 * @method MaterialDownloadHistory[]    findAll()
 * @method MaterialDownloadHistory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MaterialDownloadHistoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MaterialDownloadHistory::class);
    }

    public function add(MaterialDownloadHistory $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(MaterialDownloadHistory $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
