<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementObservation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementObservation>
 *
 * @method AnnouncementObservation|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementObservation|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementObservation[]    findAll()
 * @method AnnouncementObservation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementObservationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementObservation::class);
    }

    public function add(AnnouncementObservation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementObservation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
