openapi: 3.0.0
info:
  title: Easylearning Common API v2
  description: Common API endpoints for Easylearning
  version: 2.0.0

servers:
  - url: /api/v2/common
    description: Common API endpoints for v2

security:
  - bearerAuth: []

paths:
  /time-zones:
    get:
      tags:
        - Time Zones
      summary: Get available time zones
      description: Returns a list of available time zones in the system, indicating which one is the default
      operationId: getTimeZones
      responses:
        '200':
          description: List of available time zones
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        value:
                          type: string
                          example: "Europe/Madrid"
                        default:
                          type: boolean
                          example: true
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

  /payments/key:
    get:
      tags:
        - Payments
      summary: Get payment public key
      description: Returns the public key configured for payment processing (e.g., Stripe public key). This endpoint is used by the frontend to initialize payment flows.
      operationId: getPaymentPublicKey
      responses:
        '200':
          description: Payment public key retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      publicKey:
                        type: string
                        description: The public key for payment processing
                        example: "pk_test_51HPQjtFQe123456789abcdef"
                    required:
                      - publicKey
                required:
                  - data
        '401':
          description: Unauthorized - Authentication required
        '404':
          description: Payment public key not found - No public key configured in settings
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: integer
                    example: 1
                  message:
                    type: string
                    example: "Payment public key not found"
        '500':
          description: Internal Server Error

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
