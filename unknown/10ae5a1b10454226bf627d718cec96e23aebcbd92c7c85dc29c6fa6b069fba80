<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementNotification;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementNotification>
 *
 * @method AnnouncementNotification|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementNotification|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementNotification[]    findAll()
 * @method AnnouncementNotification[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementNotificationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementNotification::class);
    }

    public function add(AnnouncementNotification $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementNotification $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
