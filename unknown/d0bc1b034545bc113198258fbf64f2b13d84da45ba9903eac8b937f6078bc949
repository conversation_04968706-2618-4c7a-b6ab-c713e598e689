<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ExtraDataTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ExtraDataTranslation>
 *
 * @method ExtraDataTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method ExtraDataTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method ExtraDataTranslation[]    findAll()
 * @method ExtraDataTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ExtraDataTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ExtraDataTranslation::class);
    }

    public function add(ExtraDataTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ExtraDataTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
