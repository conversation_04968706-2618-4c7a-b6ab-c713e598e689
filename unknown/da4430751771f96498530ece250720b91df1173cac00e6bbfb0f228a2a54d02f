<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\TypeCourseTranslation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TypeCourseTranslation>
 *
 * @method TypeCourseTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method TypeCourseTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method TypeCourseTranslation[]    findAll()
 * @method TypeCourseTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TypeCourseTranslationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TypeCourseTranslation::class);
    }

    public function add(TypeCourseTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(TypeCourseTranslation $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
