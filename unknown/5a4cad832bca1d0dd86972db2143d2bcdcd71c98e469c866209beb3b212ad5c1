<?php

declare(strict_types=1);

namespace App\Service\General;

use Symfony\Component\HttpFoundation\RequestStack;

class IpService
{
    private RequestStack $requestStack;

    public function __construct(RequestStack $requestStack)
    {
        $this->requestStack = $requestStack;
    }

    public function getClientIp(): ?string
    {
        $request = $this->requestStack->getCurrentRequest();
        $ip = $request ? $request->getClientIp() : null;
        
        if ($_SERVER['HTTP_CF_CONNECTING_IP'] ?? false) {
            $ip = $_SERVER['HTTP_CF_CONNECTING_IP'];
        }
    
        return $ip;
    }
}
