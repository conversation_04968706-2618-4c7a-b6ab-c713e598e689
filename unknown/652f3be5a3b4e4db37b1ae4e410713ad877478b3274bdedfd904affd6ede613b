<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250514170000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Creates the cron_job_timeout table to store timeouts for cron commands';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE cron_job_timeout (
                id INT AUTO_INCREMENT NOT NULL,
                cron_job_id INT DEFAULT NULL,
                created_by_id INT DEFAULT NULL,
                updated_by_id INT DEFAULT NULL,
                deleted_by_id INT DEFAULT NULL,
                command VARCHAR(255) NOT NULL,
                timeout INT NOT NULL,
                created_at DATETIME DEFAULT NULL,
                updated_at DATETIME DEFAULT NULL,
                deleted_at DATETIME DEFAULT NULL,
                UNIQUE INDEX UNIQ_D46C2D8EA76ED395 (command),
                INDEX IDX_D46C2D8EBE04EA9 (cron_job_id),
                INDEX IDX_D46C2D8EB03A8386 (created_by_id),
                INDEX IDX_D46C2D8E896DBBDE (updated_by_id),
                INDEX IDX_D46C2D8EC76F1F52 (deleted_by_id),
                PRIMARY KEY(id)
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cron_job_timeout
            ADD CONSTRAINT FK_D46C2D8EBE04EA9 FOREIGN KEY (cron_job_id) REFERENCES cron_job (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cron_job_timeout
            ADD CONSTRAINT FK_D46C2D8EB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cron_job_timeout
            ADD CONSTRAINT FK_D46C2D8E896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE cron_job_timeout
            ADD CONSTRAINT FK_D46C2D8EC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE cron_job_timeout DROP FOREIGN KEY FK_D46C2D8EBE04EA9');
        $this->addSql('ALTER TABLE cron_job_timeout DROP FOREIGN KEY FK_D46C2D8EB03A8386');
        $this->addSql('ALTER TABLE cron_job_timeout DROP FOREIGN KEY FK_D46C2D8E896DBBDE');
        $this->addSql('ALTER TABLE cron_job_timeout DROP FOREIGN KEY FK_D46C2D8EC76F1F52');
        $this->addSql('DROP TABLE cron_job_timeout');
    }
}
