<?php

declare(strict_types=1);

namespace App\Service\Announcement\Stats;

use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementUser;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\KernelInterface;

class AnnouncementStatsService
{
    private EntityManagerInterface $em;

    public function __construct(EntityManagerInterface $em, KernelInterface $kernel, LoggerInterface $logger)
    {
        $this->em = $em;
    }

    public function getAnnouncementUserValuedCourse($announcement, $user): ?\DateTimeInterface
    {
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->getUserByAnnouncement($announcement, $user);

        return $announcementUser->getValuedCourseAt();
    }

    public function getTotalStartedAnnoucementSessions($announcement): int
    {
        return $this->em->getRepository(AnnouncementGroupSession::class)->getTotalStartedSessionGroup($announcement);
    }

    public function getTotalFinishedAnnoucementSessions($announcement): int
    {
        return $this->em->getRepository(AnnouncementGroupSession::class)->getTotalFinishedSessionGroup($announcement);
    }

    public function getTotalAnnoucementSessions($announcement, bool $isUserView = false): int
    {
        return $this->em->getRepository(AnnouncementGroupSession::class)->getTotalNumberSessionGroup($announcement, $isUserView);
    }
}
