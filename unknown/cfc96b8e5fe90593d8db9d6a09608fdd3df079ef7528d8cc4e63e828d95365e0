<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\AnnouncementTemporalization;
use App\Entity\Chapter;
use App\Entity\Course;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AnnouncementTemporalization>
 *
 * @method AnnouncementTemporalization|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementTemporalization|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementTemporalization[]    findAll()
 * @method AnnouncementTemporalization[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementTemporalizationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AnnouncementTemporalization::class);
    }

    public function add(AnnouncementTemporalization $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementTemporalization $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getCourseChapters(Course $course): array
    {
        return $this->_em->getRepository(Chapter::class)->createQueryBuilder('c')
            ->select('c.id', 't.name as typeName', 'c.title', 'c.description')
            ->addSelect('co.id as courseId')
            ->addSelect('t.type')
            ->join('c.type', 't')
            ->join('c.course', 'co')
            ->where('c.course = :course')
            ->setParameter('course', $course)
            ->getQuery()
            ->getResult();
    }
}
