workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TITLE =~ /^(\[Draft\]|\(Draft\)|Draft:)/
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never

stages:
  - test

# Templates
.php_cli:
  image: registry.gitlab.com/managementdrives/cloud-devops/base-images/public/base-image-php:easylearning-8.3-devel.2
  before_script:
    - composer install -q --no-scripts
  cache:
    key:
      files:
        - composer.json
        - symfony.lock
    paths:
      - vendor/
      - composer.lock


### Test stage
unit-test-php:
  stage: test
  extends: .php_cli
  variables:
    MYSQL_DATABASE: easylearning_test
    MYSQL_ROOT_PASSWORD: docker
    DATABASE_URL: mysql://root:docker@database:3306/easylearning_test
  services:
    - name: mariadb:10.5
      alias: database
  script:
    - php -d memory_limit=512M bin/console doctrine:migrations:migrate --all-or-nothing # Make sure all migrations are working as expected
    - php -d memory_limit=512M bin/console catalogs:update --new --no-interaction # Make sure all catalogs are working for a new client
    - php vendor/bin/phpunit # Make sure all tests are working
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
