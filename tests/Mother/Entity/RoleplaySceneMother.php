<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\RoleplayScene;
use App\Entity\RoleplaySequence;

class RoleplaySceneMother
{
    public static function create(
        RoleplaySequence $roleplaySequence,
        string $code,
        string $type,
        ?string $attachedTag,
        ?string $attached,
        ?string $background,
        ?string $avatar,
        ?string $video,
        ?string $standBy,
        ?string $statement,
        int $order,
        array $answers,
    ): RoleplayScene {
        $scene = new RoleplayScene();
        $scene->setSequence($roleplaySequence)
            ->setCode($code)
            ->setType($type)
            ->setAttachedTag($attachedTag)
            ->setAttached($attached)
            ->setBackground($background)
            ->setAvatar($avatar)
            ->setVideo($video)
            ->setStandBy($standBy)
            ->setStatement($statement)
            ->setOrder($order)
            ->setAnswers($answers);

        return $scene;
    }
}
