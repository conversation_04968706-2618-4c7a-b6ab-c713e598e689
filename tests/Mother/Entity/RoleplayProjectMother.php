<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\RoleplayProject;

class RoleplayProjectMother
{
    public const string DEFAULT_TITLE = 'Roleplay Project Test';

    public static function create(
        ?string $title = null,
        ?array $sequences = null,
        ?array $beginnings = null,
        ?array $endings = null,
    ): RoleplayProject {
        $project = new RoleplayProject();

        $project->setTitle($title ?? self::DEFAULT_TITLE);

        // Clear default collections if custom ones are provided
        if (null !== $sequences) {
            $project->initializeSequences();
            foreach ($sequences as $sequence) {
                $project->addSequence($sequence);
            }
        }

        if (null !== $beginnings) {
            $project->initializeBeginnings();
            foreach ($beginnings as $beginning) {
                $project->addBeginning($beginning);
            }
        }

        if (null !== $endings) {
            $project->initializeEndings();
            foreach ($endings as $ending) {
                $project->addEnding($ending);
            }
        }

        return $project;
    }
}
