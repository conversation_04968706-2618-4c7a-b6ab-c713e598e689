<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\RoleplayProject;
use App\Entity\RoleplaySequence;

class RoleplaySequenceMother
{
    public static function create(
        RoleplayProject $roleplayProject,
        string $color,
        int $order,
    ): RoleplaySequence {
        $sequence = new RoleplaySequence();
        $sequence->setProject($roleplayProject)
            ->setColor($color)
            ->setOrder($order);

        return $sequence;
    }
}
