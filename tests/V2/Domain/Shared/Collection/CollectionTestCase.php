<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Shared\Collection;

use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use PHPUnit\Framework\TestCase;

/**
 * @template TObject
 * @template TCollection of Collection
 */
abstract class CollectionTestCase extends TestCase
{
    /**
     * @param TObject[] $items
     *
     * @return TCollection
     */
    abstract protected function createCollection(array $items): Collection;

    abstract protected function getExpectedType(): string;

    /**
     * @return TObject
     */
    abstract protected function getItem(): object;

    public function testOnlyItemsOfExpectedTypeCanBeAdded(): void
    {
        $newItem = new \stdClass();
        $className = \get_class($newItem);

        $this->expectExceptionObject(
            CollectionException::invalidItemType($this->getExpectedType(), $className)
        );

        $this->createCollection([$newItem]);
    }

    /**
     * @throws CollectionException
     */
    public function testOnlyItemsOfExpectedTypeCanBeAppend(): void
    {
        $newItem = new \stdClass();
        $className = \get_class($newItem);

        $this->expectExceptionObject(
            CollectionException::invalidItemType($this->getExpectedType(), $className)
        );

        $collection = $this->createCollection([]);
        $collection->append($newItem);
    }

    /**
     * @throws CollectionException
     */
    public function testOnlyReplaceItemsOfExpectedType(): void
    {
        $newItem = new \stdClass();
        $className = \get_class($newItem);

        $this->expectExceptionObject(
            CollectionException::invalidItemType($this->getExpectedType(), $className)
        );

        $collection = $this->createCollection([$this->getItem()]);
        $collection->replace([$newItem]);
    }
}
