<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Assignment;

use App\Tests\V2\Mother\Assignment\AssignmentMother;
use App\Tests\V2\Mother\Shared\Id\IdMother;
use App\Tests\V2\Mother\Shared\Resource\ResourceMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Assignment\AssignmentCollection;
use App\V2\Domain\Assignment\AssignmentCriteria;
use App\V2\Domain\Assignment\AssignmentRepository;
use App\V2\Domain\Assignment\Exception\AssignmentNotFoundException;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\InvalidIdException;
use App\V2\Domain\Shared\Resource\ResourceType;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidCollection;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class AssignmentRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): AssignmentRepository;

    /**
     * @throws AssignmentNotFoundException
     * @throws InfrastructureException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws InvalidIdException
     */
    public function testCreate(): void
    {
        $assignment1 = AssignmentMother::create();
        $assignment2 = AssignmentMother::create();
        $assignment3 = AssignmentMother::create();

        $repository = $this->getRepository();

        $repository->create($assignment1);
        $repository->create($assignment2);
        $repository->create($assignment3);

        $foundAssignment = $repository->findOneBy(
            AssignmentCriteria::createById($assignment2->getId())
        );

        $this->assertEquals($assignment2, $foundAssignment);
        $this->assertNotSame($assignment2, $foundAssignment);
    }

    /**
     * @throws AssignmentNotFoundException
     * @throws InfrastructureException
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws InvalidIdException
     */
    public function testFindOneBy(): void
    {
        $assignment1 = AssignmentMother::create();
        $assignment2 = AssignmentMother::create();
        $assignment3 = AssignmentMother::create();

        $repository = $this->getRepository();

        $repository->create($assignment1);
        $repository->create($assignment2);
        $repository->create($assignment3);

        $foundAssignment = $repository->findOneBy(
            AssignmentCriteria::createById($assignment2->getId())
        );

        $this->assertEquals($assignment2, $foundAssignment);

        $this->expectException(AssignmentNotFoundException::class);
        $repository->findOneBy(
            AssignmentCriteria::createById(UuidMother::create())
        );
    }

    /**
     * @throws InfrastructureException
     * @throws InvalidIdException
     * @throws InvalidUuidException
     */
    #[DataProvider('findByDataProvider')]
    public function testFindBy(
        AssignmentCollection $input,
        AssignmentCriteria $criteria,
        int $expectedCount,
        array $expectedResults
    ): void {
        $repository = $this->getRepository();
        foreach ($input->all() as $assignment) {
            $repository->create($assignment);
        }

        $result = $repository->findBy(
            AssignmentCriteria::createEmpty()
        );
        $this->assertCount(
            \count($input->all()),
            $result->all()
        );

        $result = $repository->findBy($criteria);
        $this->assertCount($expectedCount, $result);

        // Check that all expected results are in the result
        foreach ($expectedResults as $expected) {
            $found = false;
            foreach ($result->all() as $actual) {
                if ($expected->getId()->equals($actual->getId())) {
                    $found = true;
                    break;
                }
            }
            $this->assertTrue($found, "Expected assignment with ID {$expected->getId()->value()} not found in results");
        }
    }

    /**
     * @throws CollectionException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws InvalidIdException
     */
    public static function findByDataProvider(): \Generator
    {
        $userId1 = IdMother::create(id: 1);
        $userId2 = IdMother::create(id: 2);
        $userId3 = IdMother::create(id: 3);

        $courseResource1 = ResourceMother::createCourse();
        $courseResource2 = ResourceMother::createCourse();

        $assignment1 = AssignmentMother::create(
            userId: $userId1,
            resource: $courseResource1
        );

        $assignment2 = AssignmentMother::create(
            userId: $userId2,
            resource: $courseResource1
        );

        $assignment3 = AssignmentMother::create(
            userId: $userId3,
            resource: $courseResource2
        );

        yield '3 assignments get all' => [
            'input' => new AssignmentCollection([$assignment1, $assignment2, $assignment3]),
            'criteria' => AssignmentCriteria::createEmpty(),
            'expectedCount' => 3,
            'expectedResults' => [$assignment1, $assignment2, $assignment3],
        ];

        yield '3 assignments assignment by id' => [
            'input' => new AssignmentCollection([$assignment1, $assignment2, $assignment3]),
            'criteria' => AssignmentCriteria::createById($assignment2->getId()),
            'expectedCount' => 1,
            'expectedResults' => [$assignment2],
        ];

        // Test filtering by userId
        yield '3 assignments filter by userId' => [
            'input' => new AssignmentCollection([$assignment1, $assignment2, $assignment3]),
            'criteria' => AssignmentCriteria::createEmpty()->filterByUserId($userId1),
            'expectedCount' => 1,
            'expectedResults' => [$assignment1],
        ];

        // Test filtering by resource
        yield '3 assignments filter by resource' => [
            'input' => new AssignmentCollection([$assignment1, $assignment2, $assignment3]),
            'criteria' => AssignmentCriteria::createEmpty()->filterByResource($courseResource1),
            'expectedCount' => 2,
            'expectedResults' => [$assignment1, $assignment2],
        ];

        // Test filtering by resourceType
        yield '3 assignments filter by resourceType' => [
            'input' => new AssignmentCollection([$assignment1, $assignment2, $assignment3]),
            'criteria' => AssignmentCriteria::createEmpty()->filterByResourceType(ResourceType::Course),
            'expectedCount' => 3,
            'expectedResults' => [$assignment1, $assignment2, $assignment3],
        ];

        // Test filtering by multiple IDs
        yield '3 assignments filter by multiple IDs' => [
            'input' => new AssignmentCollection([$assignment1, $assignment2, $assignment3]),
            'criteria' => AssignmentCriteria::createByIds(
                new UuidCollection([$assignment1->getId(), $assignment3->getId()])
            ),
            'expectedCount' => 2,
            'expectedResults' => [$assignment1, $assignment3],
        ];

        // Test combined filtering: userId and resource
        yield '3 assignments filter by userId and resource' => [
            'input' => new AssignmentCollection([$assignment1, $assignment2, $assignment3]),
            'criteria' => AssignmentCriteria::createEmpty()
                ->filterByUserId($userId2)
                ->filterByResource($courseResource1),
            'expectedCount' => 1,
            'expectedResults' => [$assignment2],
        ];

        // Test combined filtering: userId and resourceType
        yield '3 assignments filter by userId and resourceType' => [
            'input' => new AssignmentCollection([$assignment1, $assignment2, $assignment3]),
            'criteria' => AssignmentCriteria::createEmpty()
                ->filterByUserId($userId1)
                ->filterByResourceType(ResourceType::Course),
            'expectedCount' => 1,
            'expectedResults' => [$assignment1],
        ];

        // Test combined filtering: resource and resourceType
        yield '3 assignments filter by resource and resourceType' => [
            'input' => new AssignmentCollection([$assignment1, $assignment2, $assignment3]),
            'criteria' => AssignmentCriteria::createEmpty()
                ->filterByResource($courseResource1)
                ->filterByResourceType(ResourceType::Course),
            'expectedCount' => 2,
            'expectedResults' => [$assignment1, $assignment2],
        ];

        // Test triple combined filtering: userId, resource and resourceType
        yield '3 assignments filter by userId, resource and resourceType' => [
            'input' => new AssignmentCollection([$assignment1, $assignment2, $assignment3]),
            'criteria' => AssignmentCriteria::createEmpty()
                ->filterByUserId($userId2)
                ->filterByResource($courseResource1)
                ->filterByResourceType(ResourceType::Course),
            'expectedCount' => 1,
            'expectedResults' => [$assignment2],
        ];

        // Test no results found
        yield '3 assignments filter by non-existent userId' => [
            'input' => new AssignmentCollection([$assignment1, $assignment2, $assignment3]),
            'criteria' => AssignmentCriteria::createEmpty()->filterByUserId(IdMother::create(id: 999)),
            'expectedCount' => 0,
            'expectedResults' => [],
        ];

        // Test empty IDs collection should throw exception (handled by criteria validation)
        yield '3 assignments filter by single non-existent ID' => [
            'input' => new AssignmentCollection([$assignment1, $assignment2, $assignment3]),
            'criteria' => AssignmentCriteria::createById(UuidMother::create()),
            'expectedCount' => 0,
            'expectedResults' => [],
        ];
    }

    /**
     * @throws AssignmentNotFoundException
     * @throws InfrastructureException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws InvalidIdException
     */
    public function testDelete(): void
    {
        $assignment1 = AssignmentMother::create();
        $assignment2 = AssignmentMother::create();
        $assignment3 = AssignmentMother::create();

        $repository = $this->getRepository();

        $repository->create($assignment1);
        $repository->create($assignment2);
        $repository->create($assignment3);

        $foundAssignment = $repository->findOneBy(
            AssignmentCriteria::createById($assignment2->getId())
        );

        $this->assertEquals($assignment2, $foundAssignment);

        $repository->delete($assignment2);

        $this->expectException(AssignmentNotFoundException::class);
        $repository->findOneBy(
            AssignmentCriteria::createById($assignment2->getId())
        );
    }

    /**
     * @throws InfrastructureException
     * @throws InvalidIdException
     * @throws InvalidUuidException
     */
    public function testDeleteThrowsAssignmentNotFoundException(): void
    {
        $repository = $this->getRepository();

        $assignment1 = AssignmentMother::create();
        $assignment2 = AssignmentMother::create();

        $repository->create($assignment1);

        $this->expectException(AssignmentNotFoundException::class);
        $repository->delete($assignment2);
    }

    /**
     * @throws InfrastructureException
     * @throws InvalidUuidException
     * @throws InvalidIdException
     */
    public function testCreateWithDuplicateIdThrowsException(): void
    {
        $assignment1 = AssignmentMother::create();
        $assignment2 = AssignmentMother::create(
            id: $assignment1->getId(), // Same ID as assignment1
            userId: IdMother::create(id: 999),
            resource: ResourceMother::createCourse()
        );

        $repository = $this->getRepository();
        $repository->create($assignment1);

        $this->expectException(InfrastructureException::class);
        $repository->create($assignment2);
    }
}
