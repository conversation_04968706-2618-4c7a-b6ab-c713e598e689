<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\User;

use App\Entity\User;
use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\User\LegacyUserCollection;

class LegacyUserCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new LegacyUserCollection($items);
    }

    protected function getExpectedType(): string
    {
        return User::class;
    }

    protected function getItem(): object
    {
        return new User();
    }
}
