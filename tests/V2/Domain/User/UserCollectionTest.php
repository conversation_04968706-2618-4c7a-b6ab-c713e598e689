<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\User;

use App\Tests\V2\Domain\Shared\Entity\EntityWithIdCollectionTestCase;
use App\Tests\V2\Mother\User\UserMother;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\User\User;
use App\V2\Domain\User\UserCollection;

/**
 * @extends EntityWithIdCollectionTestCase<User, UserCollection>
 */
class UserCollectionTest extends EntityWithIdCollectionTestCase
{
    protected function getExpectedType(): string
    {
        return User::class;
    }

    protected function getItem(): object
    {
        return UserMother::create();
    }

    /**
     * @param User[] $items
     *
     * @return UserCollection
     *
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new UserCollection($items);
    }
}
