<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\User;

use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\User\LegacyUserCollection;
use App\V2\Domain\User\PaginatedUserCollection;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PaginatedUserCollectionTest extends TestCase
{
    /**
     * Test that the constructor sets the collection and total items correctly.
     *
     * @throws CollectionException
     */
    public function testConstructor(): void
    {
        // Arrange
        $collection = new LegacyUserCollection([]);
        $totalItems = 100;

        // Act
        $paginatedCollection = new PaginatedUserCollection($collection, $totalItems);

        // Assert
        $this->assertSame($collection, $paginatedCollection->getCollection());
        $this->assertSame($totalItems, $paginatedCollection->getTotalItems());
    }

    /**
     * Test that getCollection returns the correct collection.
     *
     * @throws CollectionException
     */
    public function testGetCollection(): void
    {
        // Arrange
        $collection = new LegacyUserCollection([]);
        $paginatedCollection = new PaginatedUserCollection($collection, 100);

        // Act
        $result = $paginatedCollection->getCollection();

        // Assert
        $this->assertSame($collection, $result);
    }

    /**
     * Test that getTotalItems returns the correct total items.
     *
     * @throws CollectionException
     */
    public function testGetTotalItems(): void
    {
        // Arrange
        $totalItems = 100;
        $paginatedCollection = new PaginatedUserCollection(new LegacyUserCollection([]), $totalItems);

        // Act
        $result = $paginatedCollection->getTotalItems();

        // Assert
        $this->assertSame($totalItems, $result);
    }

    /**
     * Test that the constructor throws an exception if the collection is not a UserCollection.
     *
     * @throws Exception
     */
    public function testConstructorThrowsExceptionForInvalidCollection(): void
    {
        // Arrange
        $invalidCollection = $this->createMock(Collection::class);

        // Assert
        $this->expectException(\TypeError::class);

        // Act
        new PaginatedUserCollection($invalidCollection, 100);
    }
}
