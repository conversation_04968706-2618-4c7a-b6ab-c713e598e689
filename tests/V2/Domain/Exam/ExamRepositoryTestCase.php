<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Exam;

use App\Tests\V2\Mother\Exam\ExamMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Exam\ExamCollection;
use App\V2\Domain\Exam\ExamCriteria;
use App\V2\Domain\Exam\ExamRepository;
use App\V2\Domain\Exam\Exception\ExamNotFoundException;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidCollection;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class ExamRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): ExamRepository;

    /**
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws ExamNotFoundException
     * @throws CriteriaException
     */
    public function testPutAndFindOneBy(): void
    {
        $examTitle = 'Exam 1';
        $exam = ExamMother::create(
            title: $examTitle
        );
        $repository = $this->getRepository();

        $repository->put($exam);

        $found = $repository->findOneBy(
            ExamCriteria::createEmpty()->filterById($exam->getId())
        );

        $this->assertEquals($exam, $found);

        $found = $repository->findBy(
            ExamCriteria::createEmpty()->filterBySearch($examTitle)
        );

        $this->assertEquals($exam, $found->first());
    }

    /**
     * @throws InfrastructureException
     * @throws InvalidUuidException
     * @throws ExamNotFoundException
     * @throws CriteriaException
     */
    public function testDelete(): void
    {
        $exam2 = ExamMother::create();
        $repository = $this->getRepository();

        $repository->put($exam2);

        $this->assertEquals($exam2, $repository->findOneBy(
            ExamCriteria::createEmpty()->filterById($exam2->getId())
        ));

        $repository->delete($exam2);

        $this->expectException(ExamNotFoundException::class);
        $repository->findOneBy(
            ExamCriteria::createEmpty()->filterById($exam2->getId())
        );
    }

    /**
     * @throws InfrastructureException
     * @throws InvalidUuidException
     * @throws CriteriaException
     * @throws ExamNotFoundException
     */
    public function testFindOneBy(): void
    {
        $exam1 = ExamMother::create();
        $repository = $this->getRepository();

        $repository->put($exam1);

        $found = $repository->findOneBy(
            ExamCriteria::createEmpty()->filterById($exam1->getId())
        );

        $this->assertEquals($exam1, $found);
    }

    /**
     * @throws InfrastructureException
     */
    #[DataProvider('findOneByThrowsNotFoundExceptionProvider')]
    public function testFindOneByThrowsNotFoundException(ExamCriteria $criteria): void
    {
        $repository = $this->getRepository();

        $this->expectException(ExamNotFoundException::class);
        $repository->findOneBy($criteria);
    }

    /**
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws CollectionException
     */
    public static function findOneByThrowsNotFoundExceptionProvider(): \Generator
    {
        yield 'empty search' => [
            'criteria' => ExamCriteria::createEmpty()->filterBySearch(''),
        ];

        yield 'null search' => [
            'criteria' => ExamCriteria::createEmpty()->filterBySearch((string) null),
        ];

        yield 'id not found' => [
            'criteria' => ExamCriteria::createEmpty()->filterById(UuidMother::create()),
        ];

        yield 'ids not found' => [
            'criteria' => ExamCriteria::createEmpty()->filterByIds(new UuidCollection([UuidMother::create(), UuidMother::create()])),
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws InfrastructureException
     * @throws ExamNotFoundException
     * @throws CriteriaException
     */
    public function testUpdateExam(): void
    {
        $exam = ExamMother::create(title: 'Original title');
        $repository = $this->getRepository();

        $repository->put($exam);

        $updatedExam = ExamMother::create(
            id: $exam->getId(),
            title: 'Updated title'
        );
        $repository->put($updatedExam);

        $found = $repository->findOneBy(ExamCriteria::createEmpty()->filterById($exam->getId()));

        $this->assertEquals('Updated title', $found->getTitle());
    }

    /**
     * @throws InfrastructureException
     * @throws InvalidUuidException
     */
    #[DataProvider('criteriaProvider')]
    public function testFindBy(
        ExamCollection $input,
        ExamCriteria $criteria,
        array $expectedResults,
    ): void {
        $repository = $this->getRepository();

        foreach ($input->all() as $exam) {
            $repository->put($exam);
        }

        $result = $repository->findBy($criteria);
        $this->assertCount(\count($expectedResults), $result);

        $expectedResultsIds = array_map(
            fn ($item) => $item->getId(),
            $expectedResults
        );

        $foundIds = array_map(
            fn ($item) => $item->getId(),
            $result->all()
        );

        $this->assertCount(
            0,
            array_diff($expectedResultsIds, $foundIds),
            'Expected results not found in results'
        );
    }

    /**
     * @throws InvalidUuidException
     * @throws CollectionException
     * @throws CriteriaException
     */
    public static function criteriaProvider(): \Generator
    {
        $exam1 = ExamMother::create(title: 'Exam 1', description: 'Description 1');
        $exam2 = ExamMother::create(title: 'Exam 2', description: 'Description 2');
        $exam3 = ExamMother::create(title: 'Title', description: 'Description 3');

        yield 'all' => [
            'input' => new ExamCollection([$exam1, $exam2, $exam3]),
            'criteria' => ExamCriteria::createEmpty(),
            'expectedResults' => [$exam1, $exam2, $exam3],
        ];

        yield 'by id' => [
            'input' => new ExamCollection([$exam1, $exam2, $exam3]),
            'criteria' => ExamCriteria::createEmpty()->filterById($exam1->getId()),
            'expectedResults' => [$exam1],
        ];

        yield 'by ids' => [
            'input' => new ExamCollection([$exam1, $exam2, $exam3]),
            'criteria' => ExamCriteria::createEmpty()->filterByIds(new UuidCollection([$exam1->getId(), $exam2->getId()])),
            'expectedResults' => [$exam1, $exam2],
        ];

        yield 'by title' => [
            'input' => new ExamCollection([$exam1, $exam2, $exam3]),
            'criteria' => ExamCriteria::createEmpty()->filterBySearch('Exam 1'),
            'expectedResults' => [$exam1],
        ];

        yield 'by title search' => [
            'input' => new ExamCollection([$exam1, $exam2, $exam3]),
            'criteria' => ExamCriteria::createEmpty()->filterBySearch('zzz'),
            'expectedResults' => [],
        ];

        yield 'by common title search' => [
            'input' => new ExamCollection([$exam1, $exam2, $exam3]),
            'criteria' => ExamCriteria::createEmpty()->filterBySearch('Exam'),
            'expectedResults' => [$exam1, $exam2],
        ];

        yield 'by common title search but restricted by id' => [
            'input' => new ExamCollection([$exam1, $exam2, $exam3]),
            'criteria' => ExamCriteria::createEmpty()
                ->filterById($exam1->getId())
                ->filterBySearch('Exam'),
            'expectedResults' => [$exam1],
        ];

        yield 'by description' => [
            'input' => new ExamCollection([$exam1, $exam2, $exam3]),
            'criteria' => ExamCriteria::createEmpty()->filterBySearch('Description'),
            'expectedResults' => [$exam1, $exam2, $exam3],
        ];
    }
}
