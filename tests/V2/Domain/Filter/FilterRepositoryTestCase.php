<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Filter;

use App\Tests\V2\Mother\Filter\FilterMother;
use App\V2\Domain\Filter\Exception\FilterNotFoundException;
use App\V2\Domain\Filter\FilterCriteria;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use PHPUnit\Framework\TestCase;

abstract class FilterRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): FilterRepository;

    public function testPut(): void
    {
        $repository = $this->getRepository();

        $filter1 = FilterMother::create(
            id: new Id(null),
            filterCategoryId: new Id(1),
            name: 'Filter 1',
            code: 'FILTER_1',
            sort: 3,
        );

        $filter2 = FilterMother::create(
            id: new Id(null),
            filterCategoryId: new Id(2),
            name: 'Filter 2',
            code: 'FILTER_2',
            sort: 1,
        );

        $filter3 = FilterMother::create(
            id: new Id(null),
            filterCategoryId: new Id(3),
            name: 'Filter 3',
            code: 'FILTER_3',
            sort: 2
        );

        $filter4 = FilterMother::create(
            id: new Id(null),
            filterCategoryId: new Id(1),
            name: 'Filter 4',
            code: 'FILTER_4',
            sort: 4,
            parentId: new Id(1),
        );

        $repository->put($filter1);
        $repository->put($filter2);
        $repository->put($filter3);
        $repository->put($filter4);

        $this->assertNotNull($filter1->getId()->valueOrNull());
        $this->assertNotNull($filter2->getId()->valueOrNull());
        $this->assertNotNull($filter3->getId()->valueOrNull());
        $this->assertNotNull($filter4->getId()->valueOrNull());

        $this->assertEquals($filter1, $repository->findOneBy(FilterCriteria::createById($filter1->getId())));
        $this->assertEquals($filter2, $repository->findOneBy(FilterCriteria::createById($filter2->getId())));
        $this->assertEquals($filter3, $repository->findOneBy(FilterCriteria::createById($filter3->getId())));
        $this->assertEquals($filter4, $repository->findOneBy(FilterCriteria::createById($filter4->getId())));

        $filter2Updated = FilterMother::create(
            id: $filter2->getId(),
            filterCategoryId: new Id(3),
            name: 'Filter 4 updated',
            code: 'FILTER_4 updated',
            sort: 4,
            parentId: new Id(1),
        );

        $repository->put($filter2Updated);
        $result = $repository->findBy(FilterCriteria::createEmpty());
        $this->assertCount(4, $result);
        $updated = $repository->findOneBy(FilterCriteria::createById($filter2->getId()));
        $this->assertEquals($filter2Updated, $updated);
        $this->assertNotEquals($filter2, $updated);
        $this->assertEquals($filter2->getId(), $updated->getId());
    }

    /**
     * @throws FilterNotFoundException
     * @throws InfrastructureException
     * @throws CollectionException
     * @throws CriteriaException
     */
    public function testFindOneBy(): void
    {
        $repository = $this->getRepository();

        $filter1 = FilterMother::create(
            id: new Id(null),
            filterCategoryId: new Id(1),
            name: 'Filter 1',
            code: 'FILTER_1',
            sort: 3,
        );

        $filter2 = FilterMother::create(
            id: new Id(null),
            filterCategoryId: new Id(2),
            name: 'Filter 2',
            code: 'FILTER_2',
            sort: 1,
        );

        $filter3 = FilterMother::create(
            id: new Id(null),
            filterCategoryId: new Id(3),
            name: 'Filter 3',
            code: 'FILTER_3',
            sort: 2
        );

        $filter4 = FilterMother::create(
            id: new Id(null),
            filterCategoryId: new Id(1),
            name: 'Filter 4',
            code: 'FILTER_4',
            sort: 4,
            parentId: new Id(1),
        );

        $repository->put($filter1);
        $repository->put($filter2);
        $repository->put($filter3);
        $repository->put($filter4);

        $this->assertNotNull($filter1->getId()->valueOrNull());
        $this->assertNotNull($filter2->getId()->valueOrNull());
        $this->assertNotNull($filter3->getId()->valueOrNull());
        $this->assertNotNull($filter4->getId()->valueOrNull());

        $result = $repository->findOneBy(
            FilterCriteria::createEmpty()
        );
        $this->assertEquals($filter2, $result);

        $this->assertEquals(
            $filter1,
            $repository->findOneBy(
                FilterCriteria::createById(new Id(1))
            )
        );

        $this->assertEquals(
            $filter3,
            $repository->findOneBy(
                FilterCriteria::createByIds(new IdCollection([new Id(1), new Id(3)]))
            )
        );

        $this->assertEquals(
            $filter1,
            $repository->findOneBy(
                FilterCriteria::createEmpty()
                    ->filterByCategoryId(new Id(1))
            )
        );

        $this->assertEquals(
            $filter4,
            $repository->findOneBy(
                FilterCriteria::createEmpty()
                    ->filterByParentId(new Id(1))
            )
        );

        $this->assertEquals(
            $filter3,
            $repository->findOneBy(
                FilterCriteria::createEmpty()
                    ->filterBySearch('3')
            )
        );

        $this->expectExceptionObject(new FilterNotFoundException());
        $repository->findOneBy(
            FilterCriteria::createEmpty()
                ->filterByCategoryId(new Id(10))
        );
    }

    /**
     * @throws InfrastructureException
     * @throws CollectionException
     * @throws CriteriaException
     */
    public function testFindBy(): void
    {
        $repository = $this->getRepository();

        $filter1 = FilterMother::create(
            id: new Id(null),
            filterCategoryId: new Id(1),
            name: 'Filter 1',
            code: 'FILTER_1',
            sort: 3,
        );

        $filter2 = FilterMother::create(
            id: new Id(null),
            filterCategoryId: new Id(2),
            name: 'Filter 2',
            code: 'FILTER_2',
            sort: 1,
        );

        $filter3 = FilterMother::create(
            id: new Id(null),
            filterCategoryId: new Id(3),
            name: 'Filter 3',
            code: 'FILTER_3',
            sort: 2,
            parentId: new Id(1),
        );

        $filter4 = FilterMother::create(
            id: new Id(null),
            filterCategoryId: new Id(1),
            name: 'Filter 4',
            code: 'FILTER_4',
            sort: 4,
            parentId: new Id(1),
        );

        $repository->put($filter1);
        $repository->put($filter2);
        $repository->put($filter3);
        $repository->put($filter4);

        $result = $repository->findBy(
            FilterCriteria::createEmpty()
        );
        $this->assertCount(4, $result);
        $this->assertEquals($filter2, $result->first());
        $this->assertEquals($filter4, $result->last());

        $result = $repository->findBy(
            FilterCriteria::createEmpty()->filterBySearch('Filter')
        );
        $this->assertCount(4, $result);
        $this->assertEquals($filter2, $result->first());
        $this->assertEquals($filter4, $result->last());

        $result = $repository->findBy(
            FilterCriteria::createByIds(new IdCollection([new Id(1), new Id(4)])),
        );
        $this->assertCount(2, $result);
        $this->assertEquals($filter1, $result->first());
        $this->assertEquals($filter4, $result->last());

        $result = $repository->findBy(
            FilterCriteria::createById(new Id(1))
                ->filterBySearch('Filter')
                ->filterByCategoryId(new Id(1))
        );
        $this->assertCount(1, $result);
        $this->assertEquals($filter1, $result->first());

        $result = $repository->findBy(
            FilterCriteria::createEmpty()
                ->filterByParentId(new Id(1))
        );
        $this->assertCount(2, $result);
        $this->assertEquals($filter3, $result->first());
        $this->assertEquals($filter4, $result->last());
    }
}
