<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Filter\FilterCategory;

use App\Tests\V2\Mother\Filter\FilterCategoryMother;
use App\V2\Domain\Filter\Exception\FilterCategoryNotFoundException;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryCriteria;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use PHPUnit\Framework\TestCase;

abstract class FilterCategoryRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): FilterCategoryRepository;

    public function testPut(): void
    {
        $repository = $this->getRepository();

        $category1 = FilterCategoryMother::create(
            id: new Id(null),
            name: 'Category 1',
            sort: 2,
        );

        $category2 = FilterCategoryMother::create(
            id: new Id(null),
            name: 'Category 2',
            sort: 0,
        );

        $category3 = FilterCategoryMother::create(
            id: new Id(null),
            parentId: new Id(2),
            name: 'Category 3',
            sort: 1,
        );

        $repository->put($category1);
        $repository->put($category2);
        $repository->put($category3);

        $this->assertNotNull($category1->getId()->valueOrNull(), 'Category ID should not be null');
        $this->assertNotNull($category2->getId()->valueOrNull(), 'Category ID should not be null');
        $this->assertNotNull($category3->getId()->valueOrNull(), 'Category ID should not be null');

        $this->assertEquals(
            $category1,
            $repository->findOneBy(FilterCategoryCriteria::createById($category1->getId()))
        );

        $this->assertEquals(
            $category2,
            $repository->findOneBy(FilterCategoryCriteria::createById($category2->getId()))
        );

        $this->assertEquals(
            $category3,
            $repository->findOneBy(FilterCategoryCriteria::createById($category3->getId()))
        );

        $result = $repository->findBy(FilterCategoryCriteria::createEmpty());
        $this->assertCount(3, $result);

        $category2Updated = FilterCategoryMother::create(
            id: $category2->getId(),
            name: 'Category 2 Updated',
            sort: 10,
        );

        $repository->put($category2Updated);

        $result = $repository->findBy(FilterCategoryCriteria::createEmpty());
        $this->assertCount(3, $result);

        $category2Result = $repository->findOneBy(FilterCategoryCriteria::createById($category2->getId()));
        $this->assertEquals($category2Updated, $category2Result);
        $this->assertNotSame($category2Updated, $category2Result);
        $this->assertNotEquals($category2, $category2Result);
        $this->assertEquals($category2->getId(), $category2Result->getId());
    }

    /**
     * @throws InfrastructureException
     * @throws FilterCategoryNotFoundException
     * @throws CriteriaException
     */
    public function testFindOneBy(): void
    {
        $repository = $this->getRepository();

        $category1 = FilterCategoryMother::create(
            id: new Id(null),
            name: 'Category 1',
            sort: 2,
        );

        $category2 = FilterCategoryMother::create(
            id: new Id(null),
            name: 'Category 2',
            sort: 0,
        );

        $category3 = FilterCategoryMother::create(
            id: new Id(null),
            parentId: new Id(2),
            name: 'Category 3',
            sort: 1,
        );

        $repository->put($category1);
        $repository->put($category2);
        $repository->put($category3);

        $this->assertNotNull($category1->getId()->valueOrNull(), 'Category ID should not be null');
        $this->assertNotNull($category2->getId()->valueOrNull(), 'Category ID should not be null');
        $this->assertNotNull($category3->getId()->valueOrNull(), 'Category ID should not be null');

        $this->assertEquals(
            $category1,
            $repository->findOneBy(
                FilterCategoryCriteria::createById(new Id(1))
            )
        );

        $this->assertEquals(
            $category2,
            $repository->findOneBy(
                FilterCategoryCriteria::createEmpty()
            )
        );

        $this->assertEquals(
            $category1,
            $repository->findOneBy(
                FilterCategoryCriteria::createEmpty()
                    ->filterByName('1')
            )
        );

        $this->assertEquals(
            $category3,
            $repository->findOneBy(
                FilterCategoryCriteria::createEmpty()
                    ->filterByParentId(new Id(2))
                    ->filterByName('3')
            )
        );

        $this->expectException(FilterCategoryNotFoundException::class);
        $repository->findOneBy(
            FilterCategoryCriteria::createEmpty()
                ->filterByParentId(new Id(2))
                ->filterByName('random name')
        );
    }

    /**
     * @throws InfrastructureException
     * @throws FilterCategoryNotFoundException
     * @throws CriteriaException
     * @throws CollectionException
     */
    public function testFindBy(): void
    {
        $repository = $this->getRepository();

        $category1 = FilterCategoryMother::create(
            id: new Id(null),
            name: 'Category 1',
            sort: 2,
        );

        $category2 = FilterCategoryMother::create(
            id: new Id(null),
            name: 'Category 2',
            sort: 0,
        );

        $category3 = FilterCategoryMother::create(
            id: new Id(null),
            name: 'Category 3',
            sort: 1,
        );

        $repository->put($category1);
        $repository->put($category2);
        $repository->put($category3);

        $result = $repository->findBy(
            FilterCategoryCriteria::createEmpty()
        );
        $this->assertCount(3, $result);
        $this->assertEquals(2, $result->first()->getId()->value());
        $this->assertEquals(1, $result->last()->getId()->value());

        $result = $repository->findBy(
            FilterCategoryCriteria::createEmpty()->filterByName('Category')
        );
        $this->assertCount(3, $result);
        $this->assertEquals(2, $result->first()->getId()->value());
        $this->assertEquals(1, $result->last()->getId()->value());

        $result = $repository->findBy(
            FilterCategoryCriteria::createByIds(
                new IdCollection([new Id(1), new Id(3)])
            )
        );
        $this->assertCount(2, $result);
        $this->assertEquals(3, $result->first()->getId()->value());
        $this->assertEquals(1, $result->last()->getId()->value());

        $result = $repository->findBy(
            FilterCategoryCriteria::createByIds(
                new IdCollection([new Id(1), new Id(3)])
            )->filterByName('Category 3')
        );
        $this->assertCount(1, $result);
        $this->assertEquals($category3, $result->first());
    }
}
