<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Response;

use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Pagination;
use App\V2\Domain\User\LegacyUserCollection;
use App\V2\Domain\User\PaginatedUserCollection;
use App\V2\Domain\User\UserCriteria;
use App\V2\Infrastructure\Response\ApiResponseContent;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class ApiResponseContentTest extends TestCase
{
    #[DataProvider('toArrayDataProvider')]
    public function testToArray(ApiResponseContent $responseContent, array $expectedArray): void
    {
        $this->assertEquals($expectedArray, $responseContent->toArray());
    }

    /**
     * @throws CollectionException
     */
    public static function toArrayDataProvider(): \Generator
    {
        yield 'data not defined' => [
            'responseContent' => new ApiResponseContent(),
            'expectedArray' => [],
        ];

        yield 'empty data' => [
            'responseContent' => ApiResponseContent::createFromData([]),
            'expectedArray' => [
                'data' => [],
            ],
        ];

        yield 'with data' => [
            'responseContent' => ApiResponseContent::createFromData(['data' => 'data']),
            'expectedArray' => [
                'data' => [
                    'data' => 'data',
                ],
            ],
        ];

        yield 'with message' => [
            'responseContent' => ApiResponseContent::createFromMessage('message'),
            'expectedArray' => ['message' => 'message', 'error' => 1],
        ];

        yield 'with metadata' => [
            'responseContent' => (new ApiResponseContent())->setMetadata(['metadata' => 'metadata']),
            'expectedArray' => ['metadata' => ['metadata' => 'metadata']],
        ];

        yield 'with pagination metadata' => [
            'responseContent' => ApiResponseContent::createFromData([])->addPaginationMetadata(
                new PaginatedUserCollection(new LegacyUserCollection([]), 25),
                UserCriteria::createEmpty()->withPagination(new Pagination(1, 10))
            ),
            'expectedArray' => [
                'data' => [],
                'metadata' => [
                    'page' => 1,
                    'total_pages' => 3,
                    'limit' => 10,
                    'total' => 25,
                ],
            ],
        ];

        yield 'complete' => [
            'responseContent' => ApiResponseContent::createFromData(['data' => 'data'])
                ->setMetadata(['metadata' => 'metadata'])
                ->addPaginationMetadata(
                    new PaginatedUserCollection(new LegacyUserCollection([]), 25),
                    UserCriteria::createEmpty()->withPagination(new Pagination(1, 10))
                ),
            'expectedArray' => [
                'data' => [
                    'data' => 'data',
                ],
                'metadata' => [
                    'metadata' => 'metadata',
                    'page' => 1,
                    'total_pages' => 3,
                    'limit' => 10,
                    'total' => 25,
                ],
            ],
        ];
    }
}
