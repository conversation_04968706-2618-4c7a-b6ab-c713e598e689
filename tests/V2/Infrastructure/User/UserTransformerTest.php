<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\User;

use App\Entity\User;
use App\V2\Domain\User\LegacyUserCollection;
use App\V2\Domain\User\PaginatedUserCollection;
use App\V2\Infrastructure\User\UserTransformer;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class UserTransformerTest extends TestCase
{
    /**
     * Test that fromUserToArrayForListing correctly transforms a User entity to an array.
     *
     * @throws Exception
     */
    public function testFromUserToArrayForListing(): void
    {
        // Arrange
        $user = $this->createMock(User::class);
        $user->method('getId')->willReturn(123);
        $user->method('getAvatar')->willReturn('avatar.jpg');
        $user->method('getEmail')->willReturn('<EMAIL>');
        $user->method('getFirstName')->willReturn('John');
        $user->method('getLastName')->willReturn('Doe');
        $user->method('getRoles')->willReturn(['ROLE_USER', 'ROLE_ADMIN']);
        $user->method('getIsActive')->willReturn(true);
        $user->method('getPoints')->willReturn(100);

        // Act
        $result = UserTransformer::fromUserToArrayForListing($user);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('avatar', $result);
        $this->assertArrayHasKey('email', $result);
        $this->assertArrayHasKey('first_name', $result);
        $this->assertArrayHasKey('last_name', $result);
        $this->assertArrayHasKey('roles', $result);
        $this->assertArrayHasKey('is_active', $result);
        $this->assertArrayHasKey('points', $result);

        $this->assertSame(123, $result['id']);
        $this->assertSame('avatar.jpg', $result['avatar']);
        $this->assertSame('<EMAIL>', $result['email']);
        $this->assertSame('John', $result['first_name']);
        $this->assertSame('Doe', $result['last_name']);
        $this->assertSame(['ROLE_USER', 'ROLE_ADMIN'], $result['roles']);
        $this->assertTrue($result['is_active']);
        $this->assertSame(100, $result['points']);
    }

    /**
     * Test that fromCollectionToArray correctly transforms a UserCollection to an array.
     *
     * @throws Exception
     */
    public function testFromCollectionToArray(): void
    {
        // Arrange
        $user1 = $this->createMock(User::class);
        $user1->method('getId')->willReturn(1);
        $user1->method('getEmail')->willReturn('<EMAIL>');

        $user2 = $this->createMock(User::class);
        $user2->method('getId')->willReturn(2);
        $user2->method('getEmail')->willReturn('<EMAIL>');

        $collection = $this->createMock(LegacyUserCollection::class);
        $collection->method('all')->willReturn([$user1, $user2]);

        // Act
        $result = UserTransformer::fromCollectionToArray($collection);

        // Assert
        $this->assertIsArray($result);
        $this->assertCount(2, $result);

        $this->assertArrayHasKey('id', $result[0]);
        $this->assertSame(1, $result[0]['id']);
        $this->assertArrayHasKey('email', $result[0]);
        $this->assertSame('<EMAIL>', $result[0]['email']);

        $this->assertArrayHasKey('id', $result[1]);
        $this->assertSame(2, $result[1]['id']);
        $this->assertArrayHasKey('email', $result[1]);
        $this->assertSame('<EMAIL>', $result[1]['email']);
    }

    /**
     * Test that fromPaginatedCollectionToArray correctly transforms a PaginatedUserCollection to an array.
     *
     * @throws Exception
     */
    public function testFromPaginatedCollectionToArray(): void
    {
        // Arrange
        $user1 = $this->createMock(User::class);
        $user1->method('getId')->willReturn(1);
        $user1->method('getEmail')->willReturn('<EMAIL>');

        $user2 = $this->createMock(User::class);
        $user2->method('getId')->willReturn(2);
        $user2->method('getEmail')->willReturn('<EMAIL>');

        $collection = $this->createMock(LegacyUserCollection::class);
        $collection->method('all')->willReturn([$user1, $user2]);

        $paginatedCollection = $this->createMock(PaginatedUserCollection::class);
        $paginatedCollection->method('getCollection')->willReturn($collection);
        $paginatedCollection->method('getTotalItems')->willReturn(10);

        // Act
        $result = UserTransformer::fromPaginatedCollectionToArray($paginatedCollection);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('users', $result);
        $this->assertArrayHasKey('total', $result);

        $this->assertIsArray($result['users']);
        $this->assertCount(2, $result['users']);
        $this->assertSame(10, $result['total']);

        $this->assertArrayHasKey('id', $result['users'][0]);
        $this->assertSame(1, $result['users'][0]['id']);
        $this->assertArrayHasKey('email', $result['users'][0]);
        $this->assertSame('<EMAIL>', $result['users'][0]['email']);

        $this->assertArrayHasKey('id', $result['users'][1]);
        $this->assertSame(2, $result['users'][1]['id']);
        $this->assertArrayHasKey('email', $result['users'][1]);
        $this->assertSame('<EMAIL>', $result['users'][1]['email']);
    }
}
