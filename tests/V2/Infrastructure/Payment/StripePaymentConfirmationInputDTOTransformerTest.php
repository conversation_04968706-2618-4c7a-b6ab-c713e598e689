<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Payment;

use App\Tests\V2\Mother\Payment\PaymentConfirmationInputDTOMother;
use App\V2\Infrastructure\Payment\StripePaymentConfirmationInputDTOTransformer;
use <PERSON>ymfony\Component\HttpFoundation\Request;

final class StripePaymentConfirmationInputDTOTransformerTest extends PaymentConfirmationInputDTOTransformerTestCase
{
    protected function getTransformer(): string
    {
        return StripePaymentConfirmationInputDTOTransformer::class;
    }

    public static function validRequestProvider(): \Generator
    {
        yield 'basic webhook request with signature' => [
            'request' => self::createRequestWithStripeSignature(
                payload: '{"id":"evt_test_webhook","object":"event"}',
                signature: 'whsec_test_signature_123'
            ),
            'expectedDTO' => PaymentConfirmationInputDTOMother::create(
                payload: '{"id":"evt_test_webhook","object":"event"}',
                metadata: [
                    'signature' => 'whsec_test_signature_123',
                ]
            ),
        ];

        yield 'webhook request without signature' => [
            'request' => self::createRequestWithStripeSignature(
                payload: '{"id":"evt_test_no_signature","object":"event"}',
                signature: null
            ),
            'expectedDTO' => PaymentConfirmationInputDTOMother::create(
                payload: '{"id":"evt_test_no_signature","object":"event"}',
                metadata: [
                    'signature' => '',
                ]
            ),
        ];

        yield 'empty payload request' => [
            'request' => self::createRequestWithStripeSignature(
                payload: '',
                signature: 'whsec_empty_payload'
            ),
            'expectedDTO' => PaymentConfirmationInputDTOMother::create(
                payload: '',
                metadata: [
                    'signature' => 'whsec_empty_payload',
                ]
            ),
        ];

        yield 'complex webhook payload' => [
            'request' => self::createRequestWithStripeSignature(
                payload: '{"id":"evt_complex","object":"event","type":"payment_intent.succeeded","data":{"object":{"id":"pi_test","amount":2000,"currency":"eur"}}}',
                signature: 'whsec_complex_signature_456'
            ),
            'expectedDTO' => PaymentConfirmationInputDTOMother::create(
                payload: '{"id":"evt_complex","object":"event","type":"payment_intent.succeeded","data":{"object":{"id":"pi_test","amount":2000,"currency":"eur"}}}',
                metadata: [
                    'signature' => 'whsec_complex_signature_456',
                ]
            ),
        ];
    }

    public static function returnValueConsistencyProvider(): \Generator
    {
        yield 'consistent transformation for same request' => [
            'request' => self::createRequestWithStripeSignature(
                payload: '{"id":"evt_consistency","object":"event"}',
                signature: 'whsec_consistency_test'
            ),
        ];

        yield 'consistent transformation for empty request' => [
            'request' => self::createRequestWithStripeSignature(
                payload: '',
                signature: ''
            ),
        ];
    }

    public static function dataTransformationProvider(): \Generator
    {
        yield 'payload and signature extraction' => [
            'request' => self::createRequestWithStripeSignature(
                payload: '{"test":"data"}',
                signature: 'whsec_test_123'
            ),
            'expectedPayload' => '{"test":"data"}',
            'expectedMetadata' => [
                'signature' => 'whsec_test_123',
            ],
        ];

        yield 'missing signature header' => [
            'request' => self::createRequestWithStripeSignature(
                payload: '{"missing":"signature"}',
                signature: null
            ),
            'expectedPayload' => '{"missing":"signature"}',
            'expectedMetadata' => [
                'signature' => '',
            ],
        ];

        yield 'empty signature header' => [
            'request' => self::createRequestWithStripeSignature(
                payload: '{"empty":"signature"}',
                signature: ''
            ),
            'expectedPayload' => '{"empty":"signature"}',
            'expectedMetadata' => [
                'signature' => '',
            ],
        ];
    }

    private static function createRequestWithStripeSignature(string $payload, ?string $signature): Request
    {
        $request = new Request(
            query: [],
            request: [],
            attributes: [],
            cookies: [],
            files: [],
            server: [],
            content: $payload
        );

        if (null !== $signature) {
            $request->headers->set('Stripe-Signature', $signature);
        }

        return $request;
    }
}
