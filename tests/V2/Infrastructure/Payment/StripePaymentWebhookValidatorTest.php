<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Payment;

use App\V2\Domain\Purchase\Exception\PaymentManagerException;
use App\V2\Infrastructure\Payment\StripePaymentWebhookValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;

class StripePaymentWebhookValidatorTest extends TestCase
{
    private StripePaymentWebhookValidator $validator;

    protected function setUp(): void
    {
        $this->validator = new StripePaymentWebhookValidator();
    }

    public function testValidateRequestWithValidInput(): void
    {
        $request = new Request();
        $request->initialize(
            query: [],
            request: [],
            attributes: [],
            cookies: [],
            files: [],
            content: '{"type": "payment_intent.succeeded"}'
        );
        $request->headers->set('Stripe-Signature', 't=1234567890,v1=test_signature');

        // Header will be set via server parameter instead

        // Should not throw exception
        $this->validator->validateRequest($request);
        $this->assertTrue(true); // Assert that no exception was thrown
    }

    public function testValidateRequestThrowsExceptionWithEmptyPayload(): void
    {
        $request = new Request();
        $request->initialize(
            query: [],
            request: [],
            attributes: [],
            cookies: [],
            files: [],
            server: [],
            content: ''
        );
        $request->headers->set('Stripe-Signature', 't=1234567890,v1=test_signature');

        $this->expectException(PaymentManagerException::class);
        $this->expectExceptionMessage('Invalid payment webhook payload');

        $this->validator->validateRequest($request);
    }

    public function testValidateRequestThrowsExceptionWithWhitespaceOnlyPayload(): void
    {
        $request = new Request();
        $request->initialize(
            query: [],
            request: [],
            attributes: [],
            cookies: [],
            files: [],
            server: [],
            content: '   '
        );
        $request->headers->set('Stripe-Signature', 't=1234567890,v1=test_signature');

        $this->expectException(PaymentManagerException::class);
        $this->expectExceptionMessage('Invalid payment webhook payload');

        $this->validator->validateRequest($request);
    }

    public function testValidateRequestThrowsExceptionWithMissingSignature(): void
    {
        $request = new Request();
        $request->initialize(
            query: [],
            request: [],
            attributes: [],
            cookies: [],
            files: [],
            server: [],
            content: '{"type": "payment_intent.succeeded"}'
        );

        $this->expectException(PaymentManagerException::class);
        $this->expectExceptionMessage('Invalid payment webhook signature');

        $this->validator->validateRequest($request);
    }

    public function testValidateRequestThrowsExceptionWithEmptySignature(): void
    {
        $request = new Request();
        $request->initialize(
            query: [],
            request: [],
            attributes: [],
            cookies: [],
            files: [],
            server: [],
            content: '{"type": "payment_intent.succeeded"}'
        );
        $request->headers->set('Stripe-Signature', '');

        $this->expectException(PaymentManagerException::class);
        $this->expectExceptionMessage('Invalid payment webhook signature');

        $this->validator->validateRequest($request);
    }

    public function testValidateRequestThrowsExceptionWithWhitespaceOnlySignature(): void
    {
        $request = new Request();
        $request->initialize(
            query: [],
            request: [],
            attributes: [],
            cookies: [],
            files: [],
            server: [],
            content: '{"type": "payment_intent.succeeded"}'
        );
        $request->headers->set('Stripe-Signature', '   ');

        $this->expectException(PaymentManagerException::class);
        $this->expectExceptionMessage('Invalid payment webhook signature');

        $this->validator->validateRequest($request);
    }
}
