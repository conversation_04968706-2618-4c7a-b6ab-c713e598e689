<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Payment;

use App\V2\Application\DTO\Payment\PaymentConfirmationInputDTO;
use App\V2\Infrastructure\Payment\PaymentConfirmationInputDTOTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;

abstract class PaymentConfirmationInputDTOTransformerTestCase extends TestCase
{
    abstract protected function getTransformer(): string;

    #[DataProvider('validRequestProvider')]
    public function testFromRequestWithValidInputs(
        Request $request,
        PaymentConfirmationInputDTO $expectedDTO
    ): void {
        $transformerClass = $this->getTransformer();
        $result = $transformerClass::fromRequest($request);

        $this->assertEquals($expectedDTO, $result);
        $this->assertInstanceOf(PaymentConfirmationInputDTO::class, $result);
    }

    public function testFromRequestMethodExists(): void
    {
        $transformerClass = $this->getTransformer();

        $this->assertTrue(method_exists($transformerClass, 'fromRequest'));
        $this->assertTrue(\is_callable([$transformerClass, 'fromRequest']));
    }

    public function testImplementsInterface(): void
    {
        $transformerClass = $this->getTransformer();

        $this->assertTrue(is_subclass_of($transformerClass, PaymentConfirmationInputDTOTransformer::class));
    }

    #[DataProvider('returnValueConsistencyProvider')]
    public function testFromRequestReturnValueConsistency(Request $request): void
    {
        $transformerClass = $this->getTransformer();

        $result1 = $transformerClass::fromRequest($request);
        $result2 = $transformerClass::fromRequest($request);

        $this->assertEquals($result1, $result2);
        $this->assertInstanceOf(PaymentConfirmationInputDTO::class, $result1);
        $this->assertInstanceOf(PaymentConfirmationInputDTO::class, $result2);
    }

    #[DataProvider('dataTransformationProvider')]
    public function testDataTransformationAccuracy(
        Request $request,
        string $expectedPayload,
        array $expectedMetadata
    ): void {
        $transformerClass = $this->getTransformer();
        $result = $transformerClass::fromRequest($request);

        $this->assertSame($expectedPayload, $result->getPayload());
        $this->assertEquals($expectedMetadata, $result->getMetadata());
    }

    public function testInterfaceCompliance(): void
    {
        $request = new Request();
        $transformerClass = $this->getTransformer();

        $result = $transformerClass::fromRequest($request);

        $this->assertInstanceOf(PaymentConfirmationInputDTO::class, $result);
    }

    /**
     * Data provider for valid request scenarios.
     * Should be implemented by concrete test classes.
     */
    abstract public static function validRequestProvider(): \Generator;

    /**
     * Data provider for return value consistency testing.
     * Should be implemented by concrete test classes.
     */
    abstract public static function returnValueConsistencyProvider(): \Generator;

    /**
     * Data provider for data transformation accuracy testing.
     * Should be implemented by concrete test classes.
     */
    abstract public static function dataTransformationProvider(): \Generator;
}
