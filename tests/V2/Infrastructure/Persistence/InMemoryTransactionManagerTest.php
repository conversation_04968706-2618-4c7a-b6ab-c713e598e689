<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence;

use App\Tests\V2\Application\Persistence\TransactionManagerInterfaceTestCase;
use App\V2\Infrastructure\Persistence\InMemoryTransactionManager;

class InMemoryTransactionManagerTest extends TransactionManagerInterfaceTestCase
{
    protected function getTransactionManager(): InMemoryTransactionManager
    {
        return new InMemoryTransactionManager();
    }

    public function testIsInTransactionInitiallyFalse(): void
    {
        $transactionManager = new InMemoryTransactionManager();

        $this->assertFalse($transactionManager->isInTransaction());
    }

    public function testIsInTransactionAfterBegin(): void
    {
        $transactionManager = new InMemoryTransactionManager();

        $transactionManager->beginTransaction();

        $this->assertTrue($transactionManager->isInTransaction());
    }

    public function testIsInTransactionAfterCommit(): void
    {
        $transactionManager = new InMemoryTransactionManager();

        $transactionManager->beginTransaction();
        $transactionManager->commit();

        $this->assertFalse($transactionManager->isInTransaction());
    }

    public function testIsInTransactionAfterRollback(): void
    {
        $transactionManager = new InMemoryTransactionManager();

        $transactionManager->beginTransaction();
        $transactionManager->rollback();

        $this->assertFalse($transactionManager->isInTransaction());
    }

    public function testTransactionalSetsTransactionState(): void
    {
        $transactionManager = new InMemoryTransactionManager();
        $stateInsideTransaction = null;

        $transactionManager->transactional(function () use ($transactionManager, &$stateInsideTransaction) {
            $stateInsideTransaction = $transactionManager->isInTransaction();

            return 'result';
        });

        $this->assertTrue($stateInsideTransaction);
        $this->assertFalse($transactionManager->isInTransaction());
    }

    public function testTransactionalResetsStateOnException(): void
    {
        $transactionManager = new InMemoryTransactionManager();

        try {
            $transactionManager->transactional(function () {
                throw new \RuntimeException('Test exception');
            });
        } catch (\RuntimeException) {
            // Expected exception
        }

        $this->assertFalse($transactionManager->isInTransaction());
    }
}
