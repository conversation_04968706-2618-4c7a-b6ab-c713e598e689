<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence;

use App\Tests\V2\Application\Persistence\TransactionManagerInterfaceTestCase;
use App\V2\Infrastructure\Persistence\DBALTransactionManager;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use PHPUnit\Framework\MockObject\MockObject;

final class DBALTransactionManagerTest extends TransactionManagerInterfaceTestCase
{
    private Connection&MockObject $connectionMock;

    protected function setUp(): void
    {
        parent::setUp();
        $this->connectionMock = $this->createMock(Connection::class);
    }

    protected function getTransactionManager(): DBALTransactionManager
    {
        return new DBALTransactionManager(
            connection: $this->connectionMock,
        );
    }

    public function testBeginTransactionCallsConnectionBeginTransaction(): void
    {
        $this->connectionMock
            ->expects($this->once())
            ->method('beginTransaction');

        $transactionManager = $this->getTransactionManager();
        $transactionManager->beginTransaction();
    }

    public function testCommitCallsConnectionCommit(): void
    {
        $this->connectionMock
            ->expects($this->once())
            ->method('commit');

        $transactionManager = $this->getTransactionManager();
        $transactionManager->commit();
    }

    public function testRollbackCallsConnectionRollback(): void
    {
        $this->connectionMock
            ->expects($this->once())
            ->method('rollback');

        $transactionManager = $this->getTransactionManager();
        $transactionManager->rollback();
    }

    public function testTransactionalCallsBeginCommitOnSuccess(): void
    {
        $this->connectionMock
            ->expects($this->once())
            ->method('beginTransaction');

        $this->connectionMock
            ->expects($this->once())
            ->method('commit');

        $this->connectionMock
            ->expects($this->never())
            ->method('rollback');

        $transactionManager = $this->getTransactionManager();
        $result = $transactionManager->transactional(function () {
            return 'success';
        });

        $this->assertSame('success', $result);
    }

    public function testTransactionalCallsBeginRollbackOnException(): void
    {
        $this->connectionMock
            ->expects($this->once())
            ->method('beginTransaction');

        $this->connectionMock
            ->expects($this->never())
            ->method('commit');

        $this->connectionMock
            ->expects($this->once())
            ->method('rollback');

        $transactionManager = $this->getTransactionManager();

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Test exception');

        $transactionManager->transactional(function () {
            throw new \RuntimeException('Test exception');
        });
    }

    public function testBeginTransactionThrowsDBALException(): void
    {
        $dbalException = new DBALException('Connection error');

        $this->connectionMock
            ->expects($this->once())
            ->method('beginTransaction')
            ->willThrowException($dbalException);

        $transactionManager = $this->getTransactionManager();

        $this->expectException(DBALException::class);
        $this->expectExceptionMessage('Connection error');

        $transactionManager->beginTransaction();
    }

    public function testCommitThrowsDBALException(): void
    {
        $dbalException = new DBALException('Commit error');

        $this->connectionMock
            ->expects($this->once())
            ->method('commit')
            ->willThrowException($dbalException);

        $transactionManager = $this->getTransactionManager();

        $this->expectException(DBALException::class);
        $this->expectExceptionMessage('Commit error');

        $transactionManager->commit();
    }

    public function testRollbackThrowsDBALException(): void
    {
        $dbalException = new DBALException('Rollback error');

        $this->connectionMock
            ->expects($this->once())
            ->method('rollback')
            ->willThrowException($dbalException);

        $transactionManager = $this->getTransactionManager();

        $this->expectException(DBALException::class);
        $this->expectExceptionMessage('Rollback error');

        $transactionManager->rollback();
    }
}
