<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Exam;

use App\Tests\V2\Domain\Exam\ExamRepositoryTestCase;
use App\V2\Domain\Exam\ExamRepository;
use App\V2\Infrastructure\Persistence\Exam\InMemoryExamRepository;

class InMemoryExamRepositoryTest extends ExamRepositoryTestCase
{
    protected function getRepository(): ExamRepository
    {
        return new InMemoryExamRepository();
    }
}
