<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Assignment;

use App\Tests\V2\Domain\Assignment\AssignmentRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\Assignment\AssignmentRepository;
use App\V2\Infrastructure\Persistence\Assignment\DBALAssignmentRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;

class DBALAssignmentRepositoryTest extends AssignmentRepositoryTestCase
{
    private const string TABLE_NAME = 'assignment';
    private Connection $connection;

    /**
     * @throws SchemaException
     * @throws Exception
     */
    protected function getRepository(): AssignmentRepository
    {
        /**
         * @var Schema $schema
         */
        [$this->connection, $schema] = DBALConnectionFactory::create();

        if ($schema->hasTable(self::TABLE_NAME)) {
            $this->dropTable();
        }

        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'string', ['length' => 36]);
        $table->addColumn('user_id', 'integer');
        $table->addColumn('resource_type', 'string', ['length' => 50]);
        $table->addColumn('resource_id', 'string', ['length' => 36]);
        $table->addColumn('created_at', 'datetime');
        $table->addColumn('updated_at', 'datetime', ['notnull' => false]);
        $table->addColumn('deleted_at', 'datetime', ['notnull' => false]);

        $table->setPrimaryKey(['id']);
        $table->addIndex(['user_id'], 'idx_assignment_user_id');
        $table->addIndex(['resource_type', 'resource_id'], 'idx_assignment_resource');
        $table->addIndex(['user_id', 'resource_type', 'resource_id'], 'idx_assignment_user_resource');

        foreach ($this->connection->getDatabasePlatform()->getCreateTableSQL($table) as $sql) {
            $this->connection->executeStatement($sql);
        }

        return new DBALAssignmentRepository(
            connection: $this->connection,
            assignmentTableName: self::TABLE_NAME,
        );
    }

    /**
     * @throws Exception
     */
    private function dropTable(): void
    {
        $this->connection->executeStatement('DROP TABLE IF EXISTS `' . self::TABLE_NAME . '`');
    }

    /**
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->dropTable();
        parent::tearDown();
    }
}
