<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Filter\FilterCategory;

use App\Tests\V2\Domain\Filter\FilterCategory\FilterCategoryRepositoryTestCase;
use App\V2\Infrastructure\Persistence\Filter\FilterCategory\InMemoryFilterCategoryRepository;

class InMemoryFilterCategoryRepositoryTest extends FilterCategoryRepositoryTestCase
{
    protected function getRepository(): InMemoryFilterCategoryRepository
    {
        return new InMemoryFilterCategoryRepository();
    }
}
