<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Filter;

use App\Tests\V2\Domain\Filter\FilterRepositoryTestCase;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Infrastructure\Persistence\Filter\InMemoryFilterRepository;

class InMemoryFilterRepositoryTest extends FilterRepositoryTestCase
{
    protected function getRepository(): FilterRepository
    {
        return new InMemoryFilterRepository();
    }
}
