<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\Filter;

use App\Tests\V2\Domain\Filter\FilterRepositoryTestCase;
use App\Tests\V2\Infrastructure\DBAL\DBALConnectionFactory;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Infrastructure\Persistence\Filter\DBALFilterRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;

class DBALFilterRepositoryTest extends FilterRepositoryTestCase
{
    private const string TABLE_NAME = 'filter';
    private Connection $connection;

    protected function getRepository(): FilterRepository
    {
        try {
            [$this->connection] = DBALConnectionFactory::create();

            $this->createTable();

            return new DBALFilterRepository(
                connection: $this->connection,
                filterTableName: self::TABLE_NAME,
            );
        } catch (DBALException $e) {
            $this->fail('Failed to create DBAL filter repository: ' . $e->getMessage());
        }
    }

    /**
     * @throws SchemaException
     * @throws DBALException
     */
    private function createTable(): void
    {
        $this->connection->executeQuery('DROP TABLE IF EXISTS ' . static::TABLE_NAME);
        $schema = new Schema();
        $table = $schema->createTable(self::TABLE_NAME);
        $table->addColumn('id', 'integer', ['autoincrement' => true]);
        $table->addColumn('filter_category_id', 'integer');
        $table->addColumn('name', 'string', ['length' => 255]);
        $table->addColumn('code', 'string', ['length' => 50, 'notnull' => false]);
        $table->addColumn('sort', 'integer');
        $table->addColumn('parent_id', 'integer', ['notnull' => false]);

        $table->setPrimaryKey(['id']);
        $table->addUniqueConstraint(columnNames: ['code'], indexName: 'INX_UNQ_FILTER_CODE');
        $this->connection->createSchemaManager()->createTable($table);
    }
}
