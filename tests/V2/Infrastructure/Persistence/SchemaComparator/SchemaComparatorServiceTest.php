<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\SchemaComparator;

use App\V2\Infrastructure\Persistence\SchemaComparator\SchemaComparatorService;
use PHPUnit\Framework\TestCase;

class SchemaComparatorServiceTest extends TestCase
{
    private SchemaComparatorService $schemaComparator;

    protected function setUp(): void
    {
        $this->schemaComparator = new SchemaComparatorService();
    }

    public function testCompareIdenticalStructures(): void
    {
        $structure = [
            'tables' => [
                'test_table' => [
                    'columns' => [
                        'id' => 'INT AUTO_INCREMENT NOT NULL',
                        'name' => 'VARCHAR(255) NOT NULL',
                    ],
                    'primary_key' => 'id',
                    'foreign_keys' => [],
                    'indexes' => [],
                ],
            ],
        ];

        $differences = $this->schemaComparator->compare($structure, $structure);

        $this->assertEmpty($differences);
    }

    public function testCompareEquivalentColumnTypes(): void
    {
        $expectedStructure = [
            'tables' => [
                'test_table' => [
                    'columns' => [
                        'id' => 'INT AUTO_INCREMENT NOT NULL',
                        'name' => 'VARCHAR(20) NOT NULL',
                        'email' => 'VARCHAR(255) DEFAULT NULL',
                        'score' => 'DOUBLE PRECISION NOT NULL',
                        'description' => 'LONGTEXT DEFAULT NULL',
                        'created_at' => 'DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\'',
                        'metadata' => 'LONGTEXT NOT NULL COMMENT \'(DC2Type:json)\'',
                        'uuid' => 'CHAR(36) NOT NULL',
                        'amount' => 'NUMERIC(10, 0) DEFAULT NULL',
                        'is_active' => 'TINYINT(1) DEFAULT \'1\' NOT NULL',
                        'updated_at' => 'DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\'',
                        'status' => 'INT DEFAULT NULL',
                        'settings' => 'LONGTEXT DEFAULT NULL COMMENT \'(DC2Type:array)\'',
                    ],
                    'primary_key' => 'id',
                    'foreign_keys' => [],
                    'indexes' => [],
                ],
            ],
        ];

        $currentStructure = [
            'tables' => [
                'test_table' => [
                    'columns' => [
                        'id' => 'INTEGER NOT NULL AUTO_INCREMENT',
                        'name' => 'STRING (20) NOT NULL',
                        'email' => 'STRING (255)',
                        'score' => 'FLOAT NOT NULL',
                        'description' => 'TEXT',
                        'created_at' => 'DATETIME_IMMUTABLE NOT NULL',
                        'metadata' => 'JSON NOT NULL',
                        'uuid' => 'STRING (36) NOT NULL',
                        'amount' => 'DECIMAL (10,0)',
                        'is_active' => 'BOOLEAN NOT NULL DEFAULT \'1\'',
                        'updated_at' => 'DATETIME',
                        'status' => 'INTEGER NOT NULL',
                        'settings' => 'ARRAY',
                    ],
                    'primary_key' => 'id',
                    'foreign_keys' => [],
                    'indexes' => [],
                ],
            ],
        ];

        $differences = $this->schemaComparator->compare($expectedStructure, $currentStructure);

        // No debería haber diferencias porque los tipos son equivalentes
        $this->assertEmpty($differences);
    }

    public function testCompareRealDifferences(): void
    {
        $expectedStructure = [
            'tables' => [
                'test_table' => [
                    'columns' => [
                        'id' => 'INT AUTO_INCREMENT NOT NULL',
                        'name' => 'VARCHAR(255) NOT NULL',
                    ],
                    'primary_key' => 'id',
                    'foreign_keys' => [],
                    'indexes' => [],
                ],
            ],
        ];

        $currentStructure = [
            'tables' => [
                'test_table' => [
                    'columns' => [
                        'id' => 'INT AUTO_INCREMENT NOT NULL',
                        'name' => 'VARCHAR(100) NOT NULL', // Diferente longitud
                    ],
                    'primary_key' => 'id',
                    'foreign_keys' => [],
                    'indexes' => [],
                ],
            ],
        ];

        $differences = $this->schemaComparator->compare($expectedStructure, $currentStructure);

        // Debería haber una diferencia por la longitud diferente
        $this->assertCount(1, $differences);
        $this->assertEquals('column_mismatch', $differences[0]['type']);
        $this->assertEquals('name', $differences[0]['column']);
    }

    public function testCompareMissingTable(): void
    {
        $expectedStructure = [
            'tables' => [
                'test_table' => [
                    'columns' => ['id' => 'INT AUTO_INCREMENT NOT NULL'],
                    'primary_key' => 'id',
                    'foreign_keys' => [],
                    'indexes' => [],
                ],
            ],
        ];

        $currentStructure = [
            'tables' => [],
        ];

        $differences = $this->schemaComparator->compare($expectedStructure, $currentStructure);

        $this->assertCount(1, $differences);
        $this->assertEquals('missing_table', $differences[0]['type']);
        $this->assertEquals('test_table', $differences[0]['table']);
    }

    public function testCompareExtraTable(): void
    {
        $expectedStructure = [
            'tables' => [],
        ];

        $currentStructure = [
            'tables' => [
                'extra_table' => [
                    'columns' => ['id' => 'INT AUTO_INCREMENT NOT NULL'],
                    'primary_key' => 'id',
                    'foreign_keys' => [],
                    'indexes' => [],
                ],
            ],
        ];

        $differences = $this->schemaComparator->compare($expectedStructure, $currentStructure);

        $this->assertCount(1, $differences);
        $this->assertEquals('extra_table', $differences[0]['type']);
        $this->assertEquals('extra_table', $differences[0]['table']);
    }

    public function testCompareWithSpecificTable(): void
    {
        $expectedStructure = [
            'tables' => [
                'table1' => [
                    'columns' => ['id' => 'INT AUTO_INCREMENT NOT NULL'],
                    'primary_key' => 'id',
                    'foreign_keys' => [],
                    'indexes' => [],
                ],
                'table2' => [
                    'columns' => ['id' => 'INT AUTO_INCREMENT NOT NULL'],
                    'primary_key' => 'id',
                    'foreign_keys' => [],
                    'indexes' => [],
                ],
            ],
        ];

        $currentStructure = [
            'tables' => [
                'table1' => [
                    'columns' => ['id' => 'INT AUTO_INCREMENT NOT NULL'],
                    'primary_key' => 'id',
                    'foreign_keys' => [],
                    'indexes' => [],
                ],
                // table2 falta
            ],
        ];

        // Comparar solo table1
        $differences = $this->schemaComparator->compare($expectedStructure, $currentStructure, 'table1');
        $this->assertEmpty($differences);

        // Comparar solo table2
        $differences = $this->schemaComparator->compare($expectedStructure, $currentStructure, 'table2');
        $this->assertCount(1, $differences);
        $this->assertEquals('missing_table', $differences[0]['type']);
    }

    /**
     * @dataProvider nativeTypesNormalizationProvider
     */
    public function testNativeTypesNormalization(string $nativeType, string $doctrineType): void
    {
        $expectedStructure = [
            'tables' => [
                'test_table' => [
                    'columns' => ['test_column' => $nativeType],
                    'primary_key' => null,
                    'foreign_keys' => [],
                    'indexes' => [],
                ],
            ],
        ];

        $currentStructure = [
            'tables' => [
                'test_table' => [
                    'columns' => ['test_column' => $doctrineType],
                    'primary_key' => null,
                    'foreign_keys' => [],
                    'indexes' => [],
                ],
            ],
        ];

        $differences = $this->schemaComparator->compare($expectedStructure, $currentStructure);
        $this->assertEmpty($differences, "Los tipos '$nativeType' y '$doctrineType' deberían ser equivalentes");
    }

    public static function nativeTypesNormalizationProvider(): array
    {
        return [
            // Casos específicos de lti_tool y tablas con SQL nativo
            ['int(11) NOT NULL AUTO_INCREMENT', 'INTEGER NOT NULL AUTO_INCREMENT'],
            ['varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL', 'STRING (50) NOT NULL'],
            ['varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL', 'STRING (45) NOT NULL'],
            ['varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL', 'STRING (200) NOT NULL'],

            // Casos adicionales de normalización
            ['INT(10) UNSIGNED NOT NULL', 'INTEGER UNSIGNED NOT NULL'],
            ['VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci', 'STRING (255)'],
            ['TEXT COLLATE utf8mb4_unicode_ci', 'LONGTEXT'],

            // Casos específicos de material_course
            ['boolean default true not null', 'BOOLEAN NOT NULL DEFAULT \'1\''],
            ['TINYINT(1) DEFAULT NULL', 'BOOLEAN'],
        ];
    }

    public function testIgnoresSystemTables(): void
    {
        $expectedStructure = [
            'tables' => [
                'user' => [
                    'columns' => ['id' => 'INT AUTO_INCREMENT'],
                    'primary_key' => 'id',
                    'indexes' => [],
                    'foreign_keys' => [],
                ],
            ],
        ];

        $currentStructure = [
            'tables' => [
                'user' => [
                    'columns' => ['id' => 'INT AUTO_INCREMENT'],
                    'primary_key' => 'id',
                    'indexes' => [],
                    'foreign_keys' => [],
                ],
                'migration_versions' => [
                    'columns' => ['version' => 'VARCHAR(191)'],
                    'primary_key' => 'version',
                    'indexes' => [],
                    'foreign_keys' => [],
                ],
            ],
        ];

        $differences = $this->schemaComparator->compare($expectedStructure, $currentStructure);

        // La tabla migration_versions debe ser ignorada, no debe reportarse como extra
        $this->assertEmpty($differences);
    }

    public function testIgnoresDoctrineAutoIndexes(): void
    {
        $expectedStructure = [
            'tables' => [
                'test_table' => [
                    'columns' => ['id' => 'INT AUTO_INCREMENT', 'name' => 'VARCHAR(255)'],
                    'primary_key' => 'id',
                    'indexes' => [],
                    'foreign_keys' => [],
                ],
            ],
        ];

        $currentStructure = [
            'tables' => [
                'test_table' => [
                    'columns' => ['id' => 'INT AUTO_INCREMENT', 'name' => 'VARCHAR(255)'],
                    'primary_key' => 'id',
                    'indexes' => [
                        [
                            'name' => 'IDX_5BFB3362A3', // Índice automático de Doctrine
                            'columns' => ['name'],
                            'unique' => false,
                        ],
                        [
                            'name' => 'UNIQ_1234567890', // Índice único automático de Doctrine
                            'columns' => ['name'],
                            'unique' => true,
                        ],
                    ],
                    'foreign_keys' => [],
                ],
            ],
        ];

        $differences = $this->schemaComparator->compare($expectedStructure, $currentStructure);

        // Los índices automáticos de Doctrine deben ser ignorados
        $this->assertEmpty($differences);
    }
}
