<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Persistence\MigrationReader;

use App\V2\Infrastructure\Persistence\MigrationReader\MigrationReaderService;
use App\V2\Infrastructure\Persistence\SchemaAnalyzer\MigrationSchemaAnalyzerService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Filesystem\Filesystem;

class MigrationReaderServiceTest extends TestCase
{
    private string $tempDir;
    private MigrationReaderService $migrationReader;
    private MigrationSchemaAnalyzerService $schemaAnalyzer;
    private Filesystem $filesystem;

    protected function setUp(): void
    {
        $this->tempDir = sys_get_temp_dir() . '/migration_reader_test_' . uniqid();
        $this->filesystem = new Filesystem();
        $this->filesystem->mkdir($this->tempDir . '/src/Migrations');

        $this->migrationReader = new MigrationReaderService($this->tempDir);
        $this->schemaAnalyzer = new MigrationSchemaAnalyzerService();
    }

    protected function tearDown(): void
    {
        if ($this->filesystem->exists($this->tempDir)) {
            $this->filesystem->remove($this->tempDir);
        }
    }

    public function testReadAllMigrationsWithEmptyDirectory(): void
    {
        $migrations = $this->migrationReader->readAllMigrations();

        $this->assertIsArray($migrations);
        $this->assertEmpty($migrations);
    }

    public function testReadAllMigrationsWithValidMigration(): void
    {
        $migrationContent = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250101000000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Test migration';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE TABLE test_table (
                id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                PRIMARY KEY(id)
            )
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE test_table');
    }
}
PHP;

        $this->filesystem->dumpFile(
            $this->tempDir . '/src/Migrations/Version20250101000000.php',
            $migrationContent
        );

        $migrations = $this->migrationReader->readAllMigrations();

        $this->assertCount(1, $migrations);

        $migration = $migrations[0];
        $this->assertEquals('Version20250101000000', $migration['filename']);
        $this->assertEquals('Version20250101000000', $migration['class']);
        $this->assertEquals('Test migration', $migration['description']);
        $this->assertCount(1, $migration['up_sql']);
        $this->assertCount(1, $migration['down_sql']);

        $this->assertStringContainsString('CREATE TABLE test_table', $migration['up_sql'][0]);
        $this->assertStringContainsString('DROP TABLE test_table', $migration['down_sql'][0]);
    }

    public function testReadMigrationWithMultipleSqlStatements(): void
    {
        $migrationContent = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250101000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Multiple SQL statements';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE TABLE users (
                id INT NOT NULL,
                email VARCHAR(255) NOT NULL,
                PRIMARY KEY(id)
            )
        SQL);
        
        $this->addSql(<<<'SQL'
            ALTER TABLE users ADD CONSTRAINT FK_USERS_EMAIL UNIQUE (email)
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE users');
    }
}
PHP;

        $this->filesystem->dumpFile(
            $this->tempDir . '/src/Migrations/Version20250101000001.php',
            $migrationContent
        );

        $migrations = $this->migrationReader->readAllMigrations();

        $this->assertCount(1, $migrations);

        $migration = $migrations[0];
        $this->assertCount(2, $migration['up_sql']);
        $this->assertCount(1, $migration['down_sql']);

        $this->assertStringContainsString('CREATE TABLE users', $migration['up_sql'][0]);
        $this->assertStringContainsString('ALTER TABLE users', $migration['up_sql'][1]);
    }

    public function testReadMigrationWithNormalStringAddSql(): void
    {
        $migrationContent = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250101000002 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE simple_table (id INT NOT NULL)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE simple_table');
    }
}
PHP;

        $this->filesystem->dumpFile(
            $this->tempDir . '/src/Migrations/Version20250101000002.php',
            $migrationContent
        );

        $migrations = $this->migrationReader->readAllMigrations();

        $this->assertCount(1, $migrations);

        $migration = $migrations[0];
        $this->assertCount(1, $migration['up_sql']);
        $this->assertEquals('CREATE TABLE simple_table (id INT NOT NULL)', $migration['up_sql'][0]);
    }

    public function testIgnoreInvalidFiles(): void
    {
        $this->filesystem->dumpFile(
            $this->tempDir . '/src/Migrations/InvalidFile.php',
            '<?php echo "not a migration";'
        );

        $this->filesystem->dumpFile(
            $this->tempDir . '/src/Migrations/README.txt',
            'This is not a PHP file'
        );

        $migrations = $this->migrationReader->readAllMigrations();

        $this->assertEmpty($migrations);
    }

    public function testMigrationsAreSortedByName(): void
    {
        $migration1Content = $this->createSimpleMigration('Version20250101000000', 'First migration');
        $migration2Content = $this->createSimpleMigration('Version20250102000000', 'Second migration');
        $migration3Content = $this->createSimpleMigration('Version20250103000000', 'Third migration');

        $this->filesystem->dumpFile($this->tempDir . '/src/Migrations/Version20250103000000.php', $migration3Content);
        $this->filesystem->dumpFile($this->tempDir . '/src/Migrations/Version20250101000000.php', $migration1Content);
        $this->filesystem->dumpFile($this->tempDir . '/src/Migrations/Version20250102000000.php', $migration2Content);

        $migrations = $this->migrationReader->readAllMigrations();

        $this->assertCount(3, $migrations);
        $this->assertEquals('Version20250101000000', $migrations[0]['filename']);
        $this->assertEquals('Version20250102000000', $migrations[1]['filename']);
        $this->assertEquals('Version20250103000000', $migrations[2]['filename']);
    }

    private function createSimpleMigration(string $className, string $description): string
    {
        return <<<PHP
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class {$className} extends AbstractMigration
{
    public function getDescription(): string
    {
        return '{$description}';
    }

    public function up(Schema \$schema): void
    {
        \$this->addSql('CREATE TABLE test (id INT)');
    }

    public function down(Schema \$schema): void
    {
        \$this->addSql('DROP TABLE test');
    }
}
PHP;
    }

    public function testReadMigrationWithCreateTableIfNotExists(): void
    {
        $migrationContent = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250101000003 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Test migration with IF NOT EXISTS';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE TABLE IF NOT EXISTS test_table (
                id VARCHAR(36) NOT NULL,
                name VARCHAR(255) NOT NULL,
                PRIMARY KEY(id)
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE test_table');
    }
}
PHP;

        $this->filesystem->dumpFile(
            $this->tempDir . '/src/Migrations/Version20250101000003.php',
            $migrationContent
        );

        $migrations = $this->migrationReader->readAllMigrations();

        $this->assertCount(1, $migrations);

        $migration = $migrations[0];
        $this->assertEquals('Version20250101000003', $migration['filename']);
        $this->assertEquals('Test migration with IF NOT EXISTS', $migration['description']);
        $this->assertCount(1, $migration['up_sql']);
        $this->assertStringContainsString('CREATE TABLE IF NOT EXISTS test_table', $migration['up_sql'][0]);
        $this->assertCount(1, $migration['down_sql']);
        $this->assertEquals('DROP TABLE test_table', $migration['down_sql'][0]);
    }

    public function testMigrationSchemaAnalyzerDetectsBothCreateTableVariants(): void
    {
        $migrationContent = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250101000004 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE TABLE normal_table (
                id INT NOT NULL,
                name VARCHAR(255) NOT NULL
            )
        SQL);

        $this->addSql(<<<'SQL'
            CREATE TABLE IF NOT EXISTS if_not_exists_table (
                id VARCHAR(36) NOT NULL,
                title VARCHAR(255) NOT NULL
            )
        SQL);

        $this->addSql('CREATE TABLE   extra_spaces   (id INT NOT NULL)');
        $this->addSql('CREATE TABLE IF NOT EXISTS`backticks_table` (id INT NOT NULL)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE backticks_table');
        $this->addSql('DROP TABLE extra_spaces');
        $this->addSql('DROP TABLE if_not_exists_table');
        $this->addSql('DROP TABLE normal_table');
    }
}
PHP;

        $this->filesystem->dumpFile(
            $this->tempDir . '/src/Migrations/Version20250101000004.php',
            $migrationContent
        );

        $migrations = $this->migrationReader->readAllMigrations();

        $this->assertCount(1, $migrations);

        $migration = $migrations[0];
        $this->assertEquals('Version20250101000004', $migration['filename']);
        $this->assertCount(4, $migration['up_sql']);

        $this->assertStringContainsString('CREATE TABLE normal_table', $migration['up_sql'][0]);
        $this->assertStringContainsString('CREATE TABLE IF NOT EXISTS if_not_exists_table', $migration['up_sql'][1]);
        $this->assertStringContainsString('CREATE TABLE   extra_spaces', $migration['up_sql'][2]);
        $this->assertStringContainsString('CREATE TABLE IF NOT EXISTS`backticks_table`', $migration['up_sql'][3]);

        $analyzer = new MigrationSchemaAnalyzerService();
        $structure = $analyzer->analyzeStructure([$migration]);

        $this->assertCount(4, $structure['tables']);
        $this->assertArrayHasKey('normal_table', $structure['tables']);
        $this->assertArrayHasKey('if_not_exists_table', $structure['tables']);
        $this->assertArrayHasKey('extra_spaces', $structure['tables']);
        $this->assertArrayHasKey('backticks_table', $structure['tables']);

        $this->assertCount(2, $structure['tables']['normal_table']['columns']);
        $this->assertCount(2, $structure['tables']['if_not_exists_table']['columns']);
        $this->assertCount(1, $structure['tables']['extra_spaces']['columns']);
        $this->assertCount(1, $structure['tables']['backticks_table']['columns']);
    }

    public function testReadMigrationWithSingleLineSqlStatements(): void
    {
        $migrationContent = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250101000005 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE single_line_table (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, email VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE single_line_table');
    }
}
PHP;

        $this->filesystem->dumpFile(
            $this->tempDir . '/src/Migrations/Version20250101000005.php',
            $migrationContent
        );

        $migrations = $this->migrationReader->readAllMigrations();

        $this->assertCount(1, $migrations);

        $migration = $migrations[0];
        $this->assertEquals('Version20250101000005', $migration['filename']);
        $this->assertCount(1, $migration['up_sql']);
        $this->assertStringContainsString('CREATE TABLE single_line_table', $migration['up_sql'][0]);

        $analyzer = new MigrationSchemaAnalyzerService();
        $structure = $analyzer->analyzeStructure([$migration]);

        $this->assertCount(1, $structure['tables']);
        $this->assertArrayHasKey('single_line_table', $structure['tables']);

        $table = $structure['tables']['single_line_table'];
        $this->assertCount(4, $table['columns']); // id, name, email, created_at
        $this->assertArrayHasKey('id', $table['columns']);
        $this->assertArrayHasKey('name', $table['columns']);
        $this->assertArrayHasKey('email', $table['columns']);
        $this->assertArrayHasKey('created_at', $table['columns']);

        $this->assertEquals('id', $table['primary_key']);
    }

    public function testMigrationSchemaAnalyzerHandlesComplexAlterOperations(): void
    {
        $migrationContent1 = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250101000006 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE test_table (id INT AUTO_INCREMENT NOT NULL, old_name VARCHAR(255) NOT NULL, old_column INT NOT NULL, to_modify VARCHAR(100) NOT NULL, PRIMARY KEY(id))');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE test_table');
    }
}
PHP;

        $migrationContent2 = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250101000007 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE test_table ADD new_column VARCHAR(255) DEFAULT NULL, DROP old_column, CHANGE old_name new_name VARCHAR(255) NOT NULL, MODIFY to_modify TEXT NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE test_table DROP new_column, ADD old_column INT NOT NULL, CHANGE new_name old_name VARCHAR(255) NOT NULL, MODIFY to_modify VARCHAR(100) NOT NULL');
    }
}
PHP;

        $this->filesystem->dumpFile(
            $this->tempDir . '/src/Migrations/Version20250101000006.php',
            $migrationContent1
        );

        $this->filesystem->dumpFile(
            $this->tempDir . '/src/Migrations/Version20250101000007.php',
            $migrationContent2
        );

        $migrations = $this->migrationReader->readAllMigrations();

        $this->assertCount(2, $migrations);

        $analyzer = new MigrationSchemaAnalyzerService();
        $structure = $analyzer->analyzeStructure($migrations);

        $this->assertCount(1, $structure['tables']);
        $this->assertArrayHasKey('test_table', $structure['tables']);

        $table = $structure['tables']['test_table'];

        $this->assertCount(4, $table['columns']); // id, new_name, to_modify, new_column

        $this->assertArrayHasKey('id', $table['columns']); // Sin cambios
        $this->assertArrayHasKey('new_name', $table['columns']); // CHANGE old_name -> new_name
        $this->assertArrayHasKey('to_modify', $table['columns']); // MODIFY
        $this->assertArrayHasKey('new_column', $table['columns']); // ADD

        $this->assertArrayNotHasKey('old_name', $table['columns']); // Renombrada
        $this->assertArrayNotHasKey('old_column', $table['columns']); // Eliminada

        $this->assertEquals('TEXT NOT NULL', $table['columns']['to_modify']);

        $this->assertEquals('id', $table['primary_key']);
    }

    public function testMigrationSchemaAnalyzerHandlesUserCourseScenario(): void
    {
        $migrationContent1 = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241107181222 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE user_course (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, course_id INT NOT NULL, announcement_id INT DEFAULT NULL, started_at DATETIME NOT NULL, finished_at DATETIME DEFAULT NULL, valued_at DATETIME DEFAULT NULL, time_spent INT DEFAULT NULL, points NUMERIC(10, 0) DEFAULT NULL, timezone VARCHAR(255) DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, timezone_created_at DATETIME DEFAULT NULL, timezone_updated_at DATETIME DEFAULT NULL, INDEX IDX_73CC7484A76ED395 (user_id), INDEX IDX_73CC7484591CC992 (course_id), INDEX IDX_73CC7484913AEA17 (announcement_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_course_chapter (id INT AUTO_INCREMENT NOT NULL, user_course_id INT NOT NULL, chapter_id INT NOT NULL, started_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, finished_at DATETIME DEFAULT NULL, data JSON DEFAULT NULL, time_spent INT DEFAULT NULL, points NUMERIC(10, 0) DEFAULT NULL, ip VARCHAR(255) DEFAULT NULL, timezone VARCHAR(255) DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, timezone_created_at DATETIME DEFAULT NULL, timezone_updated_at DATETIME DEFAULT NULL, INDEX IDX_9AC76F9B59FC4476 (user_course_id), INDEX IDX_9AC76F9B579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE user_course');
        $this->addSql('DROP TABLE user_course_chapter');
    }
}
PHP;

        $migrationContent2 = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250428141115 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE user_course ADD created_by_id INT DEFAULT NULL, ADD updated_by_id INT DEFAULT NULL, ADD deleted_by_id INT DEFAULT NULL, ADD created_at DATETIME DEFAULT NULL, ADD updated_at DATETIME DEFAULT NULL, ADD deleted_at DATETIME DEFAULT NULL');
        $this->addSql('ALTER TABLE user_course_chapter ADD created_by_id INT DEFAULT NULL, ADD updated_by_id INT DEFAULT NULL, ADD deleted_by_id INT DEFAULT NULL, ADD created_at DATETIME DEFAULT NULL, ADD deleted_at DATETIME DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE user_course DROP created_by_id, DROP updated_by_id, DROP deleted_by_id, DROP created_at, DROP updated_at, DROP deleted_at');
        $this->addSql('ALTER TABLE user_course_chapter DROP created_by_id, DROP updated_by_id, DROP deleted_by_id, DROP created_at, DROP deleted_at');
    }
}
PHP;

        $this->filesystem->dumpFile(
            $this->tempDir . '/src/Migrations/Version20241107181222.php',
            $migrationContent1
        );

        $this->filesystem->dumpFile(
            $this->tempDir . '/src/Migrations/Version20250428141115.php',
            $migrationContent2
        );

        $migrations = $this->migrationReader->readAllMigrations();

        $this->assertCount(2, $migrations);

        // Verify that the schema analyzer correctly processes both migrations.
        $analyzer = new MigrationSchemaAnalyzerService();
        $structure = $analyzer->analyzeStructure($migrations);

        $this->assertCount(2, $structure['tables']);
        $this->assertArrayHasKey('user_course', $structure['tables']);
        $this->assertArrayHasKey('user_course_chapter', $structure['tables']);

        // Verify user_course
        $userCourseTable = $structure['tables']['user_course'];
        $this->assertCount(19, $userCourseTable['columns']); // 13 originales + 6 nuevas

        // Verify original columns
        $this->assertArrayHasKey('id', $userCourseTable['columns']);
        $this->assertArrayHasKey('user_id', $userCourseTable['columns']);
        $this->assertArrayHasKey('course_id', $userCourseTable['columns']);

        // Verify new columns
        $this->assertArrayHasKey('created_by_id', $userCourseTable['columns']);
        $this->assertArrayHasKey('updated_by_id', $userCourseTable['columns']);
        $this->assertArrayHasKey('deleted_by_id', $userCourseTable['columns']);
        $this->assertArrayHasKey('created_at', $userCourseTable['columns']);
        $this->assertArrayHasKey('updated_at', $userCourseTable['columns']);
        $this->assertArrayHasKey('deleted_at', $userCourseTable['columns']);

        // Verify user_course_chapter
        $userCourseChapterTable = $structure['tables']['user_course_chapter'];
        $this->assertCount(19, $userCourseChapterTable['columns']); // 14 originales + 5 nuevas

        // Verify new columns
        $this->assertArrayHasKey('created_by_id', $userCourseChapterTable['columns']);
        $this->assertArrayHasKey('updated_by_id', $userCourseChapterTable['columns']);
        $this->assertArrayHasKey('deleted_by_id', $userCourseChapterTable['columns']);
        $this->assertArrayHasKey('created_at', $userCourseChapterTable['columns']);
        $this->assertArrayHasKey('deleted_at', $userCourseChapterTable['columns']);

        // Verify that updated_at is NOT present in the new columns (only user_course has it as a new column)
        // user_course_chapter already had updated_at as an original column
        $this->assertArrayHasKey('updated_at', $userCourseChapterTable['columns']); // Columna original
    }

    public function testMigrationSchemaAnalyzerHandlesInlineUniqueIndexes(): void
    {
        $migrationContent = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250101000007 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE alert_type_tutor_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_EC131BA42C2AC5D3 (translatable_id), UNIQUE INDEX alert_type_tutor_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_configuration_type_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_E60E04A22C2AC5D3 (translatable_id), UNIQUE INDEX announcement_configuration_type_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE alert_type_tutor_translation');
        $this->addSql('DROP TABLE announcement_configuration_type_translation');
    }
}
PHP;

        $migrationFile = $this->tempDir . '/src/Migrations/Version20250101000007.php';
        file_put_contents($migrationFile, $migrationContent);

        $migrations = $this->migrationReader->readAllMigrations();
        $this->assertCount(1, $migrations);

        $migration = $migrations[0];
        $this->assertEquals('Version20250101000007', $migration['class']);
        $this->assertCount(2, $migration['up_sql']);

        // Verify that the unique indexes are correctly extracted
        $sql1 = $migration['up_sql'][0];
        $sql2 = $migration['up_sql'][1];

        $this->assertStringContainsString('alert_type_tutor_translation_unique_translation', $sql1);
        $this->assertStringContainsString('announcement_configuration_type_translation_unique_translation', $sql2);
        $this->assertStringContainsString('UNIQUE INDEX', $sql1);
        $this->assertStringContainsString('UNIQUE INDEX', $sql2);
        $this->assertStringContainsString('(translatable_id, locale)', $sql1);
        $this->assertStringContainsString('(translatable_id, locale)', $sql2);
    }

    public function testMigrationSchemaAnalyzerHandlesUnnamedForeignKeys(): void
    {
        $migrationContent = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250101000008 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            CREATE TABLE course_creator (
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                PRIMARY KEY(user_id, course_id),
                FOREIGN KEY (user_id) REFERENCES `user` (id) ON DELETE CASCADE ,
                FOREIGN KEY (course_id) REFERENCES `course`(id) ON DELETE CASCADE
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE course_creator');
    }
}
PHP;

        $migrationFile = $this->tempDir . '/src/Migrations/Version20250101000008.php';
        file_put_contents($migrationFile, $migrationContent);

        $migrations = $this->migrationReader->readAllMigrations();
        $this->assertCount(1, $migrations);

        $migration = $migrations[0];
        $this->assertEquals('Version20250101000008', $migration['class']);
        $this->assertCount(1, $migration['up_sql']);

        $sql = $migration['up_sql'][0];
        $this->assertStringContainsString('FOREIGN KEY (user_id) REFERENCES `user` (id)', $sql);
        $this->assertStringContainsString('FOREIGN KEY (course_id) REFERENCES `course`(id)', $sql);
        $this->assertStringContainsString('ON DELETE CASCADE', $sql);
    }

    public function testMigrationReaderHandlesMultilineHeredocCorrectly(): void
    {
        // Crear una migración con heredoc multilinea como lti_chapter
        $migrationContent = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250101000009 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE `lti_chapter` ADD lti_tool_identifier_id VARCHAR(36) DEFAULT NULL,
               ADD CONSTRAINT FK_LTI_CHAPTER_LTI_TOOL_V2_ID FOREIGN KEY (lti_tool_identifier_id) REFERENCES
               `lti_tool_v2`(id) ON DELETE RESTRICT
        SQL);

        $this->addSql(<<<'SQL'
            ALTER TABLE lti_chapter MODIFY lti_tool_id INT NULL DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE lti_chapter');
    }
}
PHP;

        $migrationFile = $this->tempDir . '/src/Migrations/Version20250101000009.php';
        file_put_contents($migrationFile, $migrationContent);

        $migrations = $this->migrationReader->readAllMigrations();
        $this->assertCount(1, $migrations);

        $migration = $migrations[0];
        $this->assertEquals('Version20250101000009', $migration['class']);
        $this->assertCount(2, $migration['up_sql']);

        $sql1 = $migration['up_sql'][0];
        $this->assertStringContainsString('ADD lti_tool_identifier_id VARCHAR(36) DEFAULT NULL', $sql1);
        $this->assertStringContainsString('ADD CONSTRAINT FK_LTI_CHAPTER_LTI_TOOL_V2_ID', $sql1);
        $this->assertStringContainsString('FOREIGN KEY (lti_tool_identifier_id) REFERENCES', $sql1);
        $this->assertStringContainsString('`lti_tool_v2`(id) ON DELETE RESTRICT', $sql1);

        $sql2 = $migration['up_sql'][1];
        $this->assertStringContainsString('ALTER TABLE lti_chapter MODIFY lti_tool_id', $sql2);
        $this->assertStringNotContainsString('FK_LTI_CHAPTER_LTI_TOOL_V2_ID', $sql2);
    }

    public function testMigrationSchemaAnalyzerHandlesForeignKeyIndexes(): void
    {
        $migrationContent = <<<'PHP'
<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250101000010 extends AbstractMigration
{
    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
            ALTER TABLE `test_table` ADD CONSTRAINT FK_TEST_FOREIGN_KEY FOREIGN KEY (user_id) REFERENCES `user` (id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE test_table');
    }
}
PHP;

        $migrationFile = $this->tempDir . '/src/Migrations/Version20250101000010.php';
        file_put_contents($migrationFile, $migrationContent);

        $migrations = $this->migrationReader->readAllMigrations();
        $structure = $this->schemaAnalyzer->analyzeStructure($migrations);

        $this->assertArrayHasKey('test_table', $structure['tables']);
        $testTable = $structure['tables']['test_table'];

        $this->assertCount(1, $testTable['foreign_keys']);
        $foreignKey = $testTable['foreign_keys'][0];
        $this->assertEquals('FK_TEST_FOREIGN_KEY', $foreignKey['name']);
        $this->assertEquals('user_id', $foreignKey['local_column']);
        $this->assertEquals('user', $foreignKey['foreign_table']);
        $this->assertEquals('id', $foreignKey['foreign_column']);

        $this->assertCount(0, $testTable['indexes']);
    }
}
