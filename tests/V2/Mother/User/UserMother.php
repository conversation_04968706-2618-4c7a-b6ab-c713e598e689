<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\User;

use App\Tests\V2\Mother\Shared\Email\EmailMother;
use App\V2\Domain\Shared\Email\Email;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\User;

class UserMother
{
    public const string DEFAULT_CODE = 'USER-1';
    public const string DEFAULT_FIRSTNAME = 'John';
    public const string DEFAULT_LASTNAME = 'Doe';
    public const string DEFAULT_LOCALE = 'es';
    public const string DEFAULT_PASSWORD = '12345678';
    public const string DEFAULT_TIMEZONE = 'Europe/Madrid';

    /**
     * @param string[] $roles
     * @param string[] $remoteRoles
     */
    public static function create(
        ?Id $id = null,
        ?Email $email = null,
        string $password = self::DEFAULT_PASSWORD,
        array $roles = [],
        array $remoteRoles = [],
        string $code = self::DEFAULT_CODE,
        string $firstName = self::DEFAULT_FIRSTNAME,
        string $lastName = self::DEFAULT_LASTNAME,
        bool $isActive = false,
        bool $validated = false,
        bool $open = false,
        string $locale = self::DEFAULT_LOCALE,
        string $localeCampus = self::DEFAULT_LOCALE,
        Id $createdById = new Id(1),
        \DateTimeImmutable $createdAt = new \DateTimeImmutable(),
        bool $starteam = false,
        string $timezone = self::DEFAULT_TIMEZONE,
        ?Id $updatedById = null,
        ?\DateTimeImmutable $updatedAt = null,
        ?Id $deletedById = null,
        ?\DateTimeImmutable $deletedAt = null,
    ): User {
        return new User(
            id: $id ?? new Id(random_int(1, 1000)),
            email: $email ?? EmailMother::create('<EMAIL>'),
            password: $password,
            roles: $roles,
            remoteRoles: $remoteRoles,
            code: $code,
            firstName: $firstName,
            lastName: $lastName,
            isActive: $isActive,
            validated: $validated,
            open: $open,
            locale: $locale,
            localeCampus: $localeCampus,
            timezone: $timezone,
            createdById: $createdById,
            createdAt: $createdAt,
            starteam: $starteam,
            updatedById: $updatedById,
            updatedAt: $updatedAt,
            deletedById: $deletedById,
            deletedAt: $deletedAt,
        );
    }
}
