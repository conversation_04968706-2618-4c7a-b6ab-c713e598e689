<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Payment;

use App\V2\Application\DTO\Payment\PaymentConfirmationInputDTO;

final class PaymentConfirmationInputDTOMother
{
    public static function create(
        ?string $payload = null,
        ?array $metadata = null,
    ): PaymentConfirmationInputDTO {
        return new PaymentConfirmationInputDTO(
            payload: $payload ?? '{"id":"evt_test_default","object":"event","type":"payment_intent.succeeded"}',
            metadata: $metadata ?? [
                'signature' => 'whsec_default_signature_123',
            ],
        );
    }
}
