<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\Filter;

use App\V2\Domain\Filter\FilterCategory\FilterCategory;
use App\V2\Domain\Shared\Id\Id;

class FilterCategoryMother
{
    public const string DEFAULT_NAME = 'Category';

    public static function create(
        ?Id $id = null,
        ?Id $parentId = null,
        string $name = self::DEFAULT_NAME,
        int $sort = 0,
    ): FilterCategory {
        return new FilterCategory(
            id: $id ?? new Id(random_int(1, 10)),
            parentId: $parentId,
            name: $name,
            sort: $sort,
        );
    }
}
