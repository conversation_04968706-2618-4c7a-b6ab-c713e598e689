<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Persistence;

use App\V2\Application\Persistence\TransactionManagerInterface;
use PHPUnit\Framework\TestCase;

abstract class TransactionManagerInterfaceTestCase extends TestCase
{
    abstract protected function getTransactionManager(): TransactionManagerInterface;

    public function testBeginTransaction(): void
    {
        $transactionManager = $this->getTransactionManager();

        $transactionManager->beginTransaction();

        // Test passes if no exception is thrown
        $this->assertTrue(true);
    }

    public function testCommit(): void
    {
        $transactionManager = $this->getTransactionManager();

        $transactionManager->beginTransaction();
        $transactionManager->commit();

        // Test passes if no exception is thrown
        $this->assertTrue(true);
    }

    public function testRollback(): void
    {
        $transactionManager = $this->getTransactionManager();

        $transactionManager->beginTransaction();
        $transactionManager->rollback();

        // Test passes if no exception is thrown
        $this->assertTrue(true);
    }

    public function testTransactionalWithSuccessfulOperation(): void
    {
        $transactionManager = $this->getTransactionManager();
        $expectedResult = 'test_result';

        $result = $transactionManager->transactional(function () use ($expectedResult) {
            return $expectedResult;
        });

        $this->assertSame($expectedResult, $result);
    }

    public function testTransactionalWithFailingOperation(): void
    {
        $transactionManager = $this->getTransactionManager();
        $expectedException = new \RuntimeException('Test exception');

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Test exception');

        $transactionManager->transactional(function () use ($expectedException) {
            throw $expectedException;
        });
    }

    public function testTransactionalReturnsCallableResult(): void
    {
        $transactionManager = $this->getTransactionManager();
        $testData = ['key' => 'value', 'number' => 42];

        $result = $transactionManager->transactional(function () use ($testData) {
            return $testData;
        });

        $this->assertSame($testData, $result);
    }

    public function testTransactionalWithNullReturn(): void
    {
        $transactionManager = $this->getTransactionManager();

        $result = $transactionManager->transactional(function () {
            return null;
        });

        $this->assertNull($result);
    }

    public function testTransactionalWithVoidOperation(): void
    {
        $transactionManager = $this->getTransactionManager();
        $executed = false;

        $result = $transactionManager->transactional(function () use (&$executed) {
            $executed = true;
        });

        $this->assertTrue($executed);
        $this->assertNull($result);
    }
}
