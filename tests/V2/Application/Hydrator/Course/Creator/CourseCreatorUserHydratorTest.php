<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Hydrator\Course\Creator;

use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\Course\Creator\CourseCreatorMother;
use App\V2\Application\Hydrator\Course\Creator\CourseCreatorUserHydrator;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Course\Creator\CourseCreatorHydrationCriteria;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Hydrator\HydratorException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\LegacyUserCollection;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class CourseCreatorUserHydratorTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHydrator(
        ?UserRepository $userRepository = null,
    ): CourseCreatorUserHydrator {
        return new CourseCreatorUserHydrator(
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
        );
    }

    /**
     * @throws Exception
     */
    public function testPriority(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertEquals(HydratorPriority::First, $hydrator->getPriority());
    }

    /**
     * @throws Exception
     */
    public function testSupports(): void
    {
        $hydrator = $this->getHydrator();
        $this->assertTrue(
            $hydrator->supports(
                CourseCreatorHydrationCriteria::createEmpty()->withUser()
            )
        );

        $this->assertFalse(
            $hydrator->supports(
                CourseCreatorHydrationCriteria::createEmpty()
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws HydratorException
     * @throws CollectionException
     * @throws Exception
     */
    public function testEmptyCollection(): void
    {
        $collection = new CourseCreatorCollection([]);
        $hydrator = $this->getHydrator();
        $hydrator->hydrate(
            $collection,
            CourseCreatorHydrationCriteria::createEmpty()->withUser()
        );

        $this->assertEmpty($collection->all());
    }

    /**
     * @throws InfrastructureException
     * @throws HydratorException
     * @throws Exception
     * @throws CollectionException
     */
    public function testHydrateWithUser(): void
    {
        $courseCreator1 = CourseCreatorMother::create(userId: new Id(1), courseId: new Id(1));
        $courseCreator2 = CourseCreatorMother::create(userId: new Id(2), courseId: new Id(2));
        $courseCreator3 = CourseCreatorMother::create(userId: new Id(1), courseId: new Id(3));
        $courseCreator4 = CourseCreatorMother::create(userId: new Id(2), courseId: new Id(4));

        $user1 = UserMother::create(id: 1);
        $user2 = UserMother::create(id: 2);

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new LegacyUserCollection([$user1, $user2]));

        $hydrator = $this->getHydrator(
            userRepository: $userRepository
        );

        $hydrator->hydrate(
            collection: new CourseCreatorCollection([
                $courseCreator1, $courseCreator2, $courseCreator3,  $courseCreator4,
            ]),
            criteria: CourseCreatorHydrationCriteria::createEmpty()->withUser()
        );

        $this->assertNotNull($courseCreator1->getCreator());
        $this->assertNotNull($courseCreator2->getCreator());
        $this->assertNotNull($courseCreator3->getCreator());
        $this->assertNotNull($courseCreator4->getCreator());

        $this->assertEquals($user1->getId(), $courseCreator1->getCreator()->getId()->value());
        $this->assertEquals($user1->getEmail(), $courseCreator1->getCreator()->getEmail()->value());

        $this->assertEquals($user2->getId(), $courseCreator2->getCreator()->getId()->value());
        $this->assertEquals($user2->getEmail(), $courseCreator2->getCreator()->getEmail()->value());

        $this->assertEquals($user1->getId(), $courseCreator3->getCreator()->getId()->value());
        $this->assertEquals($user1->getEmail(), $courseCreator3->getCreator()->getEmail()->value());

        $this->assertEquals($user2->getId(), $courseCreator4->getCreator()->getId()->value());
        $this->assertEquals($user2->getEmail(), $courseCreator4->getCreator()->getEmail()->value());
    }
}
