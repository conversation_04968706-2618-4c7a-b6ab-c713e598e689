<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\V2\Application\Command\ProcessPaymentConfirmation;
use App\V2\Application\CommandHandler\ProcessPaymentConfirmationHandler;
use App\V2\Application\DTO\Payment\PaymentConfirmationInputDTO;
use App\V2\Domain\Purchase\Payment\PaymentManagerInterface;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class ProcessPaymentConfirmationHandlerTest extends TestCase
{
    private PaymentManagerInterface&MockObject $paymentManager;
    private ProcessPaymentConfirmationHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->paymentManager = $this->createMock(PaymentManagerInterface::class);
        $this->handler = new ProcessPaymentConfirmationHandler(
            paymentManager: $this->paymentManager,
        );
    }

    private function getCommand(): ProcessPaymentConfirmation
    {
        $dto = new PaymentConfirmationInputDTO(
            payload: '{"type":"payment_intent.succeeded"}',
            metadata: ['signature' => 't=1234567890,v1=test_signature'],
        );

        return new ProcessPaymentConfirmation($dto);
    }

    /**
     * @throws Exception
     */
    public function testIsInstantiable(): void
    {
        $handler = new ProcessPaymentConfirmationHandler(
            paymentManager: $this->paymentManager,
        );

        self::assertInstanceOf(ProcessPaymentConfirmationHandler::class, $handler);
    }

    public function testHandleMethodExists(): void
    {
        $this->assertTrue(method_exists(ProcessPaymentConfirmationHandler::class, 'handle'));
        $this->assertTrue(\is_callable([$this->handler, 'handle']));
    }

    public function testHandleCallsPaymentManagerProcessWebhook(): void
    {
        $command = $this->getCommand();

        $this->paymentManager
            ->expects($this->once())
            ->method('processWebhook')
            ->with(
                '{"type":"payment_intent.succeeded"}',
                ['signature' => 't=1234567890,v1=test_signature'],
            );

        $this->handler->handle($command);
    }

    public function testHandleReturnsVoid(): void
    {
        $command = $this->getCommand();

        $this->paymentManager
            ->expects($this->once())
            ->method('processWebhook')
            ->with(
                '{"type":"payment_intent.succeeded"}',
                ['signature' => 't=1234567890,v1=test_signature'],
            );

        $result = $this->handler->handle($command);

        $this->assertNull($result);
    }

    public function testConstructorInitializesCorrectly(): void
    {
        $paymentManager = $this->createMock(PaymentManagerInterface::class);
        $handler = new ProcessPaymentConfirmationHandler(
            paymentManager: $paymentManager,
        );

        $this->assertInstanceOf(ProcessPaymentConfirmationHandler::class, $handler);
    }

    public function testHandleWithMultipleCalls(): void
    {
        $command = $this->getCommand();

        $this->paymentManager
            ->expects($this->exactly(3))
            ->method('processWebhook')
            ->with(
                '{"type":"payment_intent.succeeded"}',
                ['signature' => 't=1234567890,v1=test_signature'],
            );

        // Call handle multiple times to ensure it works consistently
        $this->handler->handle($command);
        $this->handler->handle($command);
        $this->handler->handle($command);
    }
}
