<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\Service\Announcement;

use App\Entity\Announcement;
use App\Entity\MaterialCourse;
use App\Entity\TaskCourse;
use App\Entity\User;
use App\Repository\AnnouncementTutorRepository;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\AnnouncementMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\MaterialCourseMother;
use App\Tests\Mother\Entity\TaskCourseMother;
use App\Tests\Mother\Entity\UserMother;
use App\Tests\V2\Mother\Announcement\Manager\AnnouncementManagerMother;
use App\Tests\V2\Mother\Course\Manager\CourseManagerMother;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationService;
use App\V2\Domain\Announcement\Exception\AnnouncementManagerNotFoundException;
use App\V2\Domain\Announcement\Exception\UserNotAuthorizedException;
use App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository;
use App\V2\Domain\Course\Manager\CourseManagerCollection;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class AnnouncementAuthorizationServiceTest extends TestCase
{
    private function getService(
        ?AnnouncementManagerRepository $announcementManagerRepository = null,
        ?CourseManagerRepository $courseManagerRepository = null,
        ?SettingsService $settingsService = null,
        ?AnnouncementTutorRepository $announcementTutorRepository = null,
    ): AnnouncementAuthorizationService {
        return new AnnouncementAuthorizationService(
            announcementManagerRepository: $announcementManagerRepository
                ?? $this->createMock(AnnouncementManagerRepository::class),
            courseManagerRepository: $courseManagerRepository ?? $this->createMock(CourseManagerRepository::class),
            settingsService: $settingsService ?? $this->createMock(SettingsService::class),
            announcementTutorRepository: $announcementTutorRepository ?? $this->createMock(AnnouncementTutorRepository::class),
        );
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    public function testAsAdmin(): void
    {
        $this->expectNotToPerformAssertions();
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_ADMIN]);
        $announcement = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));
        $service = $this->getService();
        $service->ensureUserCanManageAnnouncement(user: $user, announcement: $announcement);
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    public function testAsCreator(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_CREATOR]);
        $announcement = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));

        $service = $this->getService();
        $this->expectExceptionObject(
            UserNotAuthorizedException::userNotAuthorized(
                announcement: $announcement,
                user: $user->getEmail()
            )
        );
        $service->ensureUserCanManageAnnouncement(user: $user, announcement: $announcement);
    }

    public static function provideAsManager(): \Generator
    {
        $user1 = UserMother::create(id: 1, email: '<EMAIL>', roles: [User::ROLE_MANAGER]);
        $announcement1 = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1), createdBy: $user1);
        yield 'is the creator' => [
            'user' => $user1,
            'announcement' => $announcement1,
            'sharing' => null,
            'announcementManagerRepositoryFindOneBy' => null,
            'exception' => null,
        ];

        $announcement2 = AnnouncementMother::create(
            id: 1,
            course: CourseMother::create(id: 1),
            createdBy: UserMother::create(id: 2)
        );
        yield 'not the creator and sharing disabled' => [
            'user' => $user1,
            'announcement' => $announcement2,
            'sharing' => false,
            'announcementManagerRepositoryFindOneBy' => null,
            'exception' => UserNotAuthorizedException::userNotAuthorized(
                announcement: $announcement2,
                user: '<EMAIL>'
            ),
        ];

        yield 'not the creator and sharing enabled with access' => [
            'user' => $user1,
            'announcement' => $announcement2,
            'sharing' => true,
            'announcementManagerRepositoryFindOneBy' => fn () => AnnouncementManagerMother::create(
                userId: new Id(1),
                announcementId: new Id(2),
            ),
            'exception' => null,
        ];

        yield 'not the creator and sharing enabled without access' => [
            'user' => $user1,
            'announcement' => $announcement2,
            'sharing' => true,
            'announcementManagerRepositoryFindOneBy' => fn () => throw new AnnouncementManagerNotFoundException(),
            'exception' => UserNotAuthorizedException::userNotAuthorized(
                announcement: $announcement2,
                user: '<EMAIL>'
            ),
        ];
    }

    public static function provideAsManagerMaterialsHandle(): \Generator
    {
        $user1 = UserMother::create(id: 1, email: '<EMAIL>', roles: [User::ROLE_MANAGER]);
        $course = CourseMother::create(id: 1);
        $announcement1 = AnnouncementMother::create(id: 1, course: $course, createdBy: $user1);
        $materialCourse1 = MaterialCourseMother::create(course: $course, announcement: $announcement1);
        yield 'is the creator' => [
            'user' => $user1,
            'materialCourse' => $materialCourse1,
            'sharing' => null,
            'announcementManagerRepositoryFindOneBy' => null,
            'exception' => null,
        ];

        $announcement2 = AnnouncementMother::create(
            id: 1,
            course: $course,
            createdBy: UserMother::create(id: 2)
        );
        $materialCourse2 = MaterialCourseMother::create(course: $course, announcement: $announcement2);
        yield 'not the creator and sharing disabled' => [
            'user' => $user1,
            'materialCourse' => $materialCourse2,
            'sharing' => false,
            'announcementManagerRepositoryFindOneBy' => null,
            'exception' => UserNotAuthorizedException::userNotAuthorizedToHandleMaterial(
                announcement: $announcement2,
                user: '<EMAIL>'
            ),
        ];

        yield 'not the creator and sharing enabled with access' => [
            'user' => $user1,
            'materialCourse' => $materialCourse2,
            'sharing' => true,
            'announcementManagerRepositoryFindOneBy' => fn () => AnnouncementManagerMother::create(
                userId: new Id(1),
                announcementId: new Id(2),
            ),
            'exception' => null,
        ];

        yield 'not the creator and sharing enabled without access' => [
            'user' => $user1,
            'materialCourse' => $materialCourse2,
            'sharing' => true,
            'announcementManagerRepositoryFindOneBy' => fn () => throw new AnnouncementManagerNotFoundException(),
            'exception' => UserNotAuthorizedException::userNotAuthorizedToHandleMaterial(
                announcement: $announcement2,
                user: '<EMAIL>'
            ),
        ];
    }

    public static function provideAsTutor(): \Generator
    {
        $user1 = UserMother::create(id: 1, email: '<EMAIL>', roles: [User::ROLE_TUTOR]);
        $announcement1 = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));

        yield 'is shared with tutor' => [
            'user' => $user1,
            'announcement' => $announcement1,
            'isShared' => true,
            'shouldThrow' => false,
        ];

        $user2 = UserMother::create(id: 2, email: '<EMAIL>', roles: [User::ROLE_TUTOR]);
        $announcement2 = AnnouncementMother::create(id: 2, course: CourseMother::create(id: 2));

        yield 'is NOT shared with tutor' => [
            'user' => $user2,
            'announcement' => $announcement2,
            'isShared' => false,
            'shouldThrow' => true,
        ];
    }

    public static function provideAsTutorMaterialsHandle(): \Generator
    {
        $user1 = UserMother::create(id: 1, email: '<EMAIL>', roles: [User::ROLE_TUTOR]);
        $course1 = CourseMother::create(id: 1);
        $announcement1 = AnnouncementMother::create(id: 1, course: $course1);
        $materialCourse1 = MaterialCourseMother::create(course: $course1, announcement: $announcement1);

        yield 'is shared with tutor and materials are not created by him' => [
            'user' => $user1,
            'announcement' => $announcement1,
            'materialCourse' => $materialCourse1,
            'isShared' => true,
            'shouldThrow' => true,
        ];

        $user2 = UserMother::create(id: 2, email: '<EMAIL>', roles: [User::ROLE_TUTOR]);
        $course2 = CourseMother::create(id: 2);
        $announcement2 = AnnouncementMother::create(id: 2, course: $course2);
        $materialCourse2 = MaterialCourseMother::create(course: $course2, announcement: $announcement2);

        yield 'is NOT shared with tutor' => [
            'user' => $user2,
            'announcement' => $announcement2,
            'materialCourse' => $materialCourse2,
            'isShared' => false,
            'shouldThrow' => true,
        ];

        $user3 = UserMother::create(id: 3, email: '<EMAIL>', roles: [User::ROLE_TUTOR]);
        $course3 = CourseMother::create(id: 3);
        $announcement3 = AnnouncementMother::create(id: 3, course: $course3);
        $materialCourse3 = MaterialCourseMother::create(course: $course3, announcement: $announcement3, createdBy: $user3);

        yield 'is shared with tutor and materials are created by him' => [
            'user' => $user3,
            'announcement' => $announcement3,
            'materialCourse' => $materialCourse3,
            'isShared' => true,
            'shouldThrow' => false,
        ];
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    #[DataProvider('provideAsManager')]
    public function testAsManager(
        User $user,
        Announcement $announcement,
        ?bool $sharing,
        ?callable $announcementManagerRepositoryFindOneBy,
        ?\Exception $exception = null,
    ): void {
        if (null === $sharing && null === $announcementManagerRepositoryFindOneBy && null === $exception) {
            $this->expectNotToPerformAssertions();
        }

        $settingsService = $this->createMock(SettingsService::class);
        if (null !== $sharing) {
            $settingsService->expects($this->once())
                ->method('get')
                ->with('app.announcement.managers.sharing')
                ->willReturn($sharing);
        }
        $announcementManagerRepository = $this->createMock(AnnouncementManagerRepository::class);

        if (null !== $announcementManagerRepositoryFindOneBy) {
            $announcementManagerRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($announcementManagerRepositoryFindOneBy);
        }

        $service = $this->getService(
            announcementManagerRepository: $announcementManagerRepository,
            settingsService: $settingsService,
        );
        if (null !== $exception) {
            $this->expectExceptionObject(
                $exception
            );
        }
        $service->ensureUserCanManageAnnouncement(user: $user, announcement: $announcement);
    }

    /**
     * @throws UserNotAuthorizedException
     * @throws InfrastructureException
     */
    public function testEnsureUserCanManageAnnouncementAsAdmin(): void
    {
        $this->expectNotToPerformAssertions();
        $service = $this->getService();

        $service->ensureUserCanCreateAnnouncement(
            user: UserMother::create(id: 1, roles: [User::ROLE_ADMIN]),
            course: CourseMother::create(id: 1),
        );
    }

    /**
     * @throws UserNotAuthorizedException
     * @throws CollectionException
     * @throws InfrastructureException
     */
    public function testEnsureUserCanManageAnnouncementAsManagerWithoutSharedCourses(): void
    {
        $courseManagerRepository = $this->createMock(CourseManagerRepository::class);
        $courseManagerRepository->expects($this->once())
            ->method('findBy')
            ->willReturn(new CourseManagerCollection([]));

        $service = $this->getService(
            courseManagerRepository: $courseManagerRepository,
        );

        $service->ensureUserCanCreateAnnouncement(
            user: UserMother::create(id: 1, roles: [User::ROLE_MANAGER]),
            course: CourseMother::create(id: 1),
        );
    }

    /**
     * @throws UserNotAuthorizedException
     * @throws CollectionException
     * @throws InfrastructureException
     */
    public function testEnsureUserCanManageAnnouncementAsManagerWithSharedCourses(): void
    {
        $course1 = CourseMother::create(id: 1);
        $course2 = CourseMother::create(id: 2);
        $user = UserMother::create(id: 1, email: '<EMAIL>', roles: [User::ROLE_MANAGER]);

        $courseManagerRepository = $this->createMock(CourseManagerRepository::class);
        $courseManagerRepository->expects($this->exactly(2))
            ->method('findBy')
            ->willReturn(new CourseManagerCollection([
                CourseManagerMother::create(
                    userId: new Id(1),
                    courseId: new Id(1),
                ),
            ]));

        $service = $this->getService(
            courseManagerRepository: $courseManagerRepository,
        );

        // Try shared course
        $service->ensureUserCanCreateAnnouncement(
            user: $user,
            course: $course1,
        );

        $this->expectExceptionObject(
            UserNotAuthorizedException::userNoPermissionsToCreateAnnouncementForCourse(
                course: $course2,
                email: $user->getEmail(),
            )
        );

        // Try not shared course
        $service->ensureUserCanCreateAnnouncement(
            user: $user,
            course: $course2,
        );
    }

    public function testEnsureUserCanCreateMaterialAsAdmin(): void
    {
        $this->expectNotToPerformAssertions();
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_ADMIN]);
        $announcement = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));
        $service = $this->getService();
        $service->ensureUserCanManageAnnouncementResource(user: $user, announcement: $announcement);
    }

    public function testEnsureUserCanCreateMaterialAsCreator(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_CREATOR]);
        $announcement = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));

        $service = $this->getService();
        $this->expectExceptionObject(
            UserNotAuthorizedException::userNotAuthorized(
                announcement: $announcement,
                user: $user->getEmail()
            )
        );
        $service->ensureUserCanManageAnnouncementResource(user: $user, announcement: $announcement);
    }

    #[DataProvider('provideAsManager')]
    public function testEnsureUserCanCreateMaterialAsSharedManager(
        User $user,
        Announcement $announcement,
        ?bool $sharing,
        ?callable $announcementManagerRepositoryFindOneBy,
        ?\Exception $exception = null,
    ): void {
        if (null === $sharing && null === $announcementManagerRepositoryFindOneBy && null === $exception) {
            $this->expectNotToPerformAssertions();
        }

        $settingsService = $this->createMock(SettingsService::class);
        if (null !== $sharing) {
            $settingsService->expects($this->once())
                ->method('get')
                ->with('app.announcement.managers.sharing')
                ->willReturn($sharing);
        }
        $announcementManagerRepository = $this->createMock(AnnouncementManagerRepository::class);

        if (null !== $announcementManagerRepositoryFindOneBy) {
            $announcementManagerRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($announcementManagerRepositoryFindOneBy);
        }

        $service = $this->getService(
            announcementManagerRepository: $announcementManagerRepository,
            settingsService: $settingsService,
        );
        if (null !== $exception) {
            $this->expectExceptionObject(
                $exception
            );
        }
        $service->ensureUserCanManageAnnouncementResource(user: $user, announcement: $announcement);
    }

    #[DataProvider('provideAsTutor')]
    public function testEnsureUserCanCreateMaterialAsSharedTutor(
        User $user,
        Announcement $announcement,
        bool $isShared,
        bool $shouldThrow
    ): void {
        $announcementTutorRepository = $this->createMock(AnnouncementTutorRepository::class);
        $announcementTutorRepository->method('isTutorSharedWithAnnouncement')
            ->with($user, $announcement)
            ->willReturn($isShared);

        $service = $this->getService(
            announcementTutorRepository: $announcementTutorRepository,
        );

        if ($shouldThrow) {
            $this->expectExceptionObject(
                UserNotAuthorizedException::userNotAuthorized(
                    announcement: $announcement,
                    user: $user->getEmail()
                )
            );
        } else {
            $this->expectNotToPerformAssertions();
        }
        $service->ensureUserCanManageAnnouncementResource(user: $user, announcement: $announcement);
    }

    public function testEnsureUserCanCreateMaterialThrowsExceptionForUnauthorized(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_USER]);
        $announcement = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));
        $service = $this->getService();
        $this->expectExceptionObject(
            UserNotAuthorizedException::userNotAuthorized(
                announcement: $announcement,
                user: $user->getEmail()
            )
        );
        $service->ensureUserCanManageAnnouncement(user: $user, announcement: $announcement);
    }

    public function testEnsureUserCanHandleMaterialAsAdmin(): void
    {
        $this->expectNotToPerformAssertions();
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_ADMIN]);
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course);
        $materialCourse = MaterialCourseMother::create(course: $course, announcement: $announcement);
        $service = $this->getService();
        $service->ensureUserCanManageAnnouncementMaterials(user: $user, materialCourse: $materialCourse);
    }

    public function testEnsureUserCanHandleMaterialAsCreator(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_CREATOR]);
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course);
        $materialCourse = MaterialCourseMother::create(course: $course, announcement: $announcement);

        $service = $this->getService();
        $this->expectExceptionObject(
            UserNotAuthorizedException::userNotAuthorizedToHandleMaterial(
                announcement: $announcement,
                user: $user->getEmail()
            )
        );
        $service->ensureUserCanManageAnnouncementMaterials(user: $user, materialCourse: $materialCourse);
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    #[DataProvider('provideAsManagerMaterialsHandle')]
    public function testAsManagerMaterialsHandle(
        User $user,
        MaterialCourse $materialCourse,
        ?bool $sharing,
        ?callable $announcementManagerRepositoryFindOneBy,
        ?\Exception $exception = null,
    ): void {
        if (null === $sharing && null === $announcementManagerRepositoryFindOneBy && null === $exception) {
            $this->expectNotToPerformAssertions();
        }

        $settingsService = $this->createMock(SettingsService::class);
        if (null !== $sharing) {
            $settingsService->expects($this->once())
                ->method('get')
                ->with('app.announcement.managers.sharing')
                ->willReturn($sharing);
        }
        $announcementManagerRepository = $this->createMock(AnnouncementManagerRepository::class);

        if (null !== $announcementManagerRepositoryFindOneBy) {
            $announcementManagerRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($announcementManagerRepositoryFindOneBy);
        }

        $service = $this->getService(
            announcementManagerRepository: $announcementManagerRepository,
            settingsService: $settingsService,
        );
        if (null !== $exception) {
            $this->expectExceptionObject(
                $exception
            );
        }
        $service->ensureUserCanManageAnnouncementMaterials(user: $user, materialCourse: $materialCourse);
    }

    #[DataProvider('provideAsTutorMaterialsHandle')]
    public function testEnsureUserCanHandleMaterialAsSharedTutor(
        User $user,
        Announcement $announcement,
        MaterialCourse $materialCourse,
        bool $isShared,
        bool $shouldThrow
    ): void {
        $announcementTutorRepository = $this->createMock(AnnouncementTutorRepository::class);
        $announcementTutorRepository->method('isTutorSharedWithAnnouncement')
            ->with($user, $announcement)
            ->willReturn($isShared);

        $service = $this->getService(
            announcementTutorRepository: $announcementTutorRepository,
        );

        if ($shouldThrow) {
            $this->expectExceptionObject(
                UserNotAuthorizedException::userNotAuthorizedToHandleMaterial(
                    announcement: $announcement,
                    user: $user->getEmail()
                )
            );
        } else {
            $this->expectNotToPerformAssertions();
        }
        $service->ensureUserCanManageAnnouncementMaterials(user: $user, materialCourse: $materialCourse);
    }

    public function testEnsureUserCanHandleMaterialThrowsExceptionForUnauthorized(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_USER]);
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course);
        $materialCourse = MaterialCourseMother::create(course: $course, announcement: $announcement);
        $service = $this->getService();
        $this->expectExceptionObject(
            UserNotAuthorizedException::userNotAuthorizedToHandleMaterial(
                announcement: $announcement,
                user: $user->getEmail()
            )
        );
        $service->ensureUserCanManageAnnouncementMaterials(user: $user, materialCourse: $materialCourse);
    }

    public function testEnsureUserCanCreateTaskAsAdmin(): void
    {
        $this->expectNotToPerformAssertions();
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_ADMIN]);
        $announcement = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));
        $service = $this->getService();
        $service->ensureUserCanManageAnnouncementResource(user: $user, announcement: $announcement);
    }

    public function testEnsureUserCanCreateTaskAsCreator(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_CREATOR]);
        $announcement = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));

        $service = $this->getService();
        $this->expectExceptionObject(
            UserNotAuthorizedException::userNotAuthorized(
                announcement: $announcement,
                user: $user->getEmail()
            )
        );
        $service->ensureUserCanManageAnnouncementResource(user: $user, announcement: $announcement);
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    #[DataProvider('provideAsManager')]
    public function testEnsureUserCanCreateTaskAsSharedManager(
        User $user,
        Announcement $announcement,
        ?bool $sharing,
        ?callable $announcementManagerRepositoryFindOneBy,
        ?\Exception $exception = null,
    ): void {
        if (null === $sharing && null === $announcementManagerRepositoryFindOneBy && null === $exception) {
            $this->expectNotToPerformAssertions();
        }

        $settingsService = $this->createMock(SettingsService::class);
        if (null !== $sharing) {
            $settingsService->expects($this->once())
                ->method('get')
                ->with('app.announcement.managers.sharing')
                ->willReturn($sharing);
        }
        $announcementManagerRepository = $this->createMock(AnnouncementManagerRepository::class);

        if (null !== $announcementManagerRepositoryFindOneBy) {
            $announcementManagerRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($announcementManagerRepositoryFindOneBy);
        }

        $service = $this->getService(
            announcementManagerRepository: $announcementManagerRepository,
            settingsService: $settingsService,
        );
        if (null !== $exception) {
            $this->expectExceptionObject(
                $exception
            );
        }
        $service->ensureUserCanManageAnnouncementResource(user: $user, announcement: $announcement);
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    #[DataProvider('provideAsTutor')]
    public function testEnsureUserCanCreateTaskAsSharedTutor(
        User $user,
        Announcement $announcement,
        bool $isShared,
        bool $shouldThrow
    ): void {
        $announcementTutorRepository = $this->createMock(AnnouncementTutorRepository::class);
        $announcementTutorRepository->method('isTutorSharedWithAnnouncement')
            ->with($user, $announcement)
            ->willReturn($isShared);

        $service = $this->getService(
            announcementTutorRepository: $announcementTutorRepository,
        );

        if ($shouldThrow) {
            $this->expectExceptionObject(
                UserNotAuthorizedException::userNotAuthorized(
                    announcement: $announcement,
                    user: $user->getEmail()
                )
            );
        } else {
            $this->expectNotToPerformAssertions();
        }
        $service->ensureUserCanManageAnnouncementResource(user: $user, announcement: $announcement);
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    public function testEnsureUserCanCreateTaskThrowsExceptionForUnauthorized(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_USER]);
        $announcement = AnnouncementMother::create(id: 1, course: CourseMother::create(id: 1));
        $service = $this->getService();
        $this->expectExceptionObject(
            UserNotAuthorizedException::userNotAuthorized(
                announcement: $announcement,
                user: $user->getEmail()
            )
        );
        $service->ensureUserCanManageAnnouncementResource(user: $user, announcement: $announcement);
    }

    public static function provideAsManagerTaskManage(): \Generator
    {
        $user1 = UserMother::create(id: 1, email: '<EMAIL>', roles: [User::ROLE_MANAGER]);
        $course = CourseMother::create(id: 1);
        $announcement1 = AnnouncementMother::create(id: 1, course: $course, createdBy: $user1);
        $taskCourse1 = TaskCourseMother::create(course: $course, announcement: $announcement1);
        yield 'is the announcement creator' => [
            'user' => $user1,
            'taskCourse' => $taskCourse1,
            'sharing' => null,
            'announcementManagerRepositoryFindOneBy' => null,
            'exception' => null,
        ];

        $announcement2 = AnnouncementMother::create(
            id: 2,
            course: $course,
            createdBy: UserMother::create(id: 2)
        );
        $taskCourse2 = TaskCourseMother::create(course: $course, announcement: $announcement2);
        yield 'not the announcement creator and sharing disabled' => [
            'user' => $user1,
            'taskCourse' => $taskCourse2,
            'sharing' => false,
            'announcementManagerRepositoryFindOneBy' => null,
            'exception' => UserNotAuthorizedException::userNotAuthorizedToManageTask(
                taskCourse: $taskCourse2,
                user: '<EMAIL>'
            ),
        ];

        yield 'not the announcement creator and sharing enabled with access' => [
            'user' => $user1,
            'taskCourse' => $taskCourse2,
            'sharing' => true,
            'announcementManagerRepositoryFindOneBy' => fn () => AnnouncementManagerMother::create(
                userId: new Id(1),
                announcementId: new Id(2),
            ),
            'exception' => null,
        ];

        yield 'not the announcement creator and sharing enabled without access' => [
            'user' => $user1,
            'taskCourse' => $taskCourse2,
            'sharing' => true,
            'announcementManagerRepositoryFindOneBy' => fn () => throw new AnnouncementManagerNotFoundException(),
            'exception' => UserNotAuthorizedException::userNotAuthorizedToManageTask(
                taskCourse: $taskCourse2,
                user: '<EMAIL>'
            ),
        ];
    }

    public static function provideAsTutorTaskManage(): \Generator
    {
        $user1 = UserMother::create(id: 1, email: '<EMAIL>', roles: [User::ROLE_TUTOR]);
        $course = CourseMother::create(id: 1);
        $announcement1 = AnnouncementMother::create(id: 1, course: $course);

        yield 'is shared with tutor and is task creator' => [
            'user' => $user1,
            'announcement' => $announcement1,
            'taskCourse' => TaskCourseMother::create(course: $course, announcement: $announcement1, createdBy: $user1),
            'isShared' => true,
            'shouldThrow' => false,
        ];

        $user2 = UserMother::create(id: 2, email: '<EMAIL>', roles: [User::ROLE_TUTOR]);
        $announcement2 = AnnouncementMother::create(id: 2, course: $course);

        yield 'is NOT shared with tutor' => [
            'user' => $user2,
            'announcement' => $announcement2,
            'taskCourse' => TaskCourseMother::create(course: $course, announcement: $announcement2, createdBy: $user2),
            'isShared' => false,
            'shouldThrow' => true,
        ];
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    public function testEnsureUserCanManageTaskAsAdmin(): void
    {
        $this->expectNotToPerformAssertions();
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_ADMIN]);
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course);
        $taskCourse = TaskCourseMother::create(course: $course, announcement: $announcement);
        $service = $this->getService();
        $service->ensureUserCanManageTask(user: $user, taskCourse: $taskCourse);
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    public function testEnsureUserCanManageTaskAsCreator(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_CREATOR]);
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course);
        $taskCourse = TaskCourseMother::create(course: $course, announcement: $announcement);

        $service = $this->getService();
        $this->expectExceptionObject(
            UserNotAuthorizedException::userNotAuthorizedToManageTask(
                taskCourse: $taskCourse,
                user: $user->getEmail()
            )
        );
        $service->ensureUserCanManageTask(user: $user, taskCourse: $taskCourse);
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    #[DataProvider('provideAsManagerTaskManage')]
    public function testEnsureUserCanManageTaskAsManager(
        User $user,
        TaskCourse $taskCourse,
        ?bool $sharing,
        ?callable $announcementManagerRepositoryFindOneBy,
        ?\Exception $exception = null,
    ): void {
        if (null === $sharing && null === $announcementManagerRepositoryFindOneBy && null === $exception) {
            $this->expectNotToPerformAssertions();
        }

        $settingsService = $this->createMock(SettingsService::class);
        if (null !== $sharing) {
            $settingsService->expects($this->once())
                ->method('get')
                ->with('app.announcement.managers.sharing')
                ->willReturn($sharing);
        }
        $announcementManagerRepository = $this->createMock(AnnouncementManagerRepository::class);

        if (null !== $announcementManagerRepositoryFindOneBy) {
            $announcementManagerRepository->expects($this->once())
                ->method('findOneBy')
                ->willReturnCallback($announcementManagerRepositoryFindOneBy);
        }

        $service = $this->getService(
            announcementManagerRepository: $announcementManagerRepository,
            settingsService: $settingsService,
        );
        if (null !== $exception) {
            $this->expectExceptionObject(
                $exception
            );
        }
        $service->ensureUserCanManageTask(user: $user, taskCourse: $taskCourse);
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    #[DataProvider('provideAsTutorTaskManage')]
    public function testEnsureUserCanManageTaskAsSharedTutor(
        User $user,
        Announcement $announcement,
        TaskCourse $taskCourse,
        bool $isShared,
        bool $shouldThrow
    ): void {
        $announcementTutorRepository = $this->createMock(AnnouncementTutorRepository::class);
        $announcementTutorRepository->method('isTutorSharedWithAnnouncement')
            ->with($user, $announcement)
            ->willReturn($isShared);

        $service = $this->getService(
            announcementTutorRepository: $announcementTutorRepository,
        );

        if ($shouldThrow) {
            $this->expectExceptionObject(
                UserNotAuthorizedException::userNotAuthorizedToManageTask(
                    taskCourse: $taskCourse,
                    user: $user->getEmail()
                )
            );
        } else {
            $this->expectNotToPerformAssertions();
        }
        $service->ensureUserCanManageTask(user: $user, taskCourse: $taskCourse);
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    public function testEnsureUserCanManageTaskThrowsExceptionForUnauthorized(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_USER]);
        $course = CourseMother::create(id: 1);
        $announcement = AnnouncementMother::create(id: 1, course: $course);
        $taskCourse = TaskCourseMother::create(course: $course, announcement: $announcement);
        $service = $this->getService();
        $this->expectExceptionObject(
            UserNotAuthorizedException::userNotAuthorizedToManageTask(
                taskCourse: $taskCourse,
                user: $user->getEmail()
            )
        );
        $service->ensureUserCanManageTask(user: $user, taskCourse: $taskCourse);
    }

    /**
     * @throws InfrastructureException
     * @throws UserNotAuthorizedException
     */
    public function testEnsureUserCanManageTaskWithoutAnnouncement(): void
    {
        $user = UserMother::create(id: 10, email: '<EMAIL>', roles: [User::ROLE_ADMIN]);
        $course = CourseMother::create(id: 1);
        $taskCourse = TaskCourseMother::create(course: $course, announcement: null);
        $service = $this->getService();
        $this->expectExceptionObject(
            UserNotAuthorizedException::userNotAuthorizedToManageTask(
                taskCourse: $taskCourse,
                user: $user->getEmail()
            )
        );
        $service->ensureUserCanManageTask(user: $user, taskCourse: $taskCourse);
    }
}
