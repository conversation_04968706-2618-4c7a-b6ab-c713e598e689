<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler\Admin;

use App\Entity\Filter;
use App\Entity\User;
use App\Tests\Mother\Entity\UserMother;
use App\V2\Application\Query\Admin\GetUsers;
use App\V2\Application\QueryHandler\Admin\GetUsersHandler;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Filter\FilterCollection;
use App\V2\Domain\User\LegacyUserCollection;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetUsersHandlerTest extends TestCase
{
    private UserRepository|MockObject $userRepository;
    private GetUsersHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->userRepository = $this->createMock(UserRepository::class);
        $this->handler = new GetUsersHandler(
            userRepository: $this->userRepository,
        );
    }

    /**
     * @throws InfrastructureException
     * @throws CollectionException
     */
    #[DataProvider('correctCriteriaHandlingProvider')]
    public function testCorrectCriteriaHandling(GetUsers $query, UserCriteria $expectedCriteria): void
    {
        // Mock the user repository to return a specific collection based on criteria
        $this->userRepository->expects($this->once())
            ->method('findBy')
            ->with($expectedCriteria)
            ->willReturn(new LegacyUserCollection([])); // Return an empty collection for simplicity

        $this->userRepository->expects($this->once())
            ->method('countBy')
            ->with($expectedCriteria)
            ->willReturn(0); // Return 0 for simplicity

        $this->handler->handle($query);
    }

    /**
     * @throws CollectionException
     */
    public static function correctCriteriaHandlingProvider(): \Generator
    {
        yield 'Request user is super admin, no filters applied' => [
            'query' => new GetUsers(
                criteria: UserCriteria::createEmpty(),
                requestUser: UserMother::create(
                    roles: ['ROLE_SUPER_ADMIN']
                ),
            ),
            'expectedCriteria' => UserCriteria::createEmpty(),
        ];

        yield 'Request user is not super admin, hides super admin' => [
            'query' => new GetUsers(
                criteria: UserCriteria::createEmpty(),
                requestUser: UserMother::create(
                    roles: ['ROLE_USER']
                ),
            ),
            'expectedCriteria' => UserCriteria::createEmpty()
                ->hideSuperAdmin(true),
        ];

        $filter = (new Filter())->setName('Filter');
        yield 'Request user is manager, filters by created users' => [
            'query' => new GetUsers(
                criteria: UserCriteria::createEmpty(),
                requestUser: UserMother::create(
                    id: 999,
                    roles: ['ROLE_MANAGER'],
                    filtersForManage: [$filter]
                ),
            ),
            'expectedCriteria' => UserCriteria::createEmpty()
                ->hideSuperAdmin(true)
                ->filterByFilters(
                    filters: new FilterCollection([$filter]),
                    addCreatedBy: 999,
                ),
        ];
    }
}
