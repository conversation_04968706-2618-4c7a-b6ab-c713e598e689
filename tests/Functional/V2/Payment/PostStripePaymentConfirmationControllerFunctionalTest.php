<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Payment;

use App\Tests\Functional\FunctionalTestCase;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

/**
 * Functional tests for Stripe payment confirmation webhook endpoint.
 *
 * These tests focus on HTTP layer behavior (routing, request/response handling)
 * using StripePaymentWebhookValidator for input validation and InMemoryPaymentManager
 * which intentionally bypasses actual webhook processing.
 *
 * For business logic testing (payment state updates, signature validation, etc.),
 * see the corresponding unit tests that use mocked dependencies.
 */
class PostStripePaymentConfirmationControllerFunctionalTest extends FunctionalTestCase
{
    private const string WEBHOOK_ENDPOINT = '/api/v2/payments/confirmation/stripe';

    /**
     * Test successful webhook processing with valid Stripe payload.
     *
     * This test uses StripePaymentWebhookValidator and InMemoryPaymentManager:
     * - StripePaymentWebhookValidator: Validates payload is not empty and signature is present
     * - InMemoryPaymentManager: Does nothing in processWebhook(), never throws exceptions
     *
     * This allows us to test the HTTP layer (routing, request handling, response format)
     * without external dependencies. Business logic is tested separately in unit tests.
     */
    public function testSuccessfulWebhookProcessing(): void
    {
        $validStripePayload = json_encode([
            'id' => 'evt_test_webhook',
            'type' => 'payment_intent.succeeded',
            'data' => [
                'object' => [
                    'id' => 'pi_test_123456789',
                    'status' => 'succeeded',
                    'amount' => 2000,
                    'currency' => 'eur',
                ],
            ],
            'livemode' => false,
            'created' => **********,
            'api_version' => '2020-08-27',
        ]);

        $stripeSignature = 't=**********,v1=test_signature_hash';

        $response = $this->makeRequest(
            method: 'POST',
            uri: self::WEBHOOK_ENDPOINT,
            body: $validStripePayload,
            headers: [
                'HTTP_STRIPE_SIGNATURE' => $stripeSignature,
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals(['data' => []], $responseData);
    }

    /**
     * Test webhook processing with different event types.
     *
     * StripePaymentWebhookValidator validates payload and signature presence,
     * InMemoryPaymentManager accepts any event type and always returns HTTP 200.
     */
    #[DataProvider('stripeEventTypesProvider')]
    public function testWebhookProcessingWithDifferentEventTypes(string $eventType): void
    {
        $stripePayload = json_encode([
            'id' => 'evt_test_webhook',
            'type' => $eventType,
            'data' => [
                'object' => [
                    'id' => 'pi_test_123456789',
                    'status' => 'succeeded',
                ],
            ],
        ]);

        $stripeSignature = 't=**********,v1=test_signature';

        $response = $this->makeRequest(
            method: 'POST',
            uri: self::WEBHOOK_ENDPOINT,
            body: $stripePayload,
            headers: [
                'HTTP_STRIPE_SIGNATURE' => $stripeSignature,
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    /**
     * Test webhook processing with invalid payload.
     *
     * StripePaymentWebhookValidator only validates that payload is not empty,
     * so invalid JSON will still pass validation and result in HTTP 200.
     */
    public function testWebhookProcessingWithInvalidPayload(): void
    {
        $invalidPayload = 'invalid_json_payload';
        $stripeSignature = 't=**********,v1=test_signature';

        $response = $this->makeRequest(
            method: 'POST',
            uri: self::WEBHOOK_ENDPOINT,
            body: $invalidPayload,
            headers: [
                'HTTP_STRIPE_SIGNATURE' => $stripeSignature,
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        // StripePaymentWebhookValidator validates payload is not empty and signature is present
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    /**
     * Test webhook processing with invalid signature.
     *
     * StripePaymentWebhookValidator only validates that signature is present,
     * not that it's cryptographically valid, so any non-empty signature passes.
     */
    public function testWebhookProcessingWithInvalidSignature(): void
    {
        $validPayload = json_encode(['type' => 'payment_intent.succeeded']);
        $invalidSignature = 'invalid_signature';

        $response = $this->makeRequest(
            method: 'POST',
            uri: self::WEBHOOK_ENDPOINT,
            body: $validPayload,
            headers: [
                'HTTP_STRIPE_SIGNATURE' => $invalidSignature,
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        // StripePaymentWebhookValidator validates signature presence, not validity
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    /**
     * Test webhook processing with missing Stripe signature header.
     *
     * StripePaymentWebhookValidator requires signature header to be present,
     * so missing HTTP_STRIPE_SIGNATURE should result in validation error.
     */
    public function testWebhookProcessingWithMissingSignature(): void
    {
        $validPayload = json_encode(['type' => 'payment_intent.succeeded']);

        $response = $this->makeRequest(
            method: 'POST',
            uri: self::WEBHOOK_ENDPOINT,
            body: $validPayload,
            headers: [
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        // StripePaymentWebhookValidator should reject requests without signature
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getStatusCode());
    }

    /**
     * Test webhook processing with empty payload.
     *
     * StripePaymentWebhookValidator should reject completely empty payloads.
     */
    public function testWebhookProcessingWithEmptyPayload(): void
    {
        $emptyPayload = '';
        $stripeSignature = 't=**********,v1=test_signature';

        $response = $this->makeRequest(
            method: 'POST',
            uri: self::WEBHOOK_ENDPOINT,
            body: $emptyPayload,
            headers: [
                'HTTP_STRIPE_SIGNATURE' => $stripeSignature,
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        // StripePaymentWebhookValidator should reject empty payloads
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getStatusCode());
    }

    /**
     * Test response structure and content type.
     */
    public function testResponseStructureAndContentType(): void
    {
        $validStripePayload = json_encode([
            'type' => 'payment_intent.succeeded',
            'data' => ['object' => ['id' => 'pi_test_123']],
        ]);

        $response = $this->makeRequest(
            method: 'POST',
            uri: self::WEBHOOK_ENDPOINT,
            body: $validStripePayload,
            headers: [
                'HTTP_STRIPE_SIGNATURE' => 't=**********,v1=test_signature',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertEquals('application/json', $response->headers->get('Content-Type'));

        $responseData = json_decode($response->getContent(), true);
        $this->assertIsArray($responseData);
        $this->assertEquals(['data' => []], $responseData);
    }

    /**
     * Test that only POST method is allowed.
     * Note: Some methods might return 500 instead of 405 due to routing configuration.
     */
    #[DataProvider('disallowedMethodsProvider')]
    public function testDisallowedHttpMethods(string $method): void
    {
        $response = $this->makeRequest(
            method: $method,
            uri: self::WEBHOOK_ENDPOINT,
            body: json_encode(['type' => 'payment_intent.succeeded']),
            headers: [
                'HTTP_STRIPE_SIGNATURE' => 't=**********,v1=test_signature',
                'CONTENT_TYPE' => 'application/json',
            ]
        );

        // The route is configured to only accept POST, so other methods should fail
        // This could be either 405 (Method Not Allowed) or 500 (Internal Server Error)
        $this->assertContains($response->getStatusCode(), [
            Response::HTTP_METHOD_NOT_ALLOWED,
            Response::HTTP_INTERNAL_SERVER_ERROR,
        ]);
    }

    /**
     * Data provider for different Stripe event types.
     */
    public static function stripeEventTypesProvider(): array
    {
        return [
            'payment_intent.succeeded' => ['payment_intent.succeeded'],
            'payment_intent.payment_failed' => ['payment_intent.payment_failed'],
            'payment_intent.canceled' => ['payment_intent.canceled'],
            'payment_intent.requires_action' => ['payment_intent.requires_action'],
            'payment_intent.processing' => ['payment_intent.processing'],
        ];
    }

    /**
     * Data provider for disallowed HTTP methods.
     */
    public static function disallowedMethodsProvider(): array
    {
        return [
            'GET' => ['GET'],
            'PUT' => ['PUT'],
            'PATCH' => ['PATCH'],
            'DELETE' => ['DELETE'],
        ];
    }
}
