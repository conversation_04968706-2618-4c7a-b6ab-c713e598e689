<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\Filter\FilterMother;
use App\V2\Domain\Filter\Filter;
use App\V2\Domain\Filter\FilterRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;

trait FilterFixtureTrait
{
    private function getFilterRepository(): object
    {
        return $this->client->getContainer()
            ->get('App\V2\Domain\Filter\FilterRepository');
    }

    /**
     * @param Id $id By default, the internal value is null.
     *               This means, the internal value is assigned by the repository.
     *
     * @throws InfrastructureException
     */
    private function setAndGetFilterInRepository(
        Id $id = new Id(null),
        ?Id $filterCategoryId = null,
        ?string $name = null,
        ?string $code = null,
        ?int $sort = null,
        ?Id $parentId = null,
    ): Filter {
        $filter = FilterMother::create(
            id: $id,
            filterCategoryId: $filterCategoryId,
            name: $name,
            code: $code,
            sort: $sort,
            parentId: $parentId
        );

        /** @var FilterRepository $repository */
        $repository = $this->getFilterRepository();
        $repository->put($filter);
        $this->assertNotNull($filter->getId()->valueOrNull());

        return $filter;
    }
}
