<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\Filter\FilterCategoryMother;
use App\V2\Domain\Filter\FilterCategory\FilterCategory;
use App\V2\Domain\Filter\FilterCategory\FilterCategoryRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;

trait FilterCategoryFixtureTrait
{
    private function getFilterCategoryRepository(): object
    {
        return $this->client->getContainer()
            ->get('App\V2\Domain\Filter\FilterCategory\FilterCategoryRepository');
    }

    /**
     * @param Id $id By default, the internal value is null.
     *               This means, the internal value is assigned by the repository.
     *
     * @throws InfrastructureException
     */
    private function setAndGetFilterCategoryInRepository(
        Id $id = new Id(null),
        ?Id $parentId = null,
        string $name = FilterCategoryMother::DEFAULT_NAME,
        int $sort = 0,
    ): FilterCategory {
        $category = FilterCategoryMother::create(
            id: $id,
            parentId: $parentId,
            name: $name,
            sort: $sort
        );

        /** @var FilterCategoryRepository $repository */
        $repository = $this->getFilterCategoryRepository();
        $repository->put($category);
        $this->assertNotNull(
            actual: $category->getId()->valueOrNull(),
            message: 'Failed to assign category id in repository',
        );

        return $category;
    }
}
