<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Campus\Purchase;

use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Campus\CampusPurchaseEndpoints;
use App\Tests\Functional\HelperTrait\PurchasableItemHelperTrait;
use App\Tests\V2\Mother\Billing\BillingDataMother;
use App\Tests\V2\Mother\Purchase\PurchaseMother;
use App\Tests\V2\Mother\Shared\Financial\MoneyMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\Billing\BillingDataRepository;
use App\V2\Domain\Billing\Exception\BillingDataRepositoryException;
use App\V2\Domain\Purchase\Exception\PurchaseRepositoryException;
use App\V2\Domain\Purchase\PurchaseRepository;
use App\V2\Domain\Purchase\PurchaseStatus;
use App\V2\Domain\Shared\Financial\Currency;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class PatchPurchaseBillingDataControllerFunctionalTest extends FunctionalTestCase
{
    use PurchasableItemHelperTrait;

    private const string VALID_UUID = '550e8400-e29b-41d4-a716-************';
    private const string INVALID_UUID = 'invalid-uuid';

    private PurchaseRepository $purchaseRepository;
    private BillingDataRepository $billingDataRepository;
    private User $testUser;
    private array $additionalUserIds = [];

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();

        /** @var PurchaseRepository $purchaseRepository */
        $purchaseRepository = $this->getService(PurchaseRepository::class);
        $this->purchaseRepository = $purchaseRepository;

        /** @var BillingDataRepository $billingDataRepository */
        $billingDataRepository = $this->getService(BillingDataRepository::class);
        $this->billingDataRepository = $billingDataRepository;

        $this->testUser = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: 'test.patch.billing.' . uniqid() . '@example.com',
        );
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $userIdsToDelete = [];

        if (!empty($this->testUser)) {
            $userIdsToDelete[] = $this->testUser->getId();
        }

        if (!empty($this->additionalUserIds)) {
            $userIdsToDelete = array_merge($userIdsToDelete, $this->additionalUserIds);
        }

        if (!empty($userIdsToDelete)) {
            $this->hardDeleteUsersByIds($userIdsToDelete);
        }

        parent::tearDown();
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testSuccessfulPatchBillingData(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // Create a purchase in the database first
        $purchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 5000, currency: Currency::EUR())
        );
        $this->purchaseRepository->put($purchase);

        $purchaseId = $purchase->getId()->value();

        $requestBody = [
            'tin' => '12345678A',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Madrid',
            'country' => 'ES',
            'metadata' => ['company' => 'Test Company'],
            'save_as_default' => false,
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
            body: $requestBody,
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals([], $responseData['data']);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     * @throws BillingDataRepositoryException
     * @throws \Exception
     */
    public function testSuccessfulPatchBillingDataWithSaveAsDefault(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // Create a purchase in the database first
        $purchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 3000, currency: Currency::EUR())
        );
        $this->purchaseRepository->put($purchase);

        // Create a billing data record for the user (required for save_as_default functionality)
        $existingBillingData = BillingDataMother::create(
            userId: new Id($this->testUser->getId())
        );
        $this->billingDataRepository->put($existingBillingData);

        $purchaseId = $purchase->getId()->value();

        $requestBody = [
            'tin' => '87654321B',
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'address' => '456 Oak Avenue',
            'postal_code' => '54321',
            'city' => 'Barcelona',
            'country' => 'ES',
            'metadata' => ['company' => 'Test Corp'],
            'save_as_default' => true,
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
            body: $requestBody,
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals([], $responseData['data']);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    #[DataProvider('invalidDataProvider')]
    public function testValidationErrors(
        array $requestBody,
        int $expectedStatusCode,
    ): void {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // Create a purchase in the database first
        $purchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 2000, currency: Currency::EUR())
        );
        $this->purchaseRepository->put($purchase);

        $purchaseId = $purchase->getId()->value();

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
            body: $requestBody,
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals(1, $responseData['error']);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertEquals('Validation failed', $responseData['message']);
    }

    public function testInvalidUuidFormat(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        $requestBody = [
            'tin' => '12345678A',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Madrid',
            'country' => 'ES',
            'metadata' => [],
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint(self::INVALID_UUID),
            body: $requestBody,
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals(1, $responseData['error']);
    }

    /**
     * @throws InvalidUuidException
     */
    public function testUnauthenticatedAccess(): void
    {
        $purchaseId = UuidMother::create()->value();

        $requestBody = [
            'tin' => '12345678A',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Madrid',
            'country' => 'ES',
            'metadata' => [],
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: "/api/v2/purchases/$purchaseId/billing-data",
            body: $requestBody
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testEmptyRequestBody(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // Create a purchase in the database first
        $purchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 1500, currency: Currency::EUR())
        );
        $this->purchaseRepository->put($purchase);

        $purchaseId = $purchase->getId()->value();

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
            body: [],
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals(1, $responseData['error']);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertEquals('Validation failed', $responseData['message']);
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testMalformedJsonRequest(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // Create a purchase in the database first
        $purchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 1000, currency: Currency::EUR())
        );
        $this->purchaseRepository->put($purchase);

        $purchaseId = $purchase->getId()->value();

        $this->client->request(
            'PATCH',
            'http://localhost' . CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
            [],
            [],
            ['HTTP_Authorization' => 'Bearer ' . $userToken],
            'invalid-json'
        );

        $response = $this->client->getResponse();
        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
    }

    /**
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testSuccessfulPatchBillingDataWithSaveAsDefaultWhenNoBillingDataExists(): void
    {
        $userToken = $this->loginAndGetTokenForUser($this->testUser);

        // Create a purchase in the database first
        $purchase = PurchaseMother::create(
            userId: new Id($this->testUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 2500, currency: Currency::EUR())
        );
        $this->purchaseRepository->put($purchase);

        // NOTE: We don't create a BillingData record for the user to test the case where it doesn't exist

        $purchaseId = $purchase->getId()->value();

        $requestBody = [
            'tin' => '11111111C',
            'first_name' => 'Carlos',
            'last_name' => 'Rodriguez',
            'address' => '321 New Street',
            'postal_code' => '98765',
            'city' => 'Sevilla',
            'country' => 'ES',
            'metadata' => ['notes' => 'New user registration'],
            'save_as_default' => true,
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
            body: $requestBody,
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertEquals([], $responseData['data']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     * @throws InvalidUuidException
     * @throws PurchaseRepositoryException
     */
    public function testAdminRoleAccess(): void
    {
        // Create admin user
        $adminUser = $this->createAndGetUser(
            roles: [User::ROLE_ADMIN],
            email: 'admin.patch.billing.' . uniqid() . '@example.com',
        );
        $this->additionalUserIds[] = $adminUser->getId();

        $userToken = $this->loginAndGetTokenForUser($adminUser);

        // Create a purchase in the database first
        $purchase = PurchaseMother::create(
            userId: new Id($adminUser->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 4000, currency: Currency::EUR())
        );
        $this->purchaseRepository->put($purchase);

        $purchaseId = $purchase->getId()->value();

        $requestBody = [
            'tin' => '12345678A',
            'first_name' => 'Admin',
            'last_name' => 'User',
            'address' => '789 Admin Street',
            'postal_code' => '67890',
            'city' => 'Valencia',
            'country' => 'ES',
            'metadata' => [],
            'save_as_default' => false,
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: CampusPurchaseEndpoints::patchPurchaseBillingDataEndpoint($purchaseId),
            body: $requestBody,
            bearerToken: $userToken
        );

        // Should have access to ROLE_ADMIN
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
    }

    public static function invalidDataProvider(): \Generator
    {
        yield 'missing tin field' => [
            'requestBody' => [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => [],
            ],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];

        yield 'missing first_name field' => [
            'requestBody' => [
                'tin' => '12345678A',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => [],
            ],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];

        yield 'missing last_name field' => [
            'requestBody' => [
                'tin' => '12345678A',
                'first_name' => 'John',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => [],
            ],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];

        yield 'missing address field' => [
            'requestBody' => [
                'tin' => '12345678A',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => [],
            ],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];

        yield 'missing postal_code field' => [
            'requestBody' => [
                'tin' => '12345678A',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'city' => 'Madrid',
                'country' => 'ES',
                'metadata' => [],
            ],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];

        yield 'missing city field' => [
            'requestBody' => [
                'tin' => '12345678A',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'country' => 'ES',
                'metadata' => [],
            ],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];

        yield 'missing country field' => [
            'requestBody' => [
                'tin' => '12345678A',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address' => '123 Main Street',
                'postal_code' => '12345',
                'city' => 'Madrid',
                'metadata' => [],
            ],
            'expectedStatusCode' => Response::HTTP_BAD_REQUEST,
        ];
    }

    /**
     * @throws InvalidUuidException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws PurchaseRepositoryException
     */
    public function testUserCannotAccessOtherUserPurchase(): void
    {
        // Create first user and their purchase
        $userA = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: 'userA.patch.billing.' . uniqid() . '@example.com',
        );
        $this->additionalUserIds[] = $userA->getId();

        $purchaseUserA = PurchaseMother::create(
            userId: new Id($userA->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 4000, currency: Currency::EUR())
        );

        $this->purchaseRepository->put($purchaseUserA);

        // Create a second user (who will try to access userA's purchase)
        $userB = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: 'userB.patch.billing.' . uniqid() . '@example.com',
        );
        $this->additionalUserIds[] = $userB->getId();

        $userBToken = $this->loginAndGetTokenForUser($userB);

        $requestBody = [
            'tin' => '87654321B',
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'address' => '456 Oak Avenue',
            'postal_code' => '54321',
            'city' => 'Barcelona',
            'country' => 'ES',
            'metadata' => [
                'company' => 'Evil Corp',
            ],
            'save_as_default' => false,
        ];

        // UserB tries to update UserA's purchase - should be forbidden
        $response = $this->makeRequest(
            method: 'PATCH',
            uri: "/api/v2/purchases/{$purchaseUserA->getId()->value()}/billing-data",
            body: $requestBody,
            bearerToken: $userBToken
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $responseData);
        $this->assertStringContainsString('not allowed', strtolower($responseData['message']));
    }

    /**
     * @throws InvalidUuidException
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws PurchaseRepositoryException
     */
    public function testMetadataSecurityValidation(): void
    {
        $user = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: 'user.metadata.validation.' . uniqid() . '@example.com',
        );
        $this->additionalUserIds[] = $user->getId();

        $purchase = PurchaseMother::create(
            userId: new Id($user->getId()),
            status: PurchaseStatus::Pending,
            amount: MoneyMother::create(amount: 4000, currency: Currency::EUR())
        );

        $this->purchaseRepository->put($purchase);
        $userToken = $this->loginAndGetTokenForUser($user);

        // Test metadata key too long (over 50 characters)
        $requestBodyLongKey = [
            'tin' => '12345678A',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Madrid',
            'country' => 'ES',
            'metadata' => [
                'this_is_a_very_long_metadata_key_that_exceeds_fifty_characters_limit' => 'some value',
            ],
            'save_as_default' => false,
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: "/api/v2/purchases/{$purchase->getId()->value()}/billing-data",
            body: $requestBodyLongKey,
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());

        // Test metadata value too long (over 500 characters)
        $longValue = str_repeat('a', 501);
        $requestBodyLongValue = [
            'tin' => '12345678A',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Madrid',
            'country' => 'ES',
            'metadata' => [
                'valid_key' => $longValue,
            ],
            'save_as_default' => false,
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: "/api/v2/purchases/{$purchase->getId()->value()}/billing-data",
            body: $requestBodyLongValue,
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());

        // Test too many metadata keys (over 20 limit)
        $tooManyKeys = [];
        for ($i = 0; $i < 21; ++$i) {
            $tooManyKeys["key_$i"] = "value_$i";
        }

        $requestBodyTooManyKeys = [
            'tin' => '12345678A',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Madrid',
            'country' => 'ES',
            'metadata' => $tooManyKeys,
            'save_as_default' => false,
        ];

        $response = $this->makeRequest(
            method: 'PATCH',
            uri: "/api/v2/purchases/{$purchase->getId()->value()}/billing-data",
            body: $requestBodyTooManyKeys,
            bearerToken: $userToken
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
    }
}
