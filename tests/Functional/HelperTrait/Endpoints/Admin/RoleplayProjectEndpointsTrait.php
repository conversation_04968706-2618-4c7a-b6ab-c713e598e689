<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait\Endpoints\Admin;

trait RoleplayProjectEndpointsTrait
{
    public function saveResourceEndpoint(int $projectId): string
    {
        return \sprintf('/admin/roleplay/project/%d/save-resource', $projectId);
    }

    public function deleteFileResourceEndpoint(int $projectId): string
    {
        return \sprintf('/admin/roleplay/project/%d/remove-file', $projectId);
    }

    public function saveScenesRoleplayEndpoint(int $projectId): string
    {
        return \sprintf('/admin/roleplay/project/%d/save', $projectId);
    }
}
