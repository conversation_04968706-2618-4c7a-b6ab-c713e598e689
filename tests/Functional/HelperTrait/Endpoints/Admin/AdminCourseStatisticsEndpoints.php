<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait\Endpoints\Admin;

class AdminCourseStatisticsEndpoints
{
    public static function courseStatisticsSummaryEndpoint(int $courseId): string
    {
        return "/api/v1/statistics/courses/{$courseId}/chapters/summary";
    }

    public static function courseChapterStatisticsEndpoint(int $courseId): string
    {
        return "/api/v1/statistics/courses/{$courseId}/chapters";
    }

    public static function courseChapterDetailStatisticsEndpoint(int $chapterId): string
    {
        return "/api/v1/statistics/chapters/{$chapterId}/progress";
    }
}
