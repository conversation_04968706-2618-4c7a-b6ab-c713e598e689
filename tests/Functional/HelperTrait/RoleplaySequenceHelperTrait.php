<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\RoleplayProject;
use App\Entity\RoleplaySequence;
use App\Tests\Mother\Entity\RoleplaySequenceMother;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait RoleplaySequenceHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetRoleplaySequence(
        RoleplayProject $roleplayProject,
        string $color,
        int $order,
    ): RoleplaySequence {
        $entity = RoleplaySequenceMother::create(
            roleplayProject: $roleplayProject,
            color: $color,
            order: $order,
        );

        $em = $this->getEntityManager();
        $em->persist($entity);
        $em->flush();

        return $entity;
    }
}
