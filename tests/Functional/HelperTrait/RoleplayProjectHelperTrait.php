<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\RoleplayProject;
use App\Tests\Mother\Entity\RoleplayProjectMother;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait RoleplayProjectHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetRoleplayProject(
        ?string $title = null,
        ?array $sequences = null,
        ?array $beginnings = null,
        ?array $endings = null,
    ): RoleplayProject {
        $entity = RoleplayProjectMother::create(
            title: $title,
            sequences: $sequences,
            beginnings: $beginnings,
            endings: $endings,
        );

        $em = $this->getEntityManager();
        $em->persist($entity);
        $em->flush();

        return $entity;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetEmptyRoleplayProject(): RoleplayProject
    {
        $entity = RoleplayProjectMother::create();

        $em = $this->getEntityManager();
        $em->persist($entity);
        $em->flush();

        return $entity;
    }
}
