<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\RoleplayScene;
use App\Entity\RoleplaySequence;
use App\Tests\Mother\Entity\RoleplaySceneMother;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait RoleplaySceneHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetRoleplayScene(
        RoleplaySequence $roleplaySequence,
        string $code,
        string $type,
        int $order,
        array $answers,
        ?string $attachedTag = null,
        ?string $attached = null,
        ?string $background = null,
        ?string $avatar = null,
        ?string $video = null,
        ?string $standBy = null,
        ?string $statement = null,
    ): RoleplayScene {
        $entity = RoleplaySceneMother::create(
            roleplaySequence: $roleplaySequence,
            code: $code,
            type: $type,
            attachedTag: $attachedTag,
            attached: $attached,
            background: $background,
            avatar: $avatar,
            video: $video,
            standBy: $standBy,
            statement: $statement,
            order: $order,
            answers: $answers,
        );

        $em = $this->getEntityManager();
        $em->persist($entity);
        $em->flush();

        return $entity;
    }
}
