<?php

declare(strict_types=1);

namespace App\Tests\Functional\Command\Database;

use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Tester\CommandTester;

class VerifyMigrationsCommandTest extends KernelTestCase
{
    private CommandTester $commandTester;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $application = new Application($kernel);

        $command = $application->find('database:verify-migrations');
        $this->commandTester = new CommandTester($command);
    }

    public function testCommandExists(): void
    {
        $kernel = self::bootKernel();
        $application = new Application($kernel);

        $this->assertTrue($application->has('database:verify-migrations'));
    }

    public function testCommandCanBeExecuted(): void
    {
        $this->commandTester->execute([]);

        $statusCode = $this->commandTester->getStatusCode();
        $this->assertContains($statusCode, [Command::SUCCESS, Command::FAILURE]);
    }

    public function testCommandWithDetailedOption(): void
    {
        $this->commandTester->execute([
            '--detailed' => true,
        ]);

        $output = $this->commandTester->getDisplay();

        $this->assertStringContainsString('Verification of Migrations vs. Database', $output);
        $this->assertStringContainsString('Reading migrations...', $output);
        $this->assertStringContainsString('Getting current database structure...', $output);
        $this->assertStringContainsString('Comparing structures...', $output);
    }

    public function testCommandWithSpecificTable(): void
    {
        $this->commandTester->execute([
            '--table' => 'user',
        ]);

        $output = $this->commandTester->getDisplay();

        $this->assertStringContainsString('Verification of Migrations vs. Database', $output);
    }

    public function testCommandShowsProgressInformation(): void
    {
        $this->commandTester->execute([]);

        $output = $this->commandTester->getDisplay();

        $this->assertStringContainsString('Processed', $output);
        $this->assertStringContainsString('migrations', $output);
        $this->assertStringContainsString('Found', $output);
        $this->assertStringContainsString('tables', $output);
    }

    public function testCommandHandlesVerboseOutput(): void
    {
        $this->commandTester->execute([], ['verbosity' => \Symfony\Component\Console\Output\OutputInterface::VERBOSITY_VERBOSE]);

        $statusCode = $this->commandTester->getStatusCode();
        $this->assertContains($statusCode, [Command::SUCCESS, Command::FAILURE]);
    }

    public function testCommandWithInvalidTable(): void
    {
        $this->commandTester->execute([
            '--table' => 'non_existent_table_12345',
        ]);

        $output = $this->commandTester->getDisplay();

        $statusCode = $this->commandTester->getStatusCode();
        $this->assertContains($statusCode, [Command::SUCCESS, Command::FAILURE]);
    }

    public function testCommandOutputFormat(): void
    {
        $this->commandTester->execute([
            '--detailed' => true,
        ]);

        $output = $this->commandTester->getDisplay();

        $this->assertStringContainsString('=====', $output);
        $this->assertStringContainsString('-----', $output);

        $this->assertThat(
            $output,
            $this->logicalOr(
                $this->stringContains('✅ The database structure matches the migrations perfectly.'),
                $this->stringContains('❌ Found')
            )
        );
    }

    public function testCommandGroupsOutputByTable(): void
    {
        $this->commandTester->execute([]);

        $output = $this->commandTester->getDisplay();

        if (str_contains($output, '❌ Found')) {
            $this->assertThat(
                $output,
                $this->logicalOr(
                    $this->stringContains('Table: '),
                    $this->stringContains('✅ The database structure matches the migrations perfectly.')
                )
            );

            $this->assertStringContainsString('Summary', $output);
        }
    }
}
