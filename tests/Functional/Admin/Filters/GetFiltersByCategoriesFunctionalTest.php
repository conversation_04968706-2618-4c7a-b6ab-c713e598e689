<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Filters;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\User;
use App\Entity\UserLogin;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCategoriesFiltersEndpoint;
use App\Tests\Functional\HelperTrait\FilterHelperTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class GetFiltersByCategoriesFunctionalTest extends FunctionalTestCase
{
    use FilterHelperTrait;

    private ?User $user = null;
    private array $defaultUserRoles = [];

    public function setUp(): void
    {
        parent::setUp();
        $this->user = $this->getDefaultUser();
        $this->defaultUserRoles = $this->getDefaultUser()->getRoles();
    }

    #[DataProvider('provideFiltersCategoriesOrder')]
    public function testItineraryOrder(array $categories, array $expected)
    {
        foreach ($categories as $key => $category) {
            $category = $this->createAndGetFilterCategory(name: $category['name'], sort: $category['sort'], is_ranking: $category['is_ranking']);
            $this->createAndGetFilter(name: "Example Filter {$key}", category: $category);
        }
        $userToken = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminCategoriesFiltersEndpoint::filterCategoriesEndpoint(),
            bearerToken: $userToken
        );
        $this->assertEquals(200, $response->getStatusCode());
        $data = $this->extractResponseData($response);
        $this->assertEquals($expected, $data);
    }

    public static function provideFiltersCategoriesOrder(): \Generator
    {
        yield 'One category One expected' => [
            'categories' => [['name' => 'Tienda (ES)', 'sort' => 1, 'is_ranking' => false]],
            'expected' => [
                [
                    'id' => 1,
                    'name' => 'Tienda (ES)',
                    'filters' => [['id' => 1, 'name' => 'Example Filter 0']],
                ],
            ],
        ];

        yield 'Multiple categories with different sort values' => [
            'categories' => [
                ['name' => 'Deparmento (ES)', 'sort' => 3, 'is_ranking' => true],
                ['name' => 'País (ES)', 'sort' => 2, 'is_ranking' => true],
                ['name' => 'Tienda (ES)', 'sort' => 1, 'is_ranking' => false],
            ],
            'expected' => [
                [
                    'id' => 3,
                    'name' => 'Tienda (ES)',
                    'filters' => [['id' => 3, 'name' => 'Example Filter 2']],
                ],
                [
                    'id' => 2,
                    'name' => 'País (ES)',
                    'filters' => [['id' => 2, 'name' => 'Example Filter 1']],
                ],
                [
                    'id' => 1,
                    'name' => 'Deparmento (ES)',
                    'filters' => [['id' => 1, 'name' => 'Example Filter 0']],
                ],
            ],
        ];
        yield 'Categories with same sort value' => [
            'categories' => [
                ['name' => 'País (ES)', 'sort' => 1, 'is_ranking' => true],
                ['name' => 'Tienda (ES)', 'sort' => 1, 'is_ranking' => false],
            ],
            'expected' => [
                [
                    'id' => 1,
                    'name' => 'País (ES)',
                    'filters' => [['id' => 1, 'name' => 'Example Filter 0']],
                ],
                [
                    'id' => 2,
                    'name' => 'Tienda (ES)',
                    'filters' => [['id' => 2, 'name' => 'Example Filter 1']],
                ],
            ],
        ];
        yield 'Categories with distint sorts' => [
            'categories' => [
                ['name' => 'Deparmento (ES)', 'sort' => 3, 'is_ranking' => true],
                ['name' => 'Tienda (ES)', 'sort' => 1, 'is_ranking' => false],
            ],
            'expected' => [
                [
                    'id' => 2,
                    'name' => 'Tienda (ES)',
                    'filters' => [['id' => 2, 'name' => 'Example Filter 1']],
                ],
                [
                    'id' => 1,
                    'name' => 'Deparmento (ES)',
                    'filters' => [['id' => 1, 'name' => 'Example Filter 0']],
                ],
            ],
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    #[DataProvider('managerFiltersDataProvider')]
    public function testManagerFilters(array $roles, array $expected, ?string $managerFilterName = null)
    {
        $this->user->setRoles($roles);
        $this->getEntityManager()->flush();

        $category1 = $this->createAndGetFilterCategory(
            name: 'Category 1',
            sort: 1
        );

        $category2 = $this->createAndGetFilterCategory(
            name: 'Category 2',
            sort: 2
        );

        $this->createAndGetFilter(
            name: 'Filter 1',
            category: $category1,
            sort: 1,
        );

        $this->createAndGetFilter(
            name: 'Filter 2',
            category: $category1,
            sort: 2,
        );

        $this->createAndGetFilter(
            name: 'Filter 3',
            category: $category2,
            sort: 3,
        );

        if ($managerFilterName) {
            $managerFilter = $this->getEntityManager()->getRepository(Filter::class)->findOneBy(['name' => $managerFilterName]);
            $this->user->addManagerFilter($managerFilter);
            $this->getEntityManager()->flush();
        }

        $userToken = $this->loginAndGetToken();
        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminCategoriesFiltersEndpoint::filterCategoriesEndpoint(),
            bearerToken: $userToken
        );
        $this->assertEquals(200, $response->getStatusCode());
        $data = $this->extractResponseData($response);
        $this->assertEquals($expected, $data);
    }

    public static function managerFiltersDataProvider(): \Generator
    {
        yield 'Admin user' => [
            'roles' => ['ROLE_ADMIN'],
            'expected' => [
                [
                    'id' => 1,
                    'name' => 'Category 1',
                    'filters' => [
                        ['id' => 1, 'name' => 'Filter 1'],
                        ['id' => 2, 'name' => 'Filter 2'],
                    ],
                ],
                [
                    'id' => 2,
                    'name' => 'Category 2',
                    'filters' => [['id' => 3, 'name' => 'Filter 3']],
                ],
            ],
        ];

        yield 'Manager user' => [
            'roles' => ['ROLE_MANAGER'],
            'expected' => [
                [
                    'id' => 2,
                    'name' => 'Category 2',
                    'filters' => [['id' => 3, 'name' => 'Filter 3']],
                ],
            ],
            'managerFilterName' => 'Filter 3',
        ];

        yield 'Admin & manager user' => [
            'roles' => ['ROLE_ADMIN', 'ROLE_MANAGER'],
            'expected' => [
                [
                    'id' => 1,
                    'name' => 'Category 1',
                    'filters' => [
                        ['id' => 1, 'name' => 'Filter 1'],
                        ['id' => 2, 'name' => 'Filter 2'],
                    ],
                ],
                [
                    'id' => 2,
                    'name' => 'Category 2',
                    'filters' => [['id' => 3, 'name' => 'Filter 3']],
                ],
            ],
        ];
    }

    /**
     * @throws Exception
     * @throws MappingException
     * @throws ORMException
     * @throws OptimisticLockException
     */
    protected function tearDown(): void
    {
        $this->getDefaultUser()->setRoles($this->defaultUserRoles);
        $this->getEntityManager()->flush();

        $this->truncateEntities(
            [
                FilterCategory::class,
                Filter::class,
                UserLogin::class,
            ]
        );

        parent::tearDown();
    }
}
