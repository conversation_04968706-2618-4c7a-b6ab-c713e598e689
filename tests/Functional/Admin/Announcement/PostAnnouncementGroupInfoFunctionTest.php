<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\AnnouncementHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementGroupEndpoints;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCriteria;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;
use App\V2\Domain\VirtualMeeting\VirtualMeetingType;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\Persistence\Mapping\MappingException;

class PostAnnouncementGroupInfoFunctionTest extends FunctionalTestCase
{
    use AnnouncementHelperTrait;

    /**
     * @throws ORMException
     * @throws \DateMalformedStringException
     * @throws InfrastructureException
     */
    public function testManageAnnouncementGroupSessionVirtualMeeting(): void
    {
        /** @var VirtualMeetingRepository $virtualMeetingRepository */
        $virtualMeetingRepository = $this->getService(VirtualMeetingRepository::class);
        $results = $virtualMeetingRepository->findBy(
            VirtualMeetingCriteria::createEmpty()
                ->filterByType(VirtualMeetingType::Fixed)
        );
        $this->assertTrue($results->isEmpty());

        $token = $this->loginAndGetToken();

        $announcementStartAt = (new \DateTimeImmutable('tomorrow'))->setTime(10, 0, 0);
        $announcementFinishAt = $announcementStartAt->modify('+2 day');

        $course = $this->createAndGetCourse(
            typeCourse: $this->getTypeCourse(
                code: 'virtual_classroom',
            ),
        );

        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            startAt: $announcementStartAt,
            finishAt: $announcementFinishAt,
        );

        $announcementGroup = $this->createAndGetAnnouncementGroup(announcement: $announcement);

        $this->createAndGetAnnouncementUser(
            announcement: $announcement,
            user: $this->getDefaultUser(),
        );

        $timeZone = new \DateTimeZone('Europe/Madrid');
        $sessionStartAt = $announcementStartAt->modify('+1 hour')->setTimezone($timeZone);
        $sessionFinishAt = $sessionStartAt->modify('+1 hour')->setTimezone($timeZone);

        $sessionData = [
            'id' => -1,
            'startAt' => $sessionStartAt->format('Y-m-d H:i:s'),
            'finishAt' => $sessionFinishAt->format('Y-m-d H:i:s'),
            'timezone' => 'Europe/Madrid',
            'url' => '',
            'type' => 'VIRTUAL',
            'providerId' => '',
            'session_number' => 1,
            'virtual_meeting_type' => 'fixed',
            'virtual_meeting_url' => 'https://meet.google.com/test-url',
        ];

        $basicPayload = $this->getBasicPayload(
            announcementId: $announcement->getId(),
            groupId: $announcementGroup->getId(),
        );

        $basicPayload['data'][0]['sessions'][] = $sessionData;

        // Create the session
        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminAnnouncementGroupEndpoints::postAnnouncementGroupEndpoint(),
            body: $basicPayload,
            bearerToken: $token,
        );

        $this->assertEquals(200, $response->getStatusCode());

        $results = $virtualMeetingRepository->findBy(
            VirtualMeetingCriteria::createEmpty()
                ->filterByType(VirtualMeetingType::Fixed)
        );
        $this->assertFalse($results->isEmpty());
        $this->assertEquals(1, $results->count());
        $virtualMeeting = $results->first();
        $this->assertEquals('https://meet.google.com/test-url', $virtualMeeting->getUrl());

        // Get announcement to get group sessionId
        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::announcementFormAnnouncementEndpoint($announcement->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(200, $response->getStatusCode());
        $data = $this->extractResponseData($response);
        $this->assertCount(1, $data['students'][0]['sessions']);
        $sessionResponseData = $data['students'][0]['sessions'][0];
        $sessionId = $sessionResponseData['id'];

        $announcementGroupSessionRepository = $this->getRepository(AnnouncementGroupSession::class);
        /** @var AnnouncementGroupSession $announcementGroupSession */
        $announcementGroupSession = $announcementGroupSessionRepository->find($sessionId);

        $this->assertNotNull($announcementGroupSession->getVirtualMeetingId());

        $sessionData['id'] = $announcementGroupSession->getId();
        $sessionData['virtual_meeting_url'] = 'https://meet.google.com/updated-url';

        $basicPayload = $this->getBasicPayload(
            announcementId: $announcement->getId(),
            groupId: $announcementGroup->getId(),
        );

        $basicPayload['data'][0]['sessions'][] = $sessionData;

        // Update the Virtual Meeting URL
        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminAnnouncementGroupEndpoints::postAnnouncementGroupEndpoint(),
            body: $basicPayload,
            bearerToken: $token,
        );

        $this->assertEquals(200, $response->getStatusCode());

        $results = $virtualMeetingRepository->findBy(
            VirtualMeetingCriteria::createEmpty()
                ->filterByType(VirtualMeetingType::Fixed)
        );

        $this->assertFalse($results->isEmpty());
        $this->assertEquals(1, $results->count());
        $virtualMeeting = $results->first();
        $this->assertEquals('https://meet.google.com/updated-url', $virtualMeeting->getUrl());

        // Remove Session and check that VirtualMeeting is removed
        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminAnnouncementGroupEndpoints::deleteAnnouncementGroupSession(),
            body: [
                'id' => $sessionId,
            ],
            bearerToken: $token,
        );
        $this->assertEquals(200, $response->getStatusCode());

        $results = $virtualMeetingRepository->findBy(
            VirtualMeetingCriteria::createEmpty()
                ->filterByType(VirtualMeetingType::Fixed)
        );
        $this->assertTrue($results->isEmpty());
        $this->assertEquals(0, $results->count());

        // Create new session
        $sessionData = [
            'id' => -1,
            'startAt' => $sessionStartAt->format('Y-m-d H:i:s'),
            'finishAt' => $sessionFinishAt->format('Y-m-d H:i:s'),
            'timezone' => 'Europe/Madrid',
            'url' => '',
            'type' => 'VIRTUAL',
            'providerId' => '',
            'session_number' => 1,
            'virtual_meeting_type' => 'fixed',
            'virtual_meeting_url' => 'https://meet.google.com/test-url',
        ];

        $basicPayload = $this->getBasicPayload(
            announcementId: $announcement->getId(),
            groupId: $announcementGroup->getId(),
        );
        $basicPayload['data'][0]['sessions'][] = $sessionData;

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminAnnouncementGroupEndpoints::postAnnouncementGroupEndpoint(),
            body: $basicPayload,
            bearerToken: $token,
        );
        $this->assertEquals(200, $response->getStatusCode());

        $results = $virtualMeetingRepository->findBy(
            VirtualMeetingCriteria::createEmpty()
                ->filterByType(VirtualMeetingType::Fixed)
        );
        $this->assertFalse($results->isEmpty());
        $this->assertEquals(1, $results->count());
        $virtualMeeting = $results->first();
        $this->assertEquals('https://meet.google.com/test-url', $virtualMeeting->getUrl());

        // Get announcement to get group sessionId
        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::announcementFormAnnouncementEndpoint($announcement->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(200, $response->getStatusCode());
        $data = $this->extractResponseData($response);
        $this->assertCount(1, $data['students'][0]['sessions']);
        $sessionResponseData = $data['students'][0]['sessions'][0];
        $sessionId = $sessionResponseData['id'];

        $announcementGroupSessionRepository = $this->getRepository(AnnouncementGroupSession::class);
        /** @var AnnouncementGroupSession $announcementGroupSession */
        $announcementGroupSession = $announcementGroupSessionRepository->find($sessionId);

        $this->assertNotNull($announcementGroupSession->getVirtualMeetingId());

        // Change session type to Presential and check that VirtualMeeting is removed
        $sessionData['id'] = $announcementGroupSession->getId();
        $sessionData['type'] = 'PRESENTIAL';

        $basicPayload = $this->getBasicPayload(
            announcementId: $announcement->getId(),
            groupId: $announcementGroup->getId(),
        );
        $basicPayload['data'][0]['sessions'][] = $sessionData;

        $response = $this->makeAdminApiRequest(
            method: 'POST',
            uri: AdminAnnouncementGroupEndpoints::postAnnouncementGroupEndpoint(),
            body: $basicPayload,
            bearerToken: $token,
        );
        $this->assertEquals(200, $response->getStatusCode());

        $results = $virtualMeetingRepository->findBy(
            VirtualMeetingCriteria::createEmpty()
                ->filterByType(VirtualMeetingType::Fixed)
        );
        $this->assertTrue($results->isEmpty());
        $this->assertEquals(0, $results->count());
    }

    private function getBasicPayload(int $announcementId, int $groupId): array
    {
        return [
            'id' => $announcementId,
            'data' => [
                [
                    'code' => null,
                    'companyCif' => null,
                    'companyProfile' => null,
                    'cost' => '0.00',
                    'denomination' => null,
                    'fileNumber' => null,
                    'id' => $groupId,
                    'numberOfSessions' => null,
                    'place' => null,
                    'typeMoney' => null,
                    'sessions' => [],
                ],
            ],
        ];
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            AnnouncementUser::class,
            Announcement::class,
            Course::class,
            CourseCategory::class,
            AnnouncementGroup::class,
            AnnouncementGroupSession::class,
        ]);
        parent::tearDown();
    }
}
