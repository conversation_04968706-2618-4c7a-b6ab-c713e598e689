<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\FilesTask;
use App\Entity\TaskCourse;
use App\Service\FileService;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\TaskEndpoints;
use App\Tests\Functional\HelperTrait\FileTaskHelperTrait;
use App\Tests\Functional\HelperTrait\TaskCourseHelperTrait;
use App\Tests\Functional\SettingsConstants;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use PHPUnit\Framework\MockObject\Exception;

class DeleteFileTaskAnnouncementFunctionalTest extends FunctionalTestCase
{
    use FileTaskHelperTrait;
    use TaskCourseHelperTrait;

    private array $tempFiles = [];

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSuccessDeleteFileTaskAnnouncement(): void
    {
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);
        $courseTask = $this->createAndGetTaskCourse(course: $course, announcement: $announcement);
        $userToken = $this->loginAndGetToken();

        $uploadDir = self::getContainer()->getParameter(SettingsConstants::TEST_ROUTE_TASK_FILE);
        $fileName = 'task_file_test_' . uniqid() . '.pdf';
        $tempFile = $uploadDir . '/' . $fileName;
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        file_put_contents($tempFile, 'test content');
        $this->tempFiles[] = $tempFile;
        $fileTask = $this->createAndGetFileTask(taskCourse: $courseTask, fileName: $fileName);

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: TaskEndpoints::deleteTaskFileEndpoint(
                fileTaskId: $fileTask->getId(),
            ),
            bearerToken: $userToken,
        );
        $this->assertEquals(204, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testNotFoundFileFileTaskAnnouncementDelete(): void
    {
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);
        $courseTask = $this->createAndGetTaskCourse(course: $course, announcement: $announcement);
        $userToken = $this->loginAndGetToken();

        $fileName = 'task_file_test_' . uniqid() . '.pdf';
        $fileTask = $this->createAndGetFileTask(taskCourse: $courseTask, fileName: $fileName);

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: TaskEndpoints::deleteTaskFileEndpoint(
                fileTaskId: $fileTask->getId(),
            ),
            bearerToken: $userToken,
        );
        $this->assertEquals(404, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertTrue($responseData['error']);
        $this->assertEquals('File not exist', $responseData['data']);
    }

    /**
     * @throws OptimisticLockException
     * @throws Exception
     * @throws ORMException
     */
    public function testDeleteFileTaskUnlinkFails(): void
    {
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);
        $courseTask = $this->createAndGetTaskCourse(course: $course, announcement: $announcement);
        $userToken = $this->loginAndGetToken();

        $uploadDir = self::getContainer()->getParameter(SettingsConstants::TEST_ROUTE_TASK_FILE);
        $fileName = 'task_file_test_' . uniqid() . '.pdf';
        $tempFile = $uploadDir . '/' . $fileName;

        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        file_put_contents($tempFile, 'test content');
        $this->tempFiles[] = $tempFile;

        $fileTask = $this->createAndGetFileTask(taskCourse: $courseTask, fileName: $fileName);

        // 👇 Mockear FileManager
        $fileManagerMock = $this->createMock(FileService::class);
        $fileManagerMock->method('deleteFile')->willReturn(false);

        self::getContainer()->set(FileService::class, $fileManagerMock);

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: TaskEndpoints::deleteTaskFileEndpoint(fileTaskId: $fileTask->getId()),
            bearerToken: $userToken,
        );

        $this->assertEquals(500, $response->getStatusCode());
        $responseData = json_decode($response->getContent(), true);
        $this->assertTrue($responseData['error']);
        $this->assertEquals('Failed to delete file', $responseData['data']);
    }

    /**
     * @throws OptimisticLockException
     * @throws Exception
     * @throws ORMException
     */
    public function testDeleteFileTaskCatchException(): void
    {
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);
        $courseTask = $this->createAndGetTaskCourse(course: $course, announcement: $announcement);
        $userToken = $this->loginAndGetToken();

        $uploadDir = self::getContainer()->getParameter(SettingsConstants::TEST_ROUTE_TASK_FILE);
        $fileName = 'task_file_test_' . uniqid() . '.pdf';
        $tempFile = $uploadDir . '/' . $fileName;

        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        file_put_contents($tempFile, 'test content');
        $this->tempFiles[] = $tempFile;

        $fileTask = $this->createAndGetFileTask(taskCourse: $courseTask, fileName: $fileName);

        $fileManagerMock = $this->createMock(FileService::class);
        $fileManagerMock->method('deleteFile')->willThrowException(new \RuntimeException('Filesystem crashed'));

        self::getContainer()->set(FileService::class, $fileManagerMock);

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: TaskEndpoints::deleteTaskFileEndpoint(fileTaskId: $fileTask->getId()),
            bearerToken: $userToken,
        );

        $this->assertEquals(500, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertTrue($responseData['error']);
        $this->assertEquals('Filesystem crashed', $responseData['data']);
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            Announcement::class,
            TaskCourse::class,
            FilesTask::class,
        ]);

        // Cleanup temporary files
        foreach ($this->tempFiles as $tempFile) {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }

        parent::tearDown();
    }
}
