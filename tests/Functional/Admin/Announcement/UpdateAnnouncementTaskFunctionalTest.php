<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementManager;
use App\Entity\AnnouncementTutor;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\TaskCourse;
use App\Entity\User;
use App\Entity\UserLogin;
use App\Entity\UserManage;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\TaskCourseHelperTrait;
use App\Tests\Functional\SettingsConstants;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\TransactionRequiredException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class UpdateAnnouncementTaskFunctionalTest extends FunctionalTestCase
{
    use AnnouncementManagerFixtureTrait;
    use TaskCourseHelperTrait;

    private ?User $testUser1 = null;
    private ?User $testUser2 = null;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->testUser1 = $this->createAndGetUser(email: '<EMAIL>');
        $this->testUser2 = $this->createAndGetUser(
            roles: ['ROLE_USER', 'ROLE_MANAGER'],
            email: '<EMAIL>'
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws TransactionRequiredException
     * @throws ORMException
     */
    #[DataProvider('providerUpdateAnnouncementTaskAuthorization')]
    public function testUpdateAnnouncementTaskAuthorization(
        array $roles,
        bool $isCreatorAnnouncement,
        bool $shareAnnouncementWithUser,
        int $expectedStatusCode,
    ): void {
        $this->updateSettingValue(value: 'true', code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING);

        $this->testUser1->setRoles($roles);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->testUser1);

        $announcementCreator = $isCreatorAnnouncement ? $this->testUser1 : $this->testUser2;

        $course = $this->createAndGetCourse(createdBy: $announcementCreator);

        $today = new \DateTimeImmutable('today');
        $baseData = [
            'title' => 'Default title',
            'description' => 'Valid description',
            'start_date' => $today,
            'deadline' => $today,
            'announcementStartedDate' => $today,
            'announcementEndDate' => $today,
            'groups' => json_encode([['id' => 1, 'name' => 'Grupo 1', 'numGroup' => 1, 'code' => 'G1']]),
            'visible' => true,
        ];

        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            startAt: $baseData['announcementStartedDate'],
            finishAt: $baseData['announcementEndDate'],
            createdBy: $announcementCreator,
        );

        $taskCourse = $this->createAndGetTaskCourse(
            course: $course,
            announcement: $announcement,
            createdBy: $announcementCreator
        );

        $announcementGroup = $this->createAndGetAnnouncementGroup(announcement: $announcement);

        if ($shareAnnouncementWithUser && !$isCreatorAnnouncement) {
            if (\in_array(User::ROLE_TUTOR, $roles, true)) {
                $this->createAndGetAnnouncementTutor(announcement: $announcement, announcementGroup: $announcementGroup, tutor: $this->testUser1);
            } else {
                $this->setAndGetAnnouncementManager(
                    userId: $this->testUser1->getId(),
                    announcementId: $announcement->getId(),
                );
            }
        }

        $token = $this->loginAndGetTokenForUser($this->testUser1);

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::updateAnnouncementTaskEndpoint($announcement->getId(), $taskCourse->getId()),
            queryParams: [
                'title' => $baseData['title'],
                'description' => $baseData['description'],
                'start_date' => $baseData['start_date']->format('Y-m-d H:i:s'),
                'deadline' => $baseData['deadline']->format('Y-m-d H:i:s'),
                'announcementStartedDate' => $baseData['announcementStartedDate']->format('Y-m-d H:i:s'),
                'announcementEndDate' => $baseData['announcementEndDate']->format('Y-m-d H:i:s'),
                'groups' => $baseData['groups'],
                'visible' => $baseData['visible'],
            ],
            bearerToken: $token,
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
    }

    public static function providerUpdateAnnouncementTaskAuthorization(): \Generator
    {
        yield 'Super Admin can update announcement task' => [
            'roles' => [User::ROLE_SUPER_ADMIN],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 200,
        ];

        yield 'Admin can update announcement task' => [
            'roles' => [User::ROLE_ADMIN],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 200,
        ];

        yield 'Manager can update announcement task if is announcement creator' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorAnnouncement' => true,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 200,
        ];

        yield 'Manager can update announcement task if the announcement is shared with him' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => 200,
        ];

        yield 'Creator cannot update announcement task' => [
            'roles' => [User::ROLE_CREATOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 403,
        ];

        yield 'Tutor can update announcement task if announcement is shared with him' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => 200,
        ];

        yield 'Tutor cannot create announcement task if announcement is not shared with him' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 403,
        ];

        yield 'Support cannot create announcement task' => [
            'roles' => [User::ROLE_SUPPORT],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 403,
        ];

        yield 'Inspector cannot create announcement task' => [
            'roles' => [User::ROLE_INSPECTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 302,
        ];

        yield 'User cannot create announcement task' => [
            'roles' => [User::ROLE_USER],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 302,
        ];
    }

    #[DataProvider('providerUpdateAnnouncementTaskValidations')]
    public function testUpdateAnnouncementTaskValidations(
        string $title,
        string $description,
        ?\DateTimeImmutable $start_date,
        ?\DateTimeImmutable $deadline,
        \DateTimeImmutable $announcementStartedDate,
        \DateTimeImmutable $announcementEndDate,
        array $groups,
        bool $visible,
        int $expectedStatusCode,
        string $expectedErrorMessage,
    ): void {
        $this->updateSettingValue(value: 'true', code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING);
        $user = $this->getDefaultUser();

        $course = $this->createAndGetCourse(createdBy: $user);

        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            startAt: $announcementStartedDate,
            finishAt: $announcementEndDate,
            createdBy: $user,
        );

        $taskCourse = $this->createAndGetTaskCourse(
            course: $course,
            announcement: $announcement,
            createdBy: $user
        );

        $token = $this->loginAndGetTokenForUser($user);

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::updateAnnouncementTaskEndpoint(announcementId: $announcement->getId(), taskId: $taskCourse->getId()),
            queryParams: [
                'title' => $title,
                'description' => $description,
                'start_date' => !$start_date ? $start_date : $start_date->format('Y-m-d H:i:s'),
                'deadline' => !$deadline ? $deadline : $deadline->format('Y-m-d H:i:s'),
                'announcementStartedDate' => $announcementStartedDate->format('Y-m-d H:i:s'),
                'announcementEndDate' => $announcementEndDate->format('Y-m-d H:i:s'),
                'groups' => json_encode($groups),
                'visible' => $visible,
            ],
            bearerToken: $token,
        );
        $responseData = $this->extractResponseData($response);
        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
        $this->assertEquals($expectedErrorMessage, $responseData);
    }

    public static function providerUpdateAnnouncementTaskValidations(): \Generator
    {
        $today = new \DateTimeImmutable('today');
        yield 'title is empty' => [
            'title' => '',
            'description' => 'Valid description',
            'start_date' => $today,
            'deadline' => $today,
            'announcementStartedDate' => $today,
            'announcementEndDate' => $today,
            'groups' => [['id' => 1, 'name' => 'Grupo 1', 'numGroup' => 1, 'code' => 'G1']],
            'visible' => true,
            'expectedStatusCode' => 400,
            'expectedErrorMessage' => 'TASK_COURSE.TITLE_REQUIRED',
        ];
        yield 'description is empty' => [
            'title' => 'Valid title',
            'description' => '',
            'start_date' => $today,
            'deadline' => $today,
            'announcementStartedDate' => $today,
            'announcementEndDate' => $today,
            'groups' => [['id' => 1, 'name' => 'Grupo 1', 'numGroup' => 1, 'code' => 'G1']],
            'visible' => true,
            'expectedStatusCode' => 400,
            'expectedErrorMessage' => 'TASK_COURSE.DESCRIPTION_REQUIRED',
        ];
        yield 'start_date is empty' => [
            'title' => 'Valid title',
            'description' => 'Valid description',
            'start_date' => null,
            'deadline' => $today,
            'announcementStartedDate' => $today,
            'announcementEndDate' => $today,
            'groups' => [['id' => 1, 'name' => 'Grupo 1', 'numGroup' => 1, 'code' => 'G1']],
            'visible' => true,
            'expectedStatusCode' => 400,
            'expectedErrorMessage' => 'TASK_COURSE.START_REQUIRED',
        ];
        yield 'deadline is empty' => [
            'title' => 'Valid title',
            'description' => 'Valid description',
            'start_date' => $today,
            'deadline' => null,
            'announcementStartedDate' => $today,
            'announcementEndDate' => $today,
            'groups' => [['id' => 1, 'name' => 'Grupo 1', 'numGroup' => 1, 'code' => 'G1']],
            'visible' => true,
            'expectedStatusCode' => 400,
            'expectedErrorMessage' => 'TASK_COURSE.DEADLINE_REQUIRED',
        ];
        yield 'groups is empty' => [
            'title' => 'Valid title',
            'description' => 'Valid description',
            'start_date' => $today,
            'deadline' => $today,
            'announcementStartedDate' => $today,
            'announcementEndDate' => $today,
            'groups' => [],
            'visible' => true,
            'expectedStatusCode' => 400,
            'expectedErrorMessage' => 'TASK_COURSE.GROUP_REQUIRED',
        ];
        yield 'started date is less than announcement started date' => [
            'title' => 'Valid title',
            'description' => 'Valid description',
            'start_date' => $today,
            'deadline' => $today,
            'announcementStartedDate' => $today->modify('+1 day'),
            'announcementEndDate' => $today,
            'groups' => [['id' => 1, 'name' => 'Grupo 1', 'numGroup' => 1, 'code' => 'G1']],
            'visible' => true,
            'expectedStatusCode' => 400,
            'expectedErrorMessage' => 'TASK_COURSE.DATES_OUT_OF_RANGE',
        ];
        yield 'deadline date is greater than announcement end date' => [
            'title' => 'Valid title',
            'description' => 'Valid description',
            'start_date' => $today,
            'deadline' => $today->modify('+1 day'),
            'announcementStartedDate' => $today,
            'announcementEndDate' => $today,
            'groups' => [['id' => 1, 'name' => 'Grupo 1', 'numGroup' => 1, 'code' => 'G1']],
            'visible' => true,
            'expectedStatusCode' => 400,
            'expectedErrorMessage' => 'TASK_COURSE.DATES_OUT_OF_RANGE',
        ];
        yield 'started date is less than announcement started date and deadline date is greater than announcement end date' => [
            'title' => 'Valid title',
            'description' => 'Valid description',
            'start_date' => $today,
            'deadline' => $today->modify('+1 day'),
            'announcementStartedDate' => $today->modify('+1 day'),
            'announcementEndDate' => $today,
            'groups' => [['id' => 1, 'name' => 'Grupo 1', 'numGroup' => 1, 'code' => 'G1']],
            'visible' => true,
            'expectedStatusCode' => 400,
            'expectedErrorMessage' => 'TASK_COURSE.DATES_OUT_OF_RANGE',
        ];
        yield 'started date is greater than deadline date' => [
            'title' => 'Valid title',
            'description' => 'Valid description',
            'start_date' => $today->modify('+1 day'),
            'deadline' => $today,
            'announcementStartedDate' => $today->modify('+1 day'),
            'announcementEndDate' => $today->modify('+1 day'),
            'groups' => [['id' => 1, 'name' => 'Grupo 1', 'numGroup' => 1, 'code' => 'G1']],
            'visible' => true,
            'expectedStatusCode' => 400,
            'expectedErrorMessage' => 'TASK_COURSE.INVALID_DATE_RANGE',
        ];
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            CourseCategory::class,
            Announcement::class,
            AnnouncementGroup::class,
            AnnouncementTutor::class,
            AnnouncementManager::class,
            TaskCourse::class,
            UserLogin::class,
            UserManage::class,
        ]);

        $this->hardDeleteUsersByIds([
            $this->testUser1->getId(),
            $this->testUser2->getId(),
        ]);

        parent::tearDown();
    }
}
