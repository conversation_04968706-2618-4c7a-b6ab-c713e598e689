<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementManager;
use App\Entity\AnnouncementTutor;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\TaskCourse;
use App\Entity\User;
use App\Entity\UserLogin;
use App\Entity\UserManage;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\SettingsConstants;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\TransactionRequiredException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class CreateAnnouncementTaskFunctionalTest extends FunctionalTestCase
{
    use AnnouncementManagerFixtureTrait;

    private ?User $testUser1 = null;
    private ?User $testUser2 = null;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->testUser1 = $this->createAndGetUser(email: '<EMAIL>');
        $this->testUser2 = $this->createAndGetUser(
            roles: ['ROLE_USER', 'ROLE_MANAGER'],
            email: '<EMAIL>'
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws TransactionRequiredException
     * @throws ORMException
     */
    #[DataProvider('providerCreateAnnouncementTaskAuthorization')]
    public function testCreateAnnouncementTaskAuthorization(
        array $roles,
        bool $isCreatorAnnouncement,
        bool $shareAnnouncementWithUser,
        int $expectedStatusCode,
    ): void {
        $this->updateSettingValue(value: 'true', code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING);

        $this->testUser1->setRoles($roles);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->testUser1);

        $announcementCreator = $isCreatorAnnouncement ? $this->testUser1 : $this->testUser2;

        $course = $this->createAndGetCourse(createdBy: $announcementCreator);

        $today = new \DateTimeImmutable('today');
        $baseData = [
            'title' => 'Default title',
            'description' => 'Valid description',
            'start_date' => $today,
            'deadline' => $today,
            'announcementStartedDate' => $today,
            'announcementEndDate' => $today,
            'groups' => json_encode([['id' => 1, 'name' => 'Grupo 1', 'numGroup' => 1, 'code' => 'G1']]),
            'visible' => true,
        ];

        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            startAt: $baseData['announcementStartedDate'],
            finishAt: $baseData['announcementEndDate'],
            createdBy: $announcementCreator,
        );

        $announcementGroup = $this->createAndGetAnnouncementGroup(announcement: $announcement);

        if ($shareAnnouncementWithUser && !$isCreatorAnnouncement) {
            if (\in_array(User::ROLE_TUTOR, $roles, true)) {
                $this->createAndGetAnnouncementTutor(announcement: $announcement, announcementGroup: $announcementGroup, tutor: $this->testUser1);
            } else {
                $this->setAndGetAnnouncementManager(
                    userId: $this->testUser1->getId(),
                    announcementId: $announcement->getId(),
                );
            }
        }

        $token = $this->loginAndGetTokenForUser($this->testUser1);

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::createAnnouncementTaskEndpoint($announcement->getId()),
            queryParams: [
                'title' => $baseData['title'],
                'description' => $baseData['description'],
                'start_date' => $baseData['start_date']->format('Y-m-d H:i:s'),
                'deadline' => $baseData['deadline']->format('Y-m-d H:i:s'),
                'announcementStartedDate' => $baseData['announcementStartedDate']->format('Y-m-d H:i:s'),
                'announcementEndDate' => $baseData['announcementEndDate']->format('Y-m-d H:i:s'),
                'groups' => $baseData['groups'],
                'visible' => $baseData['visible'],
            ],
            bearerToken: $token,
        );

        $this->assertEquals($expectedStatusCode, $response->getStatusCode());
    }

    public static function providerCreateAnnouncementTaskAuthorization(): \Generator
    {
        yield 'Super Admin can create announcement task' => [
            'roles' => [User::ROLE_SUPER_ADMIN],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 201,
        ];

        yield 'Admin can create announcement task' => [
            'roles' => [User::ROLE_ADMIN],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 201,
        ];

        yield 'Manager can create announcement task if is announcement creator' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorAnnouncement' => true,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 201,
        ];

        yield 'Manager can create announcement task if the announcement is shared with him' => [
            'roles' => [User::ROLE_MANAGER],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => 201,
        ];

        yield 'Creator cannot create announcement task' => [
            'roles' => [User::ROLE_CREATOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 403,
        ];

        yield 'Tutor can create announcement task is shared with him' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => true,
            'expectedStatusCode' => 201,
        ];

        yield 'Tutor cannot create announcement task' => [
            'roles' => [User::ROLE_TUTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 403,
        ];

        yield 'Support cannot create announcement task' => [
            'roles' => [User::ROLE_SUPPORT],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 403,
        ];

        yield 'Inspector cannot create announcement task' => [
            'roles' => [User::ROLE_INSPECTOR],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 302,
        ];

        yield 'User cannot create announcement task' => [
            'roles' => [User::ROLE_USER],
            'isCreatorAnnouncement' => false,
            'shareAnnouncementWithUser' => false,
            'expectedStatusCode' => 302,
        ];
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            CourseCategory::class,
            Announcement::class,
            AnnouncementGroup::class,
            AnnouncementTutor::class,
            AnnouncementManager::class,
            TaskCourse::class,
            UserLogin::class,
            UserManage::class,
        ]);

        $this->hardDeleteUsersByIds([
            $this->testUser1->getId(),
            $this->testUser2->getId(),
        ]);

        parent::tearDown();
    }
}
