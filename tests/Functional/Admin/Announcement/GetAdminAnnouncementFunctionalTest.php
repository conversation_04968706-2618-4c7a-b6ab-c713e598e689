<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\Setting;
use App\Entity\SettingGroup;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\SettingHelperTrait;
use App\Tests\Functional\SettingsConstants;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class GetAdminAnnouncementFunctionalTest extends FunctionalTestCase
{
    use SettingHelperTrait;

    private Course $course;
    private Announcement $announcement;
    private User $user;
    private ?Setting $databaseSetting;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = $this->getDefaultUser();
        $this->course = $this->createAndGetCourse();
        $this->announcement = $this->createAndGetAnnouncement(course: $this->course, createdBy: $this->user);
    }

    public function testGetAnnouncementStructure(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementEndPoint(
                announcementId: $this->announcement->getId(),
            ),
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $data = $this->extractResponseData($response);
        $this->assertArrayHasKey('alertsTutor', $data);
        $this->assertArrayHasKey('aprovalCriteria', $data);
        $this->assertArrayHasKey('bonification', $data);
        $this->assertArrayHasKey('channels', $data);
        $this->assertArrayHasKey('chatChannel', $data);
        $this->assertArrayHasKey('comunications', $data);
        $this->assertArrayHasKey('course', $data);
        $this->assertArrayHasKey('createdAt', $data);
        $this->assertArrayHasKey('createdAtText', $data);
        $this->assertArrayHasKey('diploma', $data);
        $this->assertArrayHasKey('extra', $data);
        $this->assertArrayHasKey('finishAt', $data);
        $this->assertArrayHasKey('groupBasicInfo', $data);
        $this->assertArrayHasKey('guideTitle', $data);
        $this->assertArrayHasKey('guideURL', $data);
        $this->assertArrayHasKey('hasAlertTutor', $data);
        $this->assertArrayHasKey('hasAprovedCriteria', $data);
        $this->assertArrayHasKey('hasBonification', $data);
        $this->assertArrayHasKey('hasComunication', $data);
        $this->assertArrayHasKey('hasDigitalSignature', $data);
        $this->assertArrayHasKey('hasDiploma', $data);
        $this->assertArrayHasKey('hasMaterials', $data);
        $this->assertArrayHasKey('hasReportZip', $data);
        $this->assertArrayHasKey('hasSurvey', $data);
        $this->assertArrayHasKey('hasTasks', $data);
        $this->assertArrayHasKey('hasTemporalization', $data);
        $this->assertArrayHasKey('id', $data);
        $this->assertArrayHasKey('inspector', $data);
        $this->assertArrayHasKey('mainIdentification', $data);
        $this->assertArrayHasKey('notifiedAt', $data);
        $this->assertArrayHasKey('numberOfSessions', $data);
        $this->assertArrayHasKey('objectives', $data);
        $this->assertArrayHasKey('percentageForAproved', $data);
        $this->assertArrayHasKey('server', $data);
        $this->assertArrayHasKey('shareEnabled', $data);
        $this->assertArrayHasKey('source', $data);
        $this->assertArrayHasKey('startAt', $data);
        $this->assertArrayHasKey('status', $data);
        $this->assertArrayHasKey('subsidized', $data);
        $this->assertArrayHasKey('survey', $data);
        $this->assertArrayHasKey('temporization', $data);
        $this->assertArrayHasKey('timezone', $data);
        $this->assertArrayHasKey('totalHours', $data);
        $this->assertArrayHasKey('type', $data);
        $this->assertArrayHasKey('usersPerGroup', $data);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testManagerSharedAnnouncementSetting(): void
    {
        $token = $this->loginAndGetToken();

        $oldSetting = $this->getEntityManager()
            ->getRepository(Setting::class)
            ->findOneBy(['code' => SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING]);

        $this->getEntityManager()->remove($oldSetting);
        $this->getEntityManager()->flush();

        $serviceYamlValue = $this->client
            ->getContainer()
            ->getParameter(SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING);

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementEndPoint(
                announcementId: $this->announcement->getId(),
            ),
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $data = $this->extractResponseData($response);

        // THe value is from service.yaml
        $this->assertEquals($serviceYamlValue, $data['shareEnabled']);

        $newSetting = $this->createAndGetSetting(
            id: $oldSetting->getId(),
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            name: $oldSetting->getName(),
            description: $oldSetting->getDescription(),
            sort: $oldSetting->getSort(),
            options: $oldSetting->getOptions(),
            type: $oldSetting->getType(),
            value: 'true',
            settingGroup: $this->getEntityManager()
                ->getRepository(SettingGroup::class)
                ->find($oldSetting->getSettingGroup()->getId()),
        );

        $this->client->enableReboot();

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementEndPoint(
                announcementId: $this->announcement->getId(),
            ),
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $data = $this->extractResponseData($response);

        // The value is from the database setting
        $this->assertEquals(filter_var($newSetting->getValue(), FILTER_VALIDATE_BOOLEAN), $data['shareEnabled']);

        $this->client->disableReboot();

        $newSetting = $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'false',
        );

        $this->client->enableReboot();

        $response = $this->makeAdminApiRequest(
            method: 'GET',
            uri: AdminAnnouncementEndpoints::getAnnouncementEndPoint(
                announcementId: $this->announcement->getId(),
            ),
            bearerToken: $token
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $data = $this->extractResponseData($response);

        // The value is from the database setting
        $this->assertEquals(filter_var($newSetting->getValue(), FILTER_VALIDATE_BOOLEAN), $data['shareEnabled']);

        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: $oldSetting->getValue(),
        );
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            CourseCategory::class,
            UserCourse::class,
            Announcement::class,
        ]);

        parent::tearDown();
    }
}
