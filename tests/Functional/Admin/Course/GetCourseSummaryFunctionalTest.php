<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseStatisticsEndpoints;
use App\Tests\Functional\V2\Fixtures\CourseCreatorFixtureTrait;
use App\Tests\Functional\V2\Fixtures\CourseManagerFixtureTrait;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetCourseSummaryFunctionalTest extends FunctionalTestCase
{
    use CourseCreatorFixtureTrait;
    use CourseManagerFixtureTrait;

    private Course $course1;
    private Course $course2;
    private Chapter $chapter1;
    private Chapter $chapter2;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setUp(): void
    {
        parent::setUp();
        $this->course1 = $this->createAndGetCourse(
            code: 'TEST-COURSE-1',
        );
        $this->course2 = $this->createAndGetCourse(
            code: 'TEST-COURSE-2',
        );
        $this->chapter1 = $this->createAndGetChapter(
            course: $this->course1,
            title: 'Chapter 1',
        );
        $this->chapter2 = $this->createAndGetChapter(
            course: $this->course1,
            title: 'Chapter 2',
        );
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Exception
     */
    public function testUnauthorizedUserCannotAccessCourseSummary(): void
    {
        $notAuthUser = $this->createAndGetUser(
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );

        $token = $this->loginAndGetTokenForUser($notAuthUser);

        $response = $this->makeAdminApiRequest(
            'GET',
            uri: AdminCourseStatisticsEndpoints::courseStatisticsSummaryEndpoint($this->course1->getId()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());

        $this->hardDeleteUsersByIds([$notAuthUser->getId()]);
    }

    /**
     * @throws Exception
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws CollectionException
     */
    #[DataProvider('userRolesProvider')]
    public function testUserRolesAccessCourseSummary(array $roles, int $expectedResponseCode, ?string $assignedCourseCode = null): void
    {
        $authUser = $this->createAndGetUser(
            roles: $roles,
            email: '<EMAIL>'
        );

        if ($assignedCourseCode) {
            $course = $this->getEntityManager()->getRepository(Course::class)->findOneBy(['code' => $assignedCourseCode]);
            if ($course) {
                $this->setAndGetCourseManagerInRepository(
                    userId: new Id($authUser->getId()),
                    courseId: new Id($course->getId()),
                );
            }
        }

        $token = $this->loginAndGetTokenForUser($authUser);

        $response = $this->makeAdminApiRequest(
            'GET',
            uri: AdminCourseStatisticsEndpoints::courseStatisticsSummaryEndpoint($this->course1->getId()),
            bearerToken: $token
        );

        $this->assertEquals($expectedResponseCode, $response->getStatusCode());

        $this->hardDeleteUsersByIds([$authUser->getId()]);
    }

    public static function userRolesProvider(): \Generator
    {
        yield 'Admin' => [
            'roles' => [User::ROLE_ADMIN],
            'expectedResponseCode' => Response::HTTP_OK,
        ];
        yield 'Manager with no course assigned' => [
            'roles' => [User::ROLE_MANAGER],
            'expectedResponseCode' => Response::HTTP_OK,
        ];
        yield 'Manager with valid course assigned' => [
            'roles' => [User::ROLE_MANAGER],
            'expectedResponseCode' => Response::HTTP_OK,
            'assignedCourseCode' => 'TEST-COURSE-1',
        ];
        yield 'Manager with no valid course assigned' => [
            'roles' => [User::ROLE_MANAGER],
            'expectedResponseCode' => Response::HTTP_FORBIDDEN,
            'assignedCourseCode' => 'TEST-COURSE-2',
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     */
    public function testUserCourseSummaryData()
    {
        // Summary for two users with progress on a chapters.
        $user1 = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: '<EMAIL>'
        );
        $user2 = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: '<EMAIL>'
        );
        $user1Course1Chapter1 = $this->createAndGetUserCourseChapter(
            userCourse: $this->createAndGetUserCourse(
                user: $user1,
                course: $this->course1,
            ),
            chapter: $this->chapter1,
        );
        $user2Course1Chapter2 = $this->createAndGetUserCourseChapter(
            userCourse: $this->createAndGetUserCourse(
                user: $user2,
                course: $this->course1,
            ),
            chapter: $this->chapter2,
        );
        $response = $this->makeAdminApiRequest(
            'GET',
            uri: AdminCourseStatisticsEndpoints::courseStatisticsSummaryEndpoint($this->course1->getId()),
            bearerToken: $this->loginAndGetToken()
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = $this->extractResponseData($response);

        $this->assertNotEmpty($responseData);
        $this->assertArrayHasKey('totalUsers', $responseData);
        $this->assertArrayHasKey('started', $responseData);
        $this->assertArrayHasKey('finished', $responseData);
        $this->assertArrayHasKey('totalTime', $responseData);
        $this->assertEquals(2, $responseData['started']);

        $this->hardDeleteUsersByIds([$user1->getId(), $user2->getId()]);
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            Chapter::class,
            UserCourse::class,
            UserCourseChapter::class,
            'user_login',
        ]);

        parent::tearDown();
    }
}
