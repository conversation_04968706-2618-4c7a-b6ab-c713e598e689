<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseStatisticsEndpoints;
use App\Tests\Functional\V2\Fixtures\CourseCreatorFixtureTrait;
use App\Tests\Functional\V2\Fixtures\CourseManagerFixtureTrait;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class GetCoursesChaptersStatsFunctionalTest extends FunctionalTestCase
{
    use CourseCreatorFixtureTrait;
    use CourseManagerFixtureTrait;

    private Course $course1;
    private Course $course2;
    private Chapter $chapter1;
    private Chapter $chapter2;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setUp(): void
    {
        parent::setUp();
        $this->course1 = $this->createAndGetCourse(
            code: 'TEST-COURSE-1',
        );
        $this->course2 = $this->createAndGetCourse(
            code: 'TEST-COURSE-2',
        );
        $this->chapter1 = $this->createAndGetChapter(
            course: $this->course1,
            id: 1
        );
        $this->chapter2 = $this->createAndGetChapter(
            course: $this->course1,
            id: 2
        );
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Exception
     */
    public function testUserNotLoggedAccessCourseChaptersStats(): void
    {
        // Role that cannot access the endpoint.
        $notAuthUser = $this->createAndGetUser(
            roles: [User::ROLE_ADMIN],
            email: '<EMAIL>'
        );

        // But not login and try to access the endpoint.
        $response = $this->makeAdminApiRequest(
            'GET',
            uri: AdminCourseStatisticsEndpoints::courseChapterStatisticsEndpoint($this->course1->getId()),
        );

        $this->hardDeleteUsersByIds([$notAuthUser->getId()]);
        $this->assertEquals(Response::HTTP_FOUND, $response->getStatusCode());
        $this->assertTrue($response->isRedirect('/login'));
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Exception
     */
    public function testUnauthorizedUserCannotAccessCourseChapterStats(): void
    {
        $notAuthUser = $this->createAndGetUser(
            roles: [User::ROLE_CREATOR],
            email: '<EMAIL>'
        );

        $token = $this->loginAndGetTokenForUser($notAuthUser);

        $response = $this->makeAdminApiRequest(
            'GET',
            uri: AdminCourseStatisticsEndpoints::courseChapterStatisticsEndpoint($this->course1->getId()),
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());

        $this->hardDeleteUsersByIds([$notAuthUser->getId()]);
    }

    /**
     * @throws Exception
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws CollectionException
     */
    #[DataProvider('userRolesProvider')]
    public function testUserRolesAccessCourseChaptersStats(array $roles, int $expectedResponseCode, ?string $assignedCourseCode = null): void
    {
        $authUser = $this->createAndGetUser(
            roles: $roles,
            email: '<EMAIL>'
        );

        if ($assignedCourseCode) {
            $course = $this->getEntityManager()->getRepository(Course::class)->findOneBy(['code' => $assignedCourseCode]);
            if ($course) {
                $this->setAndGetCourseManagerInRepository(
                    userId: new Id($authUser->getId()),
                    courseId: new Id($course->getId()),
                );
            }
        }

        $token = $this->loginAndGetTokenForUser($authUser);

        $response = $this->makeAdminApiRequest(
            'GET',
            uri: AdminCourseStatisticsEndpoints::courseChapterStatisticsEndpoint($this->course1->getId()),
            bearerToken: $token
        );

        $this->assertEquals($expectedResponseCode, $response->getStatusCode());

        $this->hardDeleteUsersByIds([$authUser->getId()]);
    }

    public static function userRolesProvider(): \Generator
    {
        yield 'Admin' => [
            'roles' => [User::ROLE_ADMIN],
            'expectedResponseCode' => Response::HTTP_OK,
        ];
        yield 'Manager with no course assigned' => [
            'roles' => [User::ROLE_MANAGER],
            'expectedResponseCode' => Response::HTTP_OK,
        ];
        yield 'Manager with valid course assigned' => [
            'roles' => [User::ROLE_MANAGER],
            'expectedResponseCode' => Response::HTTP_OK,
            'assignedCourseCode' => 'TEST-COURSE-1',
        ];
        yield 'Manager with no valid course assigned' => [
            'roles' => [User::ROLE_MANAGER],
            'expectedResponseCode' => Response::HTTP_FORBIDDEN,
            'assignedCourseCode' => 'TEST-COURSE-2',
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws Exception
     */
    public function testUserCourseChaptersStatsData(): void
    {
        $user1 = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: '<EMAIL>'
        );
        $user2 = $this->createAndGetUser(
            roles: [User::ROLE_USER],
            email: '<EMAIL>'
        );

        // It will be 4 users in total. Two users set in tests fixtures and two created in this test.
        $totalUsers = \count($this->getEntityManager()->getRepository(User::class)->findAll());
        $totalUsersFinished = 100 / $totalUsers;
        $totalUsersInProgress = 100 / $totalUsers;

        // User 1 finished chapter 1 with 60 minutes.
        $user1Course1Chapter1 = $this->createAndGetUserCourseChapter(
            userCourse: $this->createAndGetUserCourse(
                user: $user1,
                course: $this->course1,
            ),
            chapter: $this->chapter1,
            finishedAt: new \DateTimeImmutable(),
            timeSpent: 60,
        );

        // User 2 chapter1 in progress with 20 minutes.
        $user2Course1Chapter1 = $this->createAndGetUserCourseChapter(
            userCourse: $this->createAndGetUserCourse(
                user: $user2,
                course: $this->course1,
            ),
            chapter: $this->chapter1,
            timeSpent: 20,
        );
        $user2Course1Chapter2 = $this->createAndGetUserCourseChapter(
            userCourse: $this->createAndGetUserCourse(
                user: $user2,
                course: $this->course1,
            ),
            chapter: $this->chapter2,
        );
        $response = $this->makeAdminApiRequest(
            'GET',
            uri: AdminCourseStatisticsEndpoints::courseChapterStatisticsEndpoint($this->course1->getId()),
            bearerToken: $this->loginAndGetToken()
        );

        $this->hardDeleteUsersByIds([$user1->getId(), $user2->getId()]);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $responseData = $this->extractResponseData($response);
        $this->assertNotEmpty($responseData);
        $this->assertArrayHasKey('chapters', $responseData);
        $this->assertArrayHasKey('pagination', $responseData);
        $this->assertArrayHasKey('totalPages', $responseData['pagination']);
        $this->assertArrayHasKey('totalItems', $responseData['pagination']);
        $this->assertCount(2, $responseData['chapters']);

        foreach ($responseData['chapters'] as $chapter) {
            $this->assertArrayHasKey('id', $chapter);
            $this->assertArrayHasKey('name', $chapter);
            $this->assertArrayHasKey('thumbnail', $chapter);
            $this->assertArrayHasKey('type', $chapter);
            $this->assertArrayHasKey('icon', $chapter);
            $this->assertArrayHasKey('time', $chapter);
            $this->assertArrayHasKey('inProgress', $chapter);
            $this->assertArrayHasKey('finished', $chapter);
            $this->assertArrayHasKey('allUsersInChapters', $chapter);

            // Get the percentage of users that finished or are in progress in the chapter 1.
            if (1 == $chapter['id']) {
                $this->assertEquals($totalUsersFinished, $chapter['finished']);
                $this->assertEquals($totalUsersInProgress, $chapter['inProgress']);
            }
        }
        $this->assertNotEmpty($responseData);
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Course::class,
            Chapter::class,
            UserCourse::class,
            UserCourseChapter::class,
            'user_login',
        ]);

        parent::tearDown();
    }
}
