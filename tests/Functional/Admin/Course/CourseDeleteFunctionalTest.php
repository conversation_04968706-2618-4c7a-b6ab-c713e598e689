<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Course;

use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\User;
use App\Entity\UserLogin;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminCourseEndpoints;
use App\Tests\Functional\V2\Fixtures\CourseCreatorFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use PHPUnit\Framework\Attributes\DataProvider;

class CourseDeleteFunctionalTest extends FunctionalTestCase
{
    use CourseHelperTrait;
    use CourseCreatorFixtureTrait;

    private ?User $testUser = null;
    protected const string TEST_EMAIL = '<EMAIL>';

    protected function setUp(): void
    {
        parent::setUp();
        $this->testUser = $this->createAndGetUser(
            firstName: 'test',
            lastName: 'test',
            roles: [User::ROLE_SUPER_ADMIN],
            email: self::TEST_EMAIL,
        );
    }

    #[DataProvider('providerDeleteCourseAuthorization')]
    public function testCloneCourseAuthorization(
        array $roles,
        bool $isCourseCreator,
        bool $shareCourseWithUser,
        int $expectedStatusCode,
    ): void {
        $this->testUser->setRoles($roles);
        $this->getEntityManager()->flush();
        $this->getEntityManager()->refresh($this->testUser);
        $course = $this->createAndGetCourse(createdBy: $isCourseCreator ? $this->testUser : null);

        if ($shareCourseWithUser && !$isCourseCreator) {
            $this->setAndGetCourseCreatorInRepository(
                userId: new Id($this->testUser->getId()),
                courseId: new Id($course->getId()),
            );
        }

        $userToken = $this->loginAndGetToken(
            email: $this->testUser->getEmail(),
        );

        $request = $this->makeAdminApiRequest(
            method: 'DELETE',
            uri: AdminCourseEndpoints::deleteCourseEndpoint(courseId: $course->getId()),
            bearerToken: $userToken
        );

        $this->assertEquals($expectedStatusCode, $request->getStatusCode());
    }

    public static function providerDeleteCourseAuthorization(): \Generator
    {
        yield 'Super Admin can delete course' => [
            'roles' => [User::ROLE_SUPER_ADMIN],
            'isCourseCreator' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 200,
        ];

        yield 'Admin can delete course' => [
            'roles' => [User::ROLE_ADMIN],
            'isCourseCreator' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 200,
        ];

        yield 'Creator can delete course if is the creator' => [
            'roles' => [User::ROLE_CREATOR],
            'isCourseCreator' => true,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 200,
        ];

        yield 'Creator can delete course if is shared with him' => [
            'roles' => [User::ROLE_CREATOR],
            'isCourseCreator' => false,
            'shareCourseWithUser' => true,
            'expectedStatusCode' => 200,
        ];

        yield 'Manager cannot delete course' => [
            'roles' => [User::ROLE_MANAGER],
            'isCourseCreator' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 403,
        ];

        yield 'Tutor cannot delete course' => [
            'roles' => [User::ROLE_TUTOR],
            'isCourseCreator' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 302,
        ];

        yield 'Inspector cannot delete course' => [
            'roles' => [User::ROLE_INSPECTOR],
            'isCourseCreator' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 302,
        ];

        yield 'User cannot delete course' => [
            'roles' => [User::ROLE_USER],
            'isCourseCreator' => false,
            'shareCourseWithUser' => false,
            'expectedStatusCode' => 302,
        ];
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([
            UserLogin::class,
            Course::class,
            CourseCategory::class,
        ]);
        // Hard delete user created for the test
        if (!empty($this->testUser)) {
            $this->hardDeleteUsersByIds([
                $this->testUser->getId(),
            ]);
        }
        parent::tearDown();
    }
}
