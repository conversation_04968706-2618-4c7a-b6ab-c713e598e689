<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\RoleplayProject;

use App\Entity\RoleplayProject;
use App\Entity\RoleplayScene;
use App\Entity\RoleplaySequence;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\RoleplayProjectEndpointsTrait;
use App\Tests\Functional\HelperTrait\RoleplayProjectHelperTrait;
use App\Tests\Functional\HelperTrait\RoleplaySceneHelperTrait;
use App\Tests\Functional\HelperTrait\RoleplaySequenceHelperTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;

class SaveScenesRoleplayFunctionalTest extends FunctionalTestCase
{
    use RoleplayProjectEndpointsTrait;
    use RoleplayProjectHelperTrait;
    use RoleplaySequenceHelperTrait;
    use RoleplaySceneHelperTrait;

    private RoleplayProject $roleplayProject;
    private RoleplaySequence $roleplaySequence;
    private RoleplayScene $roleplayScene;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->roleplayProject = $this->createAndGetEmptyRoleplayProject();
        $this->roleplaySequence = $this->createAndGetRoleplaySequence(roleplayProject: $this->roleplayProject, color: '#ff0000', order: 1);
        $this->roleplayScene = $this->createAndGetRoleplayScene(roleplaySequence: $this->roleplaySequence, code: 'code', type: '', order: 1, answers: []);
    }

    public function testSaveSceneRoleplayTypeVideo(): void
    {
        $userToken = $this->loginAndGetToken();

        $sequences = [
            [
                'id' => $this->roleplaySequence->getId(),
                'color' => '#ff0000',
                'order' => 1,
                'scenes' => [
                    [
                        'id' => $this->roleplayScene->getId(),
                        'order' => 1,
                        'answers' => [
                            [
                                'id' => 'test-id-1',
                                'text' => 'Answer 1',
                                'feedback' => null,
                                'points' => 0,
                                'linkedScene' => 's1',
                                'order' => 1,
                            ],
                            [
                                'id' => 'test-id-2',
                                'text' => 'Answer 2',
                                'feedback' => null,
                                'points' => 0,
                                'linkedScene' => 's2',
                                'order' => 2,
                            ],
                        ],
                        'attached' => 'http://example.com/video.mp4',
                        'attachedTag' => 'video/mp4',
                        'avatar' => null,
                        'background' => null,
                        'standby' => 'test standby',
                        'statement' => null,
                        'type' => 'video',
                        'video' => '',
                    ],
                ],
            ],
        ];

        $response = $this->makeRequest(
            method: 'POST',
            uri: $this->saveScenesRoleplayEndpoint($this->roleplayProject->getId()),
            body: ['project' => ['id' => $this->roleplayProject->getId(), 'sequences' => $sequences]],
            bearerToken: $userToken,
        );

        $this->assertEquals(200, $response->getStatusCode());
        $scenes = $this->getEntityManager()
            ->getRepository(RoleplayScene::class)
            ->findBy(['sequence' => $this->roleplaySequence]);
        $this->assertEquals(null, $scenes[0]->getAvatar());
        $this->assertEquals(null, $scenes[0]->getBackground());
        $this->assertEquals(null, $scenes[0]->getStatement());
    }

    public function testSaveSceneRoleplayTypeImage(): void
    {
        $userToken = $this->loginAndGetToken();

        $sequences = [
            [
                'id' => $this->roleplaySequence->getId(),
                'color' => '#ff0000',
                'order' => 1,
                'scenes' => [
                    [
                        'id' => $this->roleplayScene->getId(),
                        'order' => 1,
                        'answers' => [
                            [
                                'id' => 'test-id-1',
                                'text' => 'Answer 1',
                                'feedback' => null,
                                'points' => 0,
                                'linkedScene' => 's1',
                                'order' => 1,
                            ],
                            [
                                'id' => 'test-id-2',
                                'text' => 'Answer 2',
                                'feedback' => null,
                                'points' => 0,
                                'linkedScene' => 's2',
                                'order' => 2,
                            ],
                        ],
                        'attached' => 'http://example.com/image.png',
                        'attachedTag' => 'image/png',
                        'avatar' => 'http://example.com/avatar.png',
                        'background' => 'http://example.com/background.png',
                        'standby' => 'test standby',
                        'statement' => 'test statement',
                        'type' => 'image',
                        'video' => '',
                    ],
                ],
            ],
        ];

        $response = $this->makeRequest(
            method: 'POST',
            uri: $this->saveScenesRoleplayEndpoint($this->roleplayProject->getId()),
            body: ['project' => ['id' => $this->roleplayProject->getId(), 'sequences' => $sequences]],
            bearerToken: $userToken,
        );

        $this->assertEquals(200, $response->getStatusCode());
        $scenes = $this->getEntityManager()
            ->getRepository(RoleplayScene::class)
            ->findBy(['sequence' => $this->roleplaySequence]);
        $this->assertEquals($sequences[0]['scenes'][0]['avatar'], $scenes[0]->getAvatar());
        $this->assertEquals($sequences[0]['scenes'][0]['background'], $scenes[0]->getBackground());
        $this->assertEquals($sequences[0]['scenes'][0]['statement'], $scenes[0]->getStatement());
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            RoleplayProject::class,
            RoleplaySequence::class,
            RoleplayScene::class,
        ]);

        parent::tearDown();
    }
}
