<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\RoleplayProject;

use App\Entity\RoleplayProject;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\RoleplayProjectEndpointsTrait;
use App\Tests\Functional\HelperTrait\RoleplayProjectHelperTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class CreateResourceFunctionalTest extends FunctionalTestCase
{
    use RoleplayProjectEndpointsTrait;
    use RoleplayProjectHelperTrait;

    private RoleplayProject $roleplayProject;
    private array $tempFiles = [];

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->roleplayProject = $this->createAndGetEmptyRoleplayProject();
    }

    public function testUploadResourceRoleplay(): void
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'resource_test_');
        $this->tempFiles[] = $tempFile;
        file_put_contents($tempFile, 'test image content');

        // Create a mock image file
        $file = new UploadedFile(
            $tempFile,
            'test-image.png',
            'image/png',
            null,
            true
        );

        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'POST',
            uri: $this->saveResourceEndpoint($this->roleplayProject->getId()),
            files: ['file' => $file],
            bearerToken: $userToken,
        );

        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertFalse($responseData['error']);
        $this->assertNotNull($responseData['data']['url']);
    }

    /**
     * Test unsupported file type upload.
     */
    public function testUploadUnsupportedFileTypeRoleplay(): void
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'unsupported_test_');
        $this->tempFiles[] = $tempFile;
        file_put_contents($tempFile, 'test document content');

        $file = new UploadedFile(
            $tempFile,
            'test-document.txt',
            'text/plain',
            null,
            true
        );

        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'POST',
            uri: $this->saveResourceEndpoint($this->roleplayProject->getId()),
            files: ['file' => $file],
            bearerToken: $userToken,
        );

        $this->assertEquals(400, $response->getStatusCode());
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            RoleplayProject::class,
        ]);

        foreach ($this->tempFiles as $tempFile) {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }

        parent::tearDown();
    }
}
