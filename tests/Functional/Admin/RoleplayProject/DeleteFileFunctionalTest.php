<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\RoleplayProject;

use App\Controller\Roleplay\MediaController;
use App\Entity\RoleplayProject;
use App\Service\SettingsService;
use App\Service\Vimeo\VimeoService;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\RoleplayProjectEndpointsTrait;
use App\Tests\Functional\HelperTrait\RoleplayProjectHelperTrait;
use App\Tests\Functional\SettingsConstants;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

class DeleteFileFunctionalTest extends FunctionalTestCase
{
    use RoleplayProjectEndpointsTrait;
    use RoleplayProjectHelperTrait;

    private array $tempFiles = [];
    private RoleplayProject $roleplayProject;

    protected function setUp(): void
    {
        parent::setUp();
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $requestStack = $this->createMock(RequestStack::class);
        $this->roleplayProject = $this->createAndGetEmptyRoleplayProject();
        $this->settingsService = $this->createMock(SettingsService::class);
        $this->vimeoService = $this->createMock(VimeoService::class);

        $this->controller = new MediaController(
            $entityManager,
            $requestStack,
            $this->vimeoService,
            $this->settingsService,
        );
    }

    public function testDeleteLocalFileSuccessRoleplayProject(): void
    {
        $userToken = $this->loginAndGetToken();
        $uploadDir = SettingsConstants::TEST_ROUTE_ROLEPLAY_FILE;
        $fileName = 'file_test_' . uniqid() . '.mp4';
        $tempFile = $uploadDir . '/' . $fileName;
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        file_put_contents($tempFile, 'test content');
        $this->tempFiles[] = $tempFile;
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: $this->deleteFileResourceEndpoint(
                projectId: $this->roleplayProject->getId(),
            ),
            body: [
                'resourcePath' => 'http://example/' . $tempFile,
            ],
            bearerToken: $userToken,
        );
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function testDeleteLocalFileFailedRoleplayProject(): void
    {
        $userToken = $this->loginAndGetToken();
        $uploadDir = SettingsConstants::TEST_ROUTE_ROLEPLAY_FILE;
        $fileName = 'file_test_' . uniqid() . '.mp4';
        $tempFile = $uploadDir . '/' . $fileName;
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        file_put_contents($tempFile, 'test content');
        $this->tempFiles[] = $tempFile;
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: $this->deleteFileResourceEndpoint(
                projectId: $this->roleplayProject->getId(),
            ),
            body: [
                'resourcePath' => '//example/' . $tempFile,
            ],
            bearerToken: $userToken,
        );
        $this->assertEquals(400, $response->getStatusCode());
    }

    public function testRemoveVimeoFileSuccess(): void
    {
        $url = 'https://vimeo.com/' . SettingsConstants::VIMEO_ID;
        $request = new Request(content: json_encode(['resourcePath' => $url]));
        $this->vimeoService
            ->expects($this->once())
            ->method('deleteFromVimeo')
            ->with(SettingsConstants::VIMEO_ID, $this->anything());

        $response = $this->controller->removeFile($request);
        $data = json_decode($response->getContent(), true);

        $this->assertEquals(200, $data['status']);
        $this->assertFalse($data['error']);
    }

    public function testExceptionDuringDeleteVimeoFile(): void
    {
        $url = 'https://vimeo.com/' . SettingsConstants::VIMEO_ID;
        $request = new Request(content: json_encode(['resourcePath' => $url]));
        $this->vimeoService
            ->expects($this->once())
            ->method('deleteFromVimeo')
            ->willThrowException(new \Exception('Vimeo API error'));

        $response = $this->controller->removeFile($request);
        $data = json_decode($response->getContent(), true);

        $this->assertEquals(400, $data['status']);
        $this->assertTrue($data['error']);
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([
            RoleplayProject::class,
        ]);

        // Cleanup temporary files
        foreach ($this->tempFiles as $tempFile) {
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }

        parent::tearDown();
    }
}
