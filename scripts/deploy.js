require('dotenv-flow').config();
const inquirer = require('inquirer');
const {spawnSync} = require('child_process');
const {NodeSSH} = require('node-ssh');
const {runNodeCommand, getLatestTag, getNamedArgs} = require('./utils');
const url = require('url');


let sites = {};

try {
    sites = require('./sites.json');
} catch {
    console.log('❌ sites.json not found');
    process.exit();
}

const config = require('./config.json');
const ssh = new NodeSSH();
const now = new Date();
const timestamp = `${now.getFullYear()}-${('0' + (now.getMonth() + 1)).slice(-2)}-${('0' + now.getDate()).slice(-2)}_${('0' + now.getHours()).slice(-2)}-${('0' + now.getMinutes()).slice(-2)}-${('0' + now.getSeconds()).slice(-2)}`;

const composerInstall = async() => {
    process.stdout.write('  composer install...\n');
    // await runNodeCommand('rm composer.lock');
    try {
        await runNodeCommand('make composer-install');
        await customVendors();
        process.stdout.write('\r✅  composer installed\n');
    } catch (error) {
        console.error('❌ Error en composer install');
        throw error; // Re-lanzar el error para que sea manejado por executeAction
    }
};

const customVendors = async() => {
    try {
        process.stdout.write('  updating akitpokhrel...\n');
        await runNodeCommand('rm -rf vendor/ankitpokhrel');

        await runNodeCommand('unzip -oq _tus-phpgood/ankitpokhrel -d vendor');

        process.stdout.write('\r✅  akitpokhrel updated\n');
    } catch (error) {
        console.log('\n❌ Error en customVendors');
        console.log('---------------------------------');
        console.log(error);
        throw error; // Re-lanzar el error para que sea manejado por executeAction
    }
};

const yarnInstall = async() => {
    process.stdout.write(' installing yarn dependencies...');
    await runNodeCommand('yarn install');
    process.stdout.write('\r✅  yarn dependencies installed\n');
};

const validateClientAssetsFolder = () => {
    const fs = require('fs');
    const path = require('path');
    const clientAssetsPath = path.join('assets', 'clients', selectedClient);

    if (!fs.existsSync(clientAssetsPath)) {
        console.error(`\n❌ Error: No se encontró la carpeta del cliente en 'assets/clients/${selectedClient}'`);
        console.error(`   Verifica que el nombre del cliente en sites.json coincida con la carpeta en 'assets/clients/' `);
        console.error(`   Cliente seleccionado: "${selectedClient}"`);
        console.error(`   Carpetas disponibles en 'assets/clients':`);

        try {
            const clientFolders = fs.readdirSync('assets/clients', { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => dirent.name)
                .sort();

            clientFolders.forEach(folder => {
                console.error(`     - ${folder}`);
            });
        } catch (error) {
            console.error(`     Error al leer assets / clients: ${error.message}`);
        }

        throw new Error(`Carpeta del cliente no encontrada: 'assets/clients/${selectedClient}'`);
    }
};

const yarnBuild = async() => {
    validateClientAssetsFolder();
    process.stdout.write('building javascript bundle');
    await runNodeCommand(`yarn build --env client="${selectedClient}"`);
    process.stdout.write('\r✅  javascript bundle built\n');
};

const enableMaintenanceMode = async() => {
    await ssh.execCommand('touch public/maintenance.enable');
    process.stdout.write('\r✅  maintenance mode enabled\n');
};

const disableMaintenanceMode = async() => {
    await ssh.execCommand('rm -f public/maintenance.enable');
    process.stdout.write('\r✅  maintenance mode disabled\n');
};

const enableDevMode = async() => {
    await ssh.execCommand('sed -i \'/^APP_ENV=/d\' .env.local');
    await ssh.execCommand('echo APP_ENV=dev >> .env.local');
    process.stdout.write('\r✅  dev mode enabled\n');
};

const enableProdMode = async() => {
    await ssh.execCommand('sed -i \'/^APP_ENV=/d\' .env.local');
    await ssh.execCommand('echo APP_ENV=prod >> .env.local');
    process.stdout.write('\r✅  prod mode enabled\n');
};

const backupFiles = async() => {
    const folders = config.syncFolders.join(' ');
    const backupFilename = `files_${timestamp}.tar.gz`;
    await ssh.execCommand(`mkdir -p backups`);
    await ssh.execCommand(`tar -czf backups/${backupFilename} ${folders}`);
    process.stdout.write('\r✅  backup finished\n');

};

const removeDirectories = async() => {
    const folders = config.removeFolders;
    for (const folder of folders) {
        await ssh.execCommand(`rm - rf ${folder}/*`);
    }
};

const createEmptyFolders = async () => {
    const folders = config.createFolders;
    for (const folder of folders) {
        await ssh.execCommand(`mkdir -p ${folder}`);
    }
};

const setFilePermissions = async () => {
    for (const [mode, files] of Object.entries(config.permissions)) {
        for (const file of files) {
            await ssh.execCommand(`chmod -fR ${mode} ${file}`);
        }
    }
    console.log('✅  permissions assigned');
};

const disableWebProfiler = async () => {
    await ssh.execCommand(`sed -i 's/profiler: { only_exceptions: false }/profiler: { only_exceptions: true }/' config/packages/dev/web_profiler.yaml`);
    process.stdout.write('\r✅  web profiler disabled\n');
};

const enableHttpsMode = async () => {
    await ssh.execCommand(`sed -i 's#//[[:space:]]*$_SERVER#$_SERVER#' public/index.php `);
    // await ssh.execCommand(`sed -i 's#//\$_SERVER\['\''SERVER_PORT'\''\] = 443#\$_SERVER\['\''SERVER_PORT'\''\] = 443#' public/index.php `);
    process.stdout.write('\r✅  https mode enabled\n');
};

const syncFiles = async () => {
    await removeDirectories();
    const folders = config.syncFolders;

    const {host, port, username, password} = sites[selectedClient][selectedEnv]['sftp'];

    for (const folder of folders) {
        process.stdout.write(`  syncing ${folder}...`);
        await runNodeCommand(`rsync -az --delete -e "sshpass -p '${password}' ssh -p "${port}"" "${folder}" "${username}@${host}:${folder}"`);
        process.stdout.write(`\r✅  «${folder}» synced\n`);
    }

    process.stdout.write('\r✅  files synced\n');

    await updateVersion();
    await createEmptyFolders();
    await setFilePermissions();
    await disableWebProfiler();
    await enableHttpsMode();
};

const clearCache = async () => {
    const random = Math.floor(Math.random() * 1000000);
    const cacheFilename = `cache-remove-${random}.php`;

    const {host, port, username, password} = sites[selectedClient][selectedEnv]['sftp'];
    const clientUrl = sites[selectedClient][selectedEnv]['url'];

    await runNodeCommand(`rsync -az -e "sshpass -p '${password}' ssh -p "${port}"" "cache-remove.php" "${username}@${host}:public/${cacheFilename}"`);
    await runNodeCommand(`wget -qO- "${clientUrl}/${cacheFilename}"`);
    await ssh.execCommand(`rm public/${cacheFilename}`);
    process.stdout.write('\r✅  cache cleared\n');
};

const getDatabaseBackupFilename = (database) => {
    return `${database}_${timestamp}.sql`;
};

const databaseBackup = async () => {
    const {host, database, username, password} = sites[selectedClient][selectedEnv]['db'];
    const databaseFilename = getDatabaseBackupFilename(database);
    console.log(databaseFilename);
    await ssh.execCommand(`mkdir -p backups`);
    await ssh.execCommand(`mysqldump -h ${host} -u ${username} -p${password} ${database} > backups/${databaseFilename}`);
    await runNodeCommand('mkdir -p ./scripts/db');
    await ssh.getFile(`./scripts/db/${databaseFilename}`, `backups/${databaseFilename}`);
    process.stdout.write('\r✅ database backup finished\n');
};

const databaseMigrate = async () => {
    await ssh.execCommand(`/usr/bin/php8.3 bin/console doctrine:migrations:migrate --allow-no-migration --no-interaction --quiet`);
    process.stdout.write('\r✅ database migrated\n');
};

const cataloguesUpdate = async () => {
    await ssh.execCommand(`/usr/bin/php8.3 bin/console catalogs:update --php-binary=php8.3`);
    process.stdout.write('\r✅ catalogues updated\n');
};

const databaseMigrationsInit = async () => {
    await ssh.execCommand(`/usr/bin/php8.3 bin/console doctrine:migrations:sync-metadata-storage`);
    process.stdout.write('\r✅ database migrations init\n');
};

const databaseMigrationsAddAll = async () => {
    await ssh.execCommand(`/usr/bin/php8.3 bin/console doctrine:migrations:version --add --all --no-interaction`);
    process.stdout.write('\r✅ database migrations added\n');
};

const checkIfMaintenanceModeIsEnabled = async () => {
    const {stdout} = await ssh.execCommand(`ls public/maintenance.enable`);
    return stdout;
};

const createEnvLocalFile = async () => {
    const {stdout} = await ssh.execCommand(`ls .env.local`);

    if (!stdout) {
        //const {confirm} = await confirmAction(`No existe «.env.local». ¿Quieres crearlo?`);

        await ssh.execCommand(`touch .env.local`);
        await ssh.execCommand(`echo 'APP_ENV=dev' >> .env.local`);
        const {host, database, username, password} = sites[selectedClient][selectedEnv]['db'];
        const connectionString = `DATABASE_URL=mysql://${username}:${password}@${host}:3306/${database}`;
        await ssh.execCommand(`echo '${connectionString}' >> .env.local`);
        process.stdout.write('\r✅  .env.local file created\n');
    }
};

const updateVersion = async () => {
    let version = getLatestTag();

    const today = new Date();
    version = `${version}-${today.getFullYear()}${(today.getMonth() + 1).toString().padStart(2, '0')}${today.getDate().toString().padStart(2, '0')}`;
    await ssh.execCommand('sed -i \'/^EASYLEARNING_VERSION=/d\' .env.local');
    await ssh.execCommand("sed -i -e '$a\\' .env.local");
    await ssh.execCommand(`echo EASYLEARNING_VERSION=${version} >> .env.local`);
};

let selectedClient = null;
let selectedEnv = null;

const actions = {
    'composer': composerInstall,
    'yarn-install': yarnInstall,
    'yarn-build': yarnBuild,
    'enable-maintenance-mode': enableMaintenanceMode,
    'enable-dev-mode': enableDevMode,
    'backup-files': backupFiles,
    'sync-files': syncFiles,
    'enable-prod-mode': enableProdMode,
    'clear-cache': clearCache,
    'database-backup': databaseBackup,
    'database-migrations-init': databaseMigrationsInit,
    'database-migrations-add-all': databaseMigrationsAddAll,
    'database-migrate': databaseMigrate,
    'catalogues-update': cataloguesUpdate,
    'disable-maintenance-mode': disableMaintenanceMode,
    'env-file': createEnvLocalFile,
    'enable-https-mode': enableHttpsMode,
};

const interactiveActions = [
    'composer',
    'yarn-install',
    'yarn-build',
    'enable-maintenance-mode',
    'enable-dev-mode',
    'backup-files',
    'sync-files',
    'enable-prod-mode',
    'clear-cache',
    'database-backup',
    'database-migrations-init',
    'database-migrations-add-all',
    'database-migrate',
    'catalogues-update',
]

const sshActions = [
    'enable-maintenance-mode',
    'enable-dev-mode',
    'backup-files',
    'sync-files',
    'enable-prod-mode',
    'clear-cache',
    'database-backup',
    'database-migrations-init',
    'database-migrations-add-all',
    'database-migrate',
    'catalogues-update',
    'database-diff',
    'disable-maintenance-mode',
    'env-file',
    'enable-https-mode'
];


const askForClient = () => {
    const clients = Object.keys(sites);

    return inquirer.prompt([
        {
            type: 'list',
            name: 'client',
            choices: clients,
            message: 'Selecciona el cliente:',
            source: (answersSoFar, input) => {
                if (!input) return clients;

                return clients.filter((client) => client.includes(input));
            },
        },
    ]);

};

const askForEnvironment = (client) => {
    const environments = Object.keys(sites[client]);

    return inquirer.prompt([
        {
            type: 'list',
            name: 'env',
            message: 'Selecciona el entorno:',
            choices: environments,
            source: (answersSoFar, input) => {
                if (!input) return environments;

                return environments.filter((e) => e.includes(input));
            },
        },
    ]);
};

const askForActions = () => {
    return inquirer.prompt([
        {
            type: 'checkbox',
            pageSize: 20,
            name: 'selectedActions',
            message: 'Selecciona las acciones:',
            choices: interactiveActions,
        },
    ]);
};

const confirmAction = (message) => {
    return inquirer.prompt([
        {
            type: 'confirm',
            name: 'confirm',
            message,
        },
    ]);
};

const close = () => {
    ssh.dispose();
};

const checkNodeVersion = async () => {
    try {
        const { stdout } = await spawnSync('node --version', { shell: true });
        const version = stdout.toString().trim().replace('v', '');
        const majorVersion = parseInt(version.split('.')[0]);
        const requiredVersion = config.requirements?.nodeVersion || 20;

        if (majorVersion < requiredVersion) {
            console.error(`❌ Se requiere Node.js versión ${requiredVersion} o superior. Versión actual: ${version}`);
            return false;
        }
        console.log(`✅ Node.js versión ${version} (cumple con el requisito mínimo de v${requiredVersion})`);
        return true;
    } catch (error) {
        console.error('❌ Error al verificar la versión de Node.js:', error);
        return false;
    }
};

const preDeploymentChecks = async () => {
    // Verificar versión de Node.js
    const nodeVersionValid = await checkNodeVersion();
    if (!nodeVersionValid) {
        const { confirm } = await confirmAction('La versión de Node.js no cumple con los requisitos. ¿Deseas continuar de todos modos?');
        if (!confirm) {
            console.log('❌ Despliegue abortado debido a versión incompatible de Node.js');
            process.exit(1);
        }
    }

    try {
        await runNodeCommand('composer --version');
        await runNodeCommand('yarn --version');
        await runNodeCommand('rsync --version');
        await runNodeCommand('sshpass -V');
    } catch (error) {
        return handleError(error, 'verificación de herramientas');
    }

    try {
        const {stdout: status} = await spawnSync('git status --porcelain', {shell: true});
        if (status.toString().trim() !== '') {
            const {confirm} = await confirmAction('Hay cambios sin confirmar en el repositorio. ¿Deseas continuar?');
            if (!confirm) {
                process.exit(1);
            }
        }
    } catch (error) {
        return handleError(error, 'verificación de repositorio');
    }

    return true;
};

const executeAction = async (actionName) => {
    try {
        console.log(`Ejecutando acción: ${actionName}`);
        await actions[actionName]();
        console.log(`✅ Acción ${actionName} completada con éxito`);
    } catch (error) {
        console.error(`❌ Error en acción ${actionName}:`);
        console.error(error);

        const {retry} = await inquirer.prompt([{
            type: 'confirm',
            name: 'retry',
            message: `¿Desea reintentar la acción ${actionName}?`
        }]);

        if (retry) {
            return executeAction(actionName);
        }

        const {continue: shouldContinue} = await inquirer.prompt([{
            type: 'confirm',
            name: 'continue',
            message: '¿Continuar con el resto del despliegue?'
        }]);

        if (!shouldContinue) {
            throw new Error('Despliegue cancelado por el usuario');
        }
    }
}

const handleError = async (error, action, rollbackFunction = null) => {
    console.error(`❌ Error en ${action}:`);
    console.error('---------------------------------');
    console.error(error);

    // Preguntar si se desea continuar o abortar
    const {confirm} = await confirmAction(`Error en ${action}. ¿Deseas continuar con el despliegue?`);

    if (!confirm) {
        console.log('❌ Despliegue abortado');
        // Ejecutar función de rollback si existe
        if (rollbackFunction) {
            await rollbackFunction();
        }
        close();
        process.exit(1);
    }

    return confirm;
};

const interactiveDeploy = async () => {
    const {client} = await askForClient();
    selectedClient = client;

    const {env} = await askForEnvironment(client);
    selectedEnv = env;
    const {host, port, username, password} = sites[selectedClient][selectedEnv]['sftp'];

    let connected = false;
    await ssh.connect({host, port, username, password})
        .then(() => {
            console.log('SSH Connected');
            connected = true;
        })
        .catch((error) => {
            console.log('SSH Connection error');
            console.log('---------------------------------');
            console.log(error);
        });

    if (!connected) return;

    console.log(`Selected «${client}» to «${env}»...`);

    await createEnvLocalFile();

    const {selectedActions} = await askForActions();

    console.log('Actions selected: ', selectedActions);

    const confirmDeployment = await confirmAction(`¿Quieres hacer la deploy de «${selectedClient}» para el entorno «${selectedEnv}»?`);

    if (!confirmDeployment.confirm) {
        console.log('❌ canceled deployment');
        close();
        return;
    }

    for (const action of selectedActions) {
        await executeAction(action);
    }

    const maintenanceModeEnabled = await checkIfMaintenanceModeIsEnabled();
    if (maintenanceModeEnabled) {
        const confirmMaintenanceMode = await confirmAction(`¿Quieres desactivar el modo mantenimiento?`);

        if (confirmMaintenanceMode.confirm) {
            await disableMaintenanceMode();
        }
    }

    close();
    console.log('Deployment finished');
};

const manualDeploy = async (args) => {
    const requiredManualArgs = ['client', 'env', 'action'];
    const missingArgs = requiredManualArgs.filter((arg) => !args[arg]);

    if(missingArgs.length){
        console.log('❌ missing args: ' + missingArgs.join(', '));
        return;
    }

    selectedClient = args.client;
    selectedEnv = args.env;
    const selectedAction = args.action;
    const allowedActions = {
        ...actions,
        'env-file': createEnvLocalFile,
        'disable-maintenance-mode': disableMaintenanceMode,
    };

    if(!sites[selectedClient] || !sites[selectedClient][selectedEnv]){
        console.log('❌ client or env not found');
        return;
    }

    if(!allowedActions[selectedAction]){
        console.log('❌ action not found');
        return;
    }

    if(sshActions.includes(selectedAction)){
        const {host, port, username, password} = sites[selectedClient][selectedEnv]['sftp'];
        let connected = false;

        await ssh.connect({host, port, username, password})
            .then(() => {
                console.log('SSH Connected');
                connected = true;
            })
            .catch((error) => {
                console.log('SSH Connection error');
                console.log('---------------------------------');
                console.log(error);
            });
        if (!connected) return;
    }

    console.log(`${selectedAction}: selected «${selectedClient}» to «${selectedEnv}»...`);
    await allowedActions[selectedAction]();

    close();
}

const deploy = async () => {
    const args = getNamedArgs();
    await preDeploymentChecks();
    if(Object.keys(args).length) {
        manualDeploy(args).then(() => {});
    }
    else{
        interactiveDeploy().then(() => {});
    }
}

deploy().then(() => {});


// TODO ask if deploy database diff
// TODO create profiles (groups of actions)
