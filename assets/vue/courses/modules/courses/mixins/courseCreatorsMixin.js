import { get } from 'vuex-pathify';
import SelectionModalItem from '../../../../models/selectionModalItem.model'

export const courseCreatorsMixin = {
  data() {
    return {
      isRemoving: false,
      creatorUserList: {
        users: [],
        total: 0
      }
    }
  },
    computed: {
      courseCreators: get('coursesModule/getCourseCreators'),
        ...get("coursesModule", {
            courseInfo: "getCourseInfo",
        }),

        canManageCourseContent() {
            if (this.$auth.isAdmin()) {
                return true
            }

            if (!this.$auth.isCreator()) {
                return false
            }

            const user = this.$auth.getUser()
            if (this.courseInfo?.createdBy === user.email) {
                return true
            }

            return this.courseCreators?.some(creator => creator.id === user.id);
        },

        canManageCreators() {
            if (this.$auth.isAdmin()) {
                return true;
            }

            if (this.$auth.isCreator()) {
                const user = this.$auth.getUser();
                return this.courseInfo?.createdBy === user.email;
            }

            return false;
        }
  },

  async mounted() {
    await this.loadCourseCreators();
  },

  methods: {
    async loadAvailableUsers(searchText = '') {
      const { data } = await this.$store.dispatch('coursesModule/fetchCreators', searchText)
      this.creatorUserList.users = (data?.users || []).map((user) => new SelectionModalItem({ id: user.id, name: `${user.first_name} ${user.last_name}`.trim(), description: user.email }))
      this.creatorUserList.total = data?.total || 0
    },
    async loadCourseCreators() {
      await this.$store.dispatch("coursesModule/fetchCourseCreators", this.courseId)
    },
    async addCourseCreator(user = new SelectionModalItem()) {
      if (user.isLoading) {
        return null
      }
      user.isLoading = true
      const success = await this.$store.dispatch("coursesModule/addCreator", { courseId: this.courseId, userId: user.id })
      if (success) {
        user.isSelected = true
        this.$toast.success(this.$t('COURSE.SHARE.SUCCESS'));
        await this.loadCourseCreators();
      } else {
        this.$toast.error(this.$t('COURSE.SHARE.ERROR'));
      }
      user.isLoading = false
    },
    async deleteCurseCreator(userId = 0) {
      if (this.isRemoving) {
        return null
      }
      this.isRemoving = true
      const success = await this.$store.dispatch("coursesModule/deleteCreator", { courseId: this.courseId, userId: userId})
      if (success) {
        this.$toast.success(this.$t('COURSE.SHARE.REMOVE_SUCCESS'));
        await this.loadCourseCreators();
      } else {
        this.$toast.error(this.$t('COURSE.SHARE.REMOVE_ERROR'));
      }
      this.isRemoving = false
    }
  },
};
