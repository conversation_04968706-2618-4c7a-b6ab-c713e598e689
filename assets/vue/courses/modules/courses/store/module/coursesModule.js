import { make } from "vuex-pathify";
import axios from "axios";
import { Courses } from "../models/courses";
import { CourseInfo } from "../models/course";
import { CourseChapters } from "../models/chapters";
import { Season } from "../models/seasons";
import { Announcement } from "../models/announcement";
import { Translation } from "../models/translation";

const getDefaultState = () => ({
  loading: true,
  courses: [],
  languages: [],
  totalCourses: 0,
  typeCourses: [],
  courseCategories: [],
  creators: [],
  courseInfo: {},
  courseInfoTranslates: [],
  chapters: [],
  addChapterUrl: '',
  seasons: [],
  announcements: [],
  courseCreators: [],
  courseManagers: []
});

const state = () => getDefaultState();

export const getters = {
  getCourses: (state) => state.courses,
  getCourseInfo: (state) => state.courseInfo,
  getCourseInfoTranslates: (state) => state.courseInfoTranslates,
  getChapters: (state) => state.chapters,
  getTotalCourses: (state) => state.totalCourses,
  getTypeCourses: (state) => state.typeCourses,
  getLanguages: (state) => state.languages,
  getCreators: (state) => state.creators,
  getCourseCategories: (state) => state.courseCategories,
  getIsEveryChapterContentCompleted: (state) =>
    state.chapters.every((chapter) => chapter.hasContentCompleted),
  getAddChapterUrl: (state) => state.addChapterUrl,
  getSeasons: (state) => state.seasons,
  getAnnouncements: (state) => state.announcements,
  isLoading: (state) => state.loading,
  getCourseCreators: (state) => state.courseCreators,
  getCourseManagers: (state) => state.courseManagers,
};

export const mutations = {
  ...make.mutations(state),
};

export const actions = {
  ...make.actions(state),

  async fetchCourses({ commit }, endpoint) {
    try {
      commit("SET_LOADING", true);
      const { data, error } = await axios.get(endpoint);
      if (error) {
        throw new Error(data?.message);
      }
      const parsedResponse = new Courses(data.data);
      commit("SET_COURSES", parsedResponse.courses);
      commit("SET_TOTAL_COURSES", parsedResponse.totalCourses);
      commit("SET_TYPE_COURSES", parsedResponse.typeCourses);
      commit("SET_COURSE_CATEGORIES", parsedResponse.courseCategories);
      commit("SET_CREATORS", parsedResponse.creatorsCourses);
    } catch (error) {
      console.log(error);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async updatePublishCourse({}, id) {
    try {
      const result = await axios.put(`/admin/api/v1/courses/${id}/active`);
      return result.data;
    } catch (e) {
      return Promise.reject("Failed to make request");
    }
  },
  async cloneCourse({}, id) {
    try {
      const result = await axios.post(`/admin/api/v1/courses/${id}/clone`);
      const { error, data } = result.data;
      if (error) return Promise.reject(data);
      return Promise.resolve();
    } catch (e) {
      return Promise.reject("Failed to make request");
    }
  },
  async deleteCourse({}, id) {
    try {
      const result = await axios.delete(`/admin/api/v1/courses/${id}`);
      return result.data;
    } catch (e) {
      return Promise.reject("Failed to make request");
    }
  },
  async shareCourse({}, id) {
    try {
      const result = await axios.get(`/admin/api/v1/courses/${id}/share`);
      const { error, data } = result.data;
      if (error) return Promise.reject(data);
      return Promise.resolve(data);
    } catch (e) {
      return Promise.reject("Failed to make request");
    }
  },
  async fetchCourseById({ commit }, id) {
      const validUrl = (text = '') => {
          return /^(https?:\/\/)?([\w-]+(\.[\w-]+)+)(\/[\w\-._~:/?#[\]@!$&'()*+,;=]*)?$/i.test(`${text}`);
      }
    try {
      const result = await axios.get(`/admin/api/v1/courses/${id}`);
      const { error, data } = result.data;
      if (error) return Promise.reject(data);
      const courseInfo = new CourseInfo({
        ...data,
        image: validUrl(data.image) ? `${data.image}` : `${window.location.origin}/${data.image}`
      });

      commit("SET_COURSE_INFO", courseInfo);

      window.course_data = {
        name: courseInfo.name,
        image: validUrl(courseInfo.image) ? courseInfo.image : `${window.location.origin}${courseInfo.image}`,
        category: courseInfo.category,
        type: courseInfo.typeCourse,
      };
      return Promise.resolve();
    } catch (e) {
      return Promise.reject("Failed to make request");
    }
  },
  async fetchCourseTranslateById({ commit }, id) {
    try {
      const result = await axios.get(`/admin/api/v1/courses/${id}/translates`);
      const { error, data } = result.data;
      if (error) return Promise.reject(data);
      const parsedData = Translation.fromArray(data);
      commit("SET_COURSE_INFO_TRANSLATES", parsedData);
      return Promise.resolve();
    } catch (e) {
      console.log(e)
      return Promise.reject("Failed to make request");
    }
  },
  async fetchChapters({ commit }, elements) {
    try {
      const result = await axios.post(elements.endpoint, {'url' : elements.currentUrl});
      const { data, error } = result.data;
      if (error) {
        throw new Error(data?.message);
      }
      const dataParsed = new CourseChapters(data);
      commit("SET_CHAPTERS", dataParsed.chapters);
      commit("SET_ADD_CHAPTER_URL", dataParsed.addChapterUrl);
    } catch (error) {
      console.log(error);
    }
  },
  async fetchSeasons({ commit }, id) {
    try {
      const result = await axios.get(`/admin/api/v1/courses/${id}/seasons`);
      const { data, error } = result.data;
      if (error) {
        throw new Error(data?.message);
      }
      const parsedData = Season.fromArray(data)
      commit("SET_SEASONS", parsedData);
    } catch (error) {
      console.log(error);
    }
  },
  async fetchAnnouncements({ commit }, id) {
    try {
      const result = await axios.get(
        `/admin/api/v1/courses/${id}/announcements`
      );
      const { data, error } = result.data;
      if (error) {
        throw new Error(data?.message);
      }
      const parsedData = Announcement.fromArray(data);
      commit("SET_ANNOUNCEMENTS", parsedData);
    } catch (error) {
      console.log(error);
    }
  },

  async updateSeason({ commit }, { id, form }) {
    try {
      const { error } = await axios.put(
        `/admin/api/v1/courses/seasons/${id}`, form);
      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.error("Error updating season:", error.response?.data || error);
      throw error;
    }
  },

  async createSeason({ commit }, { id, form }) {
    try {
      const { error } = await axios.post(
        `/admin/api/v1/courses/${id}/seasons`,
        form,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.error("Error creating season:", error.response?.data || error);
      throw error;
    }
  },

  async deleteSeason({ commit }, id) {
    let seasonResponse;
    try {
      const { data, error } = await axios.delete(
        `/admin/api/v1/courses/seasons/${id}`
      );
      if (error) {
        throw new Error(data?.message);
      }
      seasonResponse = data;
    } catch (error) {
      console.error("Error deleting season:", error.response?.data || error);
      throw error;
    }
    return seasonResponse;
  },
  async sortChapter({ commit, getters, state }, [init, finish, courseId]) {
    const chapters = getters.getChapters;
    const chapterSelected = chapters.splice(init, 1)[0];
    chapters.splice(finish, 0, chapterSelected)
    const newChapterList = []
    chapters.forEach((chapter, index) => {
      if (chapter.seasonId === chapterSelected.seasonId) {
        chapter.order = index + 1
        newChapterList.push(chapter.id)
      }
    })
    try {
      const { error, data } = await axios.put(`/admin/api/v1/courses/${courseId}/chapters/order`, { order: newChapterList, season: chapterSelected.seasonId });
      if (!error && !data.error) {
        commit("SET_CHAPTERS", chapters)
      }
      return data.data || ''
    } catch (e) { console.log('error') }
    return ''
  },
  async deleteChapter({ commit }, id) {
    try {
      const { error } = await axios.delete(
        `/admin/api/v1/courses/chapters/${id}`
      );
      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.error("Error deleting chapter:", error.response?.data || error);
      throw error;
    }
  },
  async deleteAnnouncement({ commit }, id) {
    try {
      const { error } = await axios.delete(`/admin/api/v1/announcement/${id}`);
      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.error("Error deleting chapter:", error.response?.data || error);
      throw error;
    }
  },
  async translateCourse({ commit }, { id, body }) {
    try {
      const { error } = await axios.post(
        `/admin/api/v1/courses/${id}/translate`,
        body
      );
      if (error) {
        throw new Error(data?.message);
      }
    } catch (error) {
      console.error("Error creating season:", error.response?.data || error);
      throw error;
    }
  },

  async fetchCreators({ commit }, searchString = '') {
    try {
      const URL = searchString ? `/api/v2/admin/users/creators?search=${searchString}&page_size=10` : "/api/v2/admin/users/creators?page_size=10"
      const result = await axios.get(`${URL}&page=1`);
      return result.data;
    } catch (error) {
      return { data: { users: [], total: 0 } , error: true }
    }
  },

  async fetchCourseCreators({ commit }, id) {
    try {
      const result = await axios.get(`/api/v2/admin/courses/${id}/creators`);
      const { data, error } = result.data;
      if (error) {
        throw new Error(data?.message);
      }
      commit("SET_COURSE_CREATORS", data);
    } catch (error) {
      throw error;
    }
  },

  async addCreator({}, payload){
    try{
      const { courseId, userId } = payload
      await axios.put(`/api/v2/admin/courses/${courseId}/creators/${userId}`);
      return true;
    }
    catch (e) {
      return false
    }
  },

  async deleteCreator({}, payload){
    try{
      const { courseId, userId } = payload
      await axios.delete(`/api/v2/admin/courses/${courseId}/creators/${userId}`);
      return true;
    }
    catch (e) {
      return false
    }
  },

  async fetchCourseManagers({ commit }, courseId) {
      try {
          const result =  await axios.get(`/admin/course/${courseId}/managers`);
          const { data, error } = result.data;
          if (error) {
              throw new Error(data?.message);
          }
          commit('SET_COURSE_MANAGERS', data ?? []);
      } catch (error) {
          throw error;
      }
    },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
