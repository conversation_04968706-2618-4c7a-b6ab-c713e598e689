<template>
  <Home
    title="COURSES.HOME.TITLE"
    description="COURSES.HOME.DESCRIPTION"
    src-thumbnail="/assets/imgs/courses_new_app.svg"
    :allowFilters="true"
    :showFilterMessage="true"
    :numberOfFiltersApplied="appliedFiltersCount"
    @apply-filters="applyFilters"
    @clear-filters="resetFilters"
  >
    <template v-slot:content-actions>
      <button
        v-if="$auth.hasPermission(COURSE_PERMISSIONS.EXPORT)"
        class="btn btn-primary"
              data-bs-toggle="modal"
              data-bs-target="#courseViewExportModal"
      >
        {{ $t("COURSE.EXPORT.TITLE") }}
      </button>
      <button
        v-if="$auth.hasPermission(COURSE_PERMISSIONS.CREATE)"
        class="btn btn-primary"
        @click="createCourse">
        {{ $t("COURSE.CREATE_COURSE") }}
      </button>
    </template>
    <template v-slot:content-filters>
      <div class="row mb-3">
        <div class="form-group col-12 col-md-6 col-lg-4">
          <label for="type">{{ $t("TYPE") }}</label>
          <Multiselect
            v-model="filters.type"
            :options="typeCourses"
            :multiple="true"
            :close-on-select="true"
            :clear-on-select="false"
            :preserve-search="true"
            :placeholder="$t('ALL')"
            label="name"
            track-by="id"
            :preselect-first="false"
            :show-labels="false"
          />
        </div>
        <div class="form-group col-12 col-md-6 col-lg-4">
          <label for="category">{{ $t("CATEGORY") }}</label>
          <Multiselect
            v-model="filters.category"
            :options="courseCategories"
            :multiple="true"
            :close-on-select="true"
            :clear-on-select="false"
            :preserve-search="true"
            :placeholder="$t('ALL')"
            label="name"
            track-by="id"
            :preselect-first="false"
            :show-labels="false"
          />
        </div>
        <div class="form-group col-12 col-md-6 col-lg-4">
          <label for="createdBy">{{ $t("CREATED_BY") }}</label>
          <Multiselect
            v-model="filters.createdBy"
            :options="creators"
            :multiple="true"
            :close-on-select="true"
            :clear-on-select="false"
            :preserve-search="true"
            :placeholder="$t('ALL')"
            label="name"
            track-by="id"
            :preselect-first="false"
            :show-labels="false"
          />
        </div>
        <div class="form-group col-12 col-md-6 col-lg-4">
          <label>{{ $t("FILTER.CREATED_AT_IN") }}</label>
          <div class="d-flex align-items-center gap-3">
            <div class="col-6 px-0">
              <input
                type="date"
                v-model="filters.createdStartDate"
                @change="validateDates"
                :max="filters.createdEndDate"
                class="form-control"
              />
              <span v-if="dateErrors.start" class="text-danger">{{
                dateErrors.start
              }}</span>
            </div>
            <div class="col-6 pl-0">
              <input
                type="date"
                v-model="filters.createdEndDate"
                @change="validateDates"
                :min="filters.createdStartDate"
                class="form-control"
              />
              <span v-if="dateErrors.end" class="text-danger">{{
                dateErrors.end
              }}</span>
            </div>
          </div>
        </div>
        <div class="form-group col-12 col-md-6 col-lg-4">
          <label for="language">{{ $t("LOCALE") }}</label>
          <Multiselect
            v-model="filters.language"
            :options="languages"
            :multiple="true"
            :close-on-select="true"
            :clear-on-select="false"
            :preserve-search="true"
            :placeholder="$t('ALL')"
            label="name"
            track-by="id"
            :preselect-first="false"
            :show-labels="false"
          />
        </div>
        <div class="form-group col-12 col-md-6 col-lg-4">
          <label>{{ $t("STATUS") }}</label>
          <div class="row">
            <div class="col-lg-6">
              <div class="form-check pl-0 d-flex align-items-center gap-1 mb-2">
                <BaseSwitch v-model="filters.active" :tag="'active'" />
                <label class="form-check-label" for="active">{{
                 $t("COURSE.ACTIVE")
                }}</label>
              </div>
              <div class="form-check pl-0 d-flex align-items-center gap-1 mb-2">
                <BaseSwitch
                  v-model="filters.visibleInCampus"
                  :tag="'visibleInCampus'"
                />
                <label class="form-check-label" for="visibleInCampus">{{
                  $t("USER.LABEL.OPEN_CAMPUS")
                }}</label>
              </div>
            </div>
            <div class="col-lg-6">
              <div class="form-check pl-0 d-flex align-items-center gap-1 mb-2">
                <BaseSwitch v-model="filters.visibleByFilter" :tag="'published'" />
                <label class="form-check-label" for="published">{{
                  $t("COURSE.VISIBLE_BY_FILTER")
                }}</label>
              </div>
              <div class="form-check pl-0 d-flex align-items-center gap-1 mb-2">
                <BaseSwitch v-model="filters.new" :tag="'new'" />
                <label class="form-check-label" for="new">{{
                  $t("NEW")
                }}</label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-slot:content-main>
      <div
        class="d-flex align-items-center justify-content-center"
        v-if="loading"
      >
        <Spinner />
      </div>
      <div v-show="!loading">
        <div class="table-responsive" >
          <table class="table">
            <thead>
            <tr>
              <th>{{ $t("ID") }}
                <SortButton
                  sort-by="id"
                  :current="sortParams[0]"
                  @toggle="sortBy"
                  :disabled="loading"
                />
              </th>
              <th>{{ $t("NAME") }}
                <SortButton
                  sort-by="name"
                  :current="sortParams[0]"
                  @toggle="sortBy"
                  :disabled="loading"
                />
              </th>
              <th>{{ $t("TYPE") }}
                <SortButton
                  sort-by="typeCourse"
                  :current="sortParams[0]"
                  @toggle="sortBy"
                  :disabled="loading"
                />
              </th>
              <th>{{ $t("CATEGORY") }}
                <SortButton
                  sort-by="category"
                  :current="sortParams[0]"
                  @toggle="sortBy"
                  :disabled="loading"
                />
              </th>
              <th v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.PUBLISH)" class="text-center">{{ $t("COURSE.ACTIVE") }}
                <SortButton
                  sort-by="active"
                  :current="sortParams[0]"
                  @toggle="sortBy"
                  :disabled="loading"
                />
              </th>
              <th class="text-center">{{ $t("CHAPTERS.LABEL.PLURAL") }}
                <SortButton
                  sort-by="totalChapters"
                  :current="sortParams[0]"
                  @toggle="sortBy"
                  :disabled="loading"
                />
              </th>
              <th class="text-center">{{ $t("LOCALE") }}
                <SortButton
                  sort-by="locale"
                  :current="sortParams[0]"
                  @toggle="sortBy"
                  :disabled="loading"
                />
              </th>
              <th></th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="course in courses" :key="course.id">
              <td>
                {{ course.id }}
              </td>
              <td>
                <div class="container-name-course">
                  <img :src="course.thumbnailUrl || 'assets/chapters/default-image.svg'" @error="() => course.thumbnailUrl = 'assets/chapters/default-image.svg'" :alt="course.name"/>
                  <div>
                    <router-link :to="{name: ROUTE_NAMES.VIEW_COURSE, params: { id: course.id, name: course.name }}">{{ course.name }}</router-link>
                    <p class="code">{{ course.code }}</p>
                  </div>
                </div>
              </td>
              <td>
                <i class="text-primary fs-3" :class="`${course.icon}`"
                  data-toggle="tooltip"
                  data-placement="top"
                  :title="course.typeCourse"
                />
              </td>
              <td>{{ course.category }}</td>
              <td v-if="$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.PUBLISH)">
                <BaseSwitch
                  class="justify-content-center"
                  :tag="`switcher-pages-${course.id}`"
                  v-model="course.active"
                  @change="publishCourse(course)"
                  theme="light"
                />
              </td>
              <td class="text-center">
                <span class="badge badge-secondary">
                  {{ course.totalChapters }}
                </span>
              </td>
              <td class="text-uppercase text-center">
                <b @click="goToView(course.id, course.name)" class="baseLanguage">
                  {{ course.locale }}
                  {{ course.languages.length > 0 ? " - " : "" }}</b>
                <span v-for="(language, index) in course.languages.slice(0, 3)" v-if="course.languages.length > 0" :key="index">
                    <router-link :to="{
                      name: ROUTE_NAMES.VIEW_COURSE,
                      params: {
                        ...$route.params,
                        id: language.idCourse,
                        name: language.nameCourse
                      },
                      }" custom v-slot="{ navigate }">
                            <a @click="navigate"
                                :style="!language.published ? { color: 'orange' } : { color: 'hsl(198, 99%, 34%)' }"
                                class="language-link"
                                :data-tooltip="!language.published ? $t('COURSE.LANGUAGES.NOAVAILABLE')  : null">
                              {{ language.locale }}
                            </a>
                    </router-link>
                  <span v-if="index < 2 && index < course.languages.length - 1"> - </span>
                </span>
                <BaseIconInfo v-if="course.languages.length > 3" :tooltipItems="course.languages" position="top" :route="ROUTE_NAMES.VIEW_COURSE" />
              </td>
              <td class="text-right">
                <div class="dropdown">
                  <button
                    class="btn btn-default"
                    type="button"
                    :id="`dropdown-menu-${course.id}`"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                    style="margin-left: 12px"
                  >
                    <i class="fa fa-ellipsis-h"></i>
                  </button>
                  <ul
                    class="dropdown-menu"
                    :aria-labelledby="`dropdown-menu-${course.id}`"
                  >
                    <li v-if="$auth.hasPermission(COURSE_PERMISSIONS.SHARE)" @click="shareCourse(course.id)" role="button">
                      <a class="dropdown-item justify-content-center">
                        {{ $t("SHARE") }}
                      </a>
                    </li>
                    <li @click="goToView(course.id, course.name)">
                      <span class="dropdown-item justify-content-center">
                        {{ $t("VIEW") }}
                      </span>
                    </li>
                    <li v-if="$auth.hasPermission(COURSE_PERMISSIONS.CREATE)" @click="updateCourse(course.id, course.name)">
                      <span class="dropdown-item justify-content-center">
                        {{ $t("EDIT") }}
                      </span>
                    </li>
                    <li v-if="$auth.hasPermission(COURSE_PERMISSIONS.CLONE)" @click="cloneCourse(course.id)" role="button">
                      <span class="dropdown-item justify-content-center">
                        {{ $t("CLONE") }}
                      </span>
                    </li>
                    <li v-if="$auth.hasPermission(COURSE_PERMISSIONS.DELETE)" @click="deleteCourse(course.id)" role="button">
                      <span class="dropdown-item justify-content-center delete">{{
                        $t("DELETE")
                        }}
                      </span>
                    </li>
                  </ul>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="12" v-if="courses.length === 0">
                <BaseNotResult />
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        
        <Pagination
          class="justify-content-end"
          :prop-current-page="page"
          :total-items="totalCourses"
          @current-page="onCurrentPage"
        />
        <CourseExportModal/>
      </div>
    </template>
  </Home>
</template>

<script>
import $ from "jquery";
import { get } from "vuex-pathify";
import Home from "../../../../base/Home.vue";
import Spinner from "../../../../admin/components/base/Spinner.vue";
import BaseNotResult from "../../../../base/BaseNotResult.vue";
import Pagination from "../../../../admin/components/Pagination.vue";
import BaseSwitch from "../../../../base/BaseSwitch.vue";
import Multiselect from "vue-multiselect";
import searchMixin from "../../../../mixins/searchMixin";
import "../../../../../css/vueMultiSelect.css";
import ROUTE_NAMES from "../router/routeNames";
import CourseExportModal from "../components/modal/CourseExportModal.vue";
import SortButton from '../components/SortButton.vue'
import BaseIconInfo from "../../../../base/BaseIconInfo.vue";
import { removeKeyTabName } from "../helpers";
import {
  COURSE_ACTIONS_BY_ROLE,
  COURSE_PERMISSIONS,
} from '../../../../common/utils/auth/permissions/course.permissions'


export default {
  name: "HomeView",
  components: {
    SortButton,
    Pagination,
    BaseNotResult,
    Spinner,
    Home,
    BaseSwitch,
    Multiselect,
    CourseExportModal,
    BaseIconInfo
  },
  mixins: [searchMixin],
  data() {
    return {
      ROUTE_NAMES,
      page: 1,
      filters: {
        type: [],
        category: [],
        createdBy: [],
        createdStartDate: "",
        createdEndDate: "",
        language: [],
        active: false,
        new: false,
        visibleByFilter: false,
        visibleInCampus: false,
        search: "",
      },
      sortParams: ['createdAt', 'DESC'],
      dateErrors: {
        start: "",
        end: "",
      },
    };
  },
  computed: {
    COURSE_PERMISSIONS() {
      return COURSE_PERMISSIONS
    },
    COURSE_ACTIONS_BY_ROLE() {
      return COURSE_ACTIONS_BY_ROLE
    },
    ...get("coursesModule", {
      loading: "isLoading",
      courses: "getCourses",
      typeCourses: "getTypeCourses",
      courseCategories: "getCourseCategories",
      creators: "getCreators",
      totalCourses: "getTotalCourses",
    }),
    languages: get("localeModule/getLanguagesOptions"),
    appliedFiltersCount() {
      return [
        this.filters.type.length,
        this.filters.category.length,
        this.filters.createdBy.length,
        this.filters.createdStartDate.length ||
          this.filters.createdEndDate.length,
        this.filters.language.length,
        this.filters.open,
        this.filters.new,
        this.filters.visibleByFilter,
        this.filters.visibleInCampus,
      ].filter(Boolean).length;
    },
  },
  created() {
    removeKeyTabName();
    this.getCoursesDB();
    this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: this.$t("COURSES.HOME.TITLE"),
        params: {},
      },
    });
  },
  mounted() {
    this.$auth.setPermissionList(COURSE_ACTIONS_BY_ROLE)
  },
  updated() {
    $('[data-toggle="tooltip"]').tooltip()
  },
  beforeDestroy() {
    this.$auth.setPermissionList({})
  },
  methods: {
    validateDates() {
      this.dateErrors.start = "";
      this.dateErrors.end = "";

      if (this.filters.createdStartDate && this.filters.createdEndDate) {
        if (
          new Date(this.filters.createdStartDate) >
          new Date(this.filters.createdEndDate)
        ) {
          this.dateErrors.start = this.$t(
            "START_DATE_CANNOT_BE_AFTER_END_DATE"
          );
          this.dateErrors.end = this.$t("END_DATE_CANNOT_BE_BEFORE_START_DATE");
        }
      }
    },
    onSearch(searchValue) {
      this.filters.search = searchValue;
      this.applyFilters();
    },

    onCurrentPage(page) {
      this.page = page;
      this.getCoursesDB();
    },
    
    sortBy(value) {
      if (this.loading) return null;
      this.sortParams = [value.sortBy, value.direction]
      this.getCoursesDB();
    },
    
    getCoursesDB() {
      const [sortBy, sortDir] = this.sortParams
      const queryParams = new URLSearchParams({
        page: this.page,
        typeCourse: this.filters.type.map((item) => item.id),
        category: this.filters.category.map((item) => item.id),
        createdBy: this.filters.createdBy.map((item) => item.id),
        createdAt: this.filters.createdStartDate,
        dateTo: this.filters.createdEndDate,
        locale: this.filters.language.map((item) => item.id),
        open: this.filters.visibleByFilter ? 1 : 0,
        isNew: this.filters.new ? 1 : 0,
        active: this.filters.active ? 1 : 0,
        open_visible: this.filters.visibleInCampus ? 1 : 0,
        search: this.filters.search,
        sortBy,
        sortDir
      }).toString();

      this.$store.dispatch(
        "coursesModule/fetchCourses",
        `/admin/api/v1/courses?${queryParams}`
      );
    },
    async publishCourse(course) {
      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.UPDATE.PUBLISH)) return null;
      if(!course.completed && course.active){
        this.$nextTick(() => {
          course.active = false;
        });
        this.$toast.info(this.$t("COURSE.PUBLISH.MESSAGE"));
      }
      else if(course.completed || (!course.active && !course.completed)){
        let result = await this.$store.dispatch("coursesModule/updatePublishCourse", course.id);
        if(result.error){
          this.$toast.error(this.$t(result.message));
          this.$nextTick(() => {
            course.active = !course.active;
          });
        }
        else{
          this.$toast.success(this.$t(result.message));
        }
      }
    },
    
    async cloneCourse(id) {
      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.CLONE)) return null;
      try {
        this.$toast.info(this.$t("EXECUTING"));
        await this.$store.dispatch("coursesModule/cloneCourse", id);
        this.$toast.success(this.$t("SUCCESS"));
        this.getCoursesDB();
      } catch (e) {
        this.$toast.error(this.$t("FAILED"));
      }
    },
    async shareCourse(id) {
      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.SHARE)) return null;
      try {
        this.$toast.info(this.$t("EXECUTING"));
        const url = await this.$store.dispatch("coursesModule/shareCourse", id);
        await navigator.clipboard.writeText(url);
        this.$toast.success(this.$t("URL_COPIED"));
      } catch (e) {
        this.$toast.error(this.$t("COPY_FAILED"));
      }
    },
    async deleteCourse(id) {
      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.DELETE)) return null;
      try {
        this.$alertify.confirmWithTitle(
          this.$t("DELETE"),
          this.$t("COMMON_AREAS.QUESTION_DELETE"),
          async () => {
            let response = await this.$store.dispatch("coursesModule/deleteCourse", id);
            if(response && response.error){
              this.$toast.error(this.$t(response.message));
            }
            else{
              this.getCoursesDB();
              this.$toast.success(this.$t("DELETE_SUCCESS"));
            }
          },
          () => {}
        );
      } catch (e) {
        this.$toast.error(this.$t("DELETE_FAILED"));
      }
    },
    applyFilters() {
      this.validateDates();
      if (!this.dateErrors.start && !this.dateErrors.end) {
        this.page = 1;
        this.getCoursesDB();
      }
    },
    resetFilters() {
      this.filters = {
        type: [],
        category: [],
        createdBy: [],
        createdStartDate: "",
        createdEndDate: "",
        language: [],
        active: false,
        new: false,
        published: false,
        visibleInCampus: false,
        search: this.searchInputValue
      };
      this.applyFilters();
    },
    goToView(id, name) {
      this.$router.push({ name: ROUTE_NAMES.VIEW_COURSE, params: { id, name } }).catch()
    },
    updateCourse(id, name) {
      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.CREATE)) return null;
      this.$router.push({ name: ROUTE_NAMES.UPDATE_COURSE, params: { id, name } }).catch()
    },
    createCourse() {
      if (!this.$auth.hasPermission(COURSE_PERMISSIONS.CREATE)) return null;
      this.$router.push({ name: ROUTE_NAMES.CREATE_COURSE }).catch();
    }
  },
};
</script>

<style scoped lang="scss">
.Home {
  :deep(.Home--header--banner) {
    height: 190px !important;
  }
  :deep(.Home--header) {
    background-color: #f6f7f8;
  }
  :deep(.Home--content) {
    background-color: #ffffff;
  }

  :deep(.Home--content--main),
  :deep(.Home--content--actions) {
    padding: 1rem 2rem !important;
  }

  .badge-primary {
    background-color: rgb(181, 180, 180) !important;
  }

  .table-responsive {
    min-height: 400px;
  }

  .container-name-course {
    display: flex;
    gap: 1rem;
    align-items: center;
    a {
      font-size: 1rem;
    }
    img {
      width: 50px;
      height: 30px;
      flex-shrink: 0;
    }
  }

  :deep {
    .multiselect__placeholder {
      margin-bottom: 0 !important;
      color: var(--color-neutral-dark) !important;
    }

    .multiselect__tags {
      border: 1px solid var(--color-neutral-mid) !important;
    }

    .multiselect__tag,
    .multiselect__option--highlight {
      background: var(--color-primary) !important;
      text-transform: capitalize;
    }

    .multiselect__tag-icon::after {
      color: var(--color-neutral-light) !important;
    }

    .multiselect__option {
      text-transform: capitalize;
    }
  }
  :deep(mark) {
    background: var(--highlight-bg);
    border-radius: var(--border-radius);
    box-shadow: 0 1px 0 rgba(250, 204, 21, .4);
    color: inherit;
    padding: 0 1px;
  }
  .language-link {
    text-decoration: none;
    position: relative;
    cursor: pointer;
    isolation: isolate;
    z-index: 1;
    &:hover {
      text-decoration: underline;
    }
  }

  .language-link[data-tooltip]::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: calc(100% + 5px);
    left: 50%;
    transform: translateX(-50%);
    background-color: #ff9800;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    pointer-events: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    text-transform: none !important;
    width: max-content;
    text-align: center;
    z-index: 99999;
  }

  /* Flecha del tooltip */
  .language-link[data-tooltip]::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: #ff9800 transparent transparent transparent;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .language-link[data-tooltip]:hover::after,
  .language-link[data-tooltip]:hover::before {
    opacity: 1;
    visibility: visible;
  }

  .table-responsive, table, tr, td:has(.language-link) {
    position: static !important;
    overflow: visible !important;
  }
  
  .baseLanguage{
    color: hsl(198, 99%, 34%);
    cursor:pointer;
  }
  
  .code {
    color: $color-neutral-mid-darker;
    margin: 0;
    font-size: 0.8rem;
  }
}
</style>
