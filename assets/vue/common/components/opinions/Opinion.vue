<template>
  <div class="Opinion">
    <div class="Opinion--title" v-if="showTitle">
      {{ opinion.courseName }}
    </div>
    <div class="Opinion--header" :class="{'pt-3': !showTitle}">
      <img
        class="avatar userAvatar align-self-center align-self-sm-start"
        :src="`url(/uploads/users/avatars/${opinion.avatar ?? 'default.svg'})`"
        alt=" "
      />
      <div class="d-flex flex-column w-100">
        <span class="username w-100"
        >{{ opinion.firstName }} {{ opinion.lastName }}</span
        >
        <span class="date w-100 mt-1">{{ datePublish }}  </span>
      </div>
      <div class="rating d-flex flex-row nowrap align-items-center">
        <i v-for="i in 5" class="fa" :class="star(i)" :key="i"></i>
      </div>
    </div>
    <div class="Opinion--content with-visibility mt-2">
      <p ref="opinionText" class="mb-0 opinionText" :class="{ textEffect: showViewMore }">
        {{ opinion.text }}
      </p>
      <div
        v-if="opinion.visibleOptions && !readOnly"
        class="d-flex flex-column gap-2 align-items-start pl-2"
      >
        <div class="d-flex pl-2">
          <div class="custom-control custom-switch">
            <input
              type="checkbox"
              class="custom-control-input"
              :id="'switch_opinion_' + opinion.id"
              v-model="opinion.visible"
              @change="onChange(opinion)"
            />
            <label
              class="custom-control-label"
              :for="'switch_opinion_' + opinion.id"
            ></label>
          </div>
          <label class="form-label m-0 text-nowrap">{{
            $t("LIBRARY.COMMENTS.VISIBLE")
            }}</label>
        </div>
        <div class="d-flex pl-2">
          <div class="custom-control custom-switch">
            <input
              type="checkbox"
              class="custom-control-input"
              :id="'switch_opinion_highlight_' + opinion.id"
              v-model="opinion.highlight"
              @change="onChangeHighlight(opinion)"
            />
            <label
              class="custom-control-label"
              :for="'switch_opinion_highlight_' + opinion.id"
            ></label>
          </div>
          <label class="form-label m-0 text-nowrap">
            {{ $t("HIGHLIGHTED") }}
          </label>
        </div>
      </div>
      <div v-if="(opinion.extra || []).length || showViewMore" @click="openDetails(opinion)">
        <span
          class="link"
          data-bs-toggle="modal"
          data-bs-target="#opinionModal"
        >{{ $t("VIEW_MORE") }} </span>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import { UtilsMixin } from "../../../announcement/mixins/utilsMixin";

export default {
  name: "Opinion",
  mixins: [UtilsMixin],
  props: {
    opinion: {
      type: Object | Array,
      required: true,
    },
    showTitle: {
      type: Boolean,
      default: true,
    },
    readOnly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      observer: null,
      showViewMore: false
    }
  },
  computed: {
    rating() {
      return this.opinion.rating / 2;
    },
    
    datePublish() {
      const date = new Date(this.opinion.createdAt);
      const options = {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      };
      
      if(this.$i18n?.locale === 'es') return date.toLocaleDateString("es-ES", options);
      
      return date.toLocaleDateString("en-EN", options);
    },
  },
  mounted() {
    const el = this.$refs.opinionText
    this.observer = new ResizeObserver(() => {
      this.validateOverflow(el)
    })
    this.observer.observe(el)
    this.validateOverflow(el)
  },
  beforeDestroy() {
    this.observer.disconnect()
  },
  methods: {
    star(index) {
      const rating = this.rating;
      if (rating - Math.floor(rating) !== 0) {
        if (index - 1 === Math.floor(rating)) return "fa-star-half-alt active";
      }
      return index <= rating ? "fa-star active" : "fa-star";
    },
    onChange(opinion) {
      this.$emit("on-opinion-visibility-change", opinion);
    },
    onChangeHighlight(opinion) {
      this.$emit("on-opinion-highlight-change", opinion);
    },
    openDetails(opinion) {
      this.$emit("open-details", opinion);
    },
    validateOverflow(el) {
      const lineHeight = parseFloat(getComputedStyle(el).lineHeight);
      const lines = Math.round(el.scrollHeight / lineHeight);
      this.showViewMore = (lines > 2);
    }
  },
};
</script>

 <style scoped lang="scss"> 
.Opinion {
  display: flex;
  flex-flow: column;
  align-items: center;
  padding: 0 0 1rem;
  border: 1px solid $base-border-color;
  border-radius: 5px;
  min-height: 200px;
  
  .username {
    font-weight: 500;
  }
  
  .date {
    font-size: 0.9rem;
    color: var(--color-neutral-mid-darker);
  }
  
  &--title {
    width: 100%;
    padding: 0.25rem 1rem;
    margin-bottom: 1rem;
    background-color: var(--color-neutral-mid);
    font-weight: bold;
  }
  
  &--header {
    display: grid;
    grid-template-columns: 60px 1fr 100px;
    gap: 0.5rem;
    width: 100%;
    padding: 0 1rem;
    
    .rating {
      justify-self: right;
      
      .fa-star {
        color: var(--color-neutral-mid);
      }
      
      .fa-star.active,
      .fa-star-half-alt.active {
        color: #f7be73;
      }
    }
  }
  
  &--content {
    width: 100%;
    margin: 0.5rem 0 0 0;
    text-align: left;
    overflow-wrap: anywhere;
    padding: 0 1rem;
    
    &.with-visibility {
      display: grid;
      grid-template-columns: 1fr auto;
      align-items: start;
      justify-content: center;
    }

    .opinionText {
      max-height: 4rem;
      overflow-y: hidden;

      &.textEffect {
        mask-image: linear-gradient(to bottom, black 70%, transparent 100%);
        mask-repeat: no-repeat;
        mask-size: 100% 100%;
      }
    }
  }
  
  .avatar {
    width: 3rem;
    height: 3rem;
  }
  
  .link {
    color: var(--color-primary);
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>
