<template>
  <div class="TaskCourseHistoryItem">
    <div class="TaskCourseHistoryItem--status">
      <div class="status--selector">
        <div class="selector-container">
          <button class="selector"
                  v-for="(name, key) in historyDeliveryTaskStates" :key="key"
                  :class="(state === key ? 'active' : '') + ` state-${key}`" @click="updateState(key)">{{ $t(name) }}</button>
        </div>
      </div>
    </div>
    <div class="TaskCourseHistoryItem--files">
      <div class="w-100 d-flex align-items-center justify-content-center flex-column" v-if="loadingFiles">
        <loader :is-loaded="loadingFiles"/>
        <span>{{ $t('LOADING_FILES') }}</span>
      </div>
      <div class="TaskCourseHistoryItem--files--content" v-else>
        <div v-for="file in files" class="card file">
          <span class="icon-file"><i class="fa fa-file"></i></span>
          <p class="content">
            <span>{{ file.originalName }}</span>
            <span>{{ file.createdAt }}</span>
          </p>
          <button type="button" @click="download(file)"><i class="fa fa-download"></i></button>
        </div>
      </div>
    </div>
    <div class="TaskCourseHistoryItem--comments">
      <forum-body>
        <template v-slot:content>
          <div class="w-100 d-flex align-items-center justify-content-center flex-column" v-if="loadingComments">
            <loader :is-loaded="loadingComments"/>
            <span>{{ $t('LOADING_COMMENTS') }}</span>
          </div>
          <div class="TaskCourseHistoryItem--comments--content" v-else>
            <thread-message v-for="comment in comments" :reply-to="replyTo(comment.parent_id)" :key="comment.id" :message="comment" @reply="replyToMessage = $event"/>
          </div>
        </template>
      </forum-body>
      <forum-send-message
          :reply-to-message="replyToMessage"
          @send-message="sendMessage"
          :clear="timestamp"
          @cancel-reply="replyToMessage = null"></forum-send-message>
    </div>
  </div>
</template>

<script>
import UserComment from "../UserComment.vue";
import Loader from "../../../admin/components/Loader.vue";
import ThreadMessage from "../../../announcement/components/forum/ThreadMessage.vue";
import ForumSendMessage from "../forum/ForumSendMessage.vue";
import ForumBody from "../forum/ForumBody.vue";
export default {
  name: "TaskCourseHistoryItem",
  components: {ForumBody, ForumSendMessage, ThreadMessage, Loader, UserComment},
  props: {
    historyDeliveryTaskId: {
      type: Number|String,
      required: true
    },
    historyDeliveryItem: null
  },
  data() {
    return {
      loadingFiles: true,
      files: [],
      loadingComments: true,
      comments: [],

      state: 0,
      updatingState: false,
      replyToMessage: null,
      timestamp: null
    };
  },
  computed: {
    historyDeliveryTaskStates() {
      return this.$store.getters['taskCourseModule/getHistoryDeliveryTaskStates'];
    }
  },
  watch: {
    historyDeliveryTaskId(newValue) {
      this.loadFiles();
      this.loadComments();
    },
    historyDeliveryItem(newValue) {
      if (newValue) {
        this.state = this.historyDeliveryItem.state;
      }
    },
  },
  methods: {
    loadFiles() {
      if (this.historyDeliveryTaskId < 1) return;
      this.loadingFiles = true;
      this.$store.dispatch('taskCourseModule/loadTaskCourseHistoryFiles', this.historyDeliveryTaskId).then(res => {
        const { data, error } = res;
        this.files = data;
      }).finally(() => {
        this.loadingFiles = false;

      });
    },

    loadComments() {
      if (this.historyDeliveryTaskId < 1) return;
      this.loadingComments = true;
      this.$store.dispatch('taskCourseModule/loadTaskCourseHistoryComments', this.historyDeliveryTaskId).then(res => {
        const { data, error } = res;
        this.comments = data;
      }).finally(() => {
        this.loadingComments = false;
      });
    },

    updateState(state) {
      this.updatingState = true;
      this.$store.dispatch('taskCourseModule/updateHistoryDeliveryTaskState', { id: this.historyDeliveryTaskId, state }).then(res => {
        const { data, error } = res.data;
        if (error) {
          this.$toast.success(this.$t('HISTORY_DELIVERY_TASK.STATE.FAILED') + '');
        } else {
          this.state = state;
          this.$toast.success(this.$t('HISTORY_DELIVERY_TASK.STATE.UPDATED') + '');
        }
      }).finally(() => {
        this.updatingState = false;
      })
    },
    replyTo(id = null) {
      if (id) {
        const index = this.comments.findIndex(element => element.id === id);
        if (index >= 0) return this.comments[index];
      }
      return null;
    },
    sendMessage(message) {
      this.$store.dispatch('taskCourseModule/sendMessage', { id: this.historyDeliveryTaskId, message }).then(res => {
        const { error, data } = res;
        if (error) {
          this.$toast.error(data);
        } else {
          this.comments.push(data);
          this.timestamp = (new Date()).valueOf()
        }
      })
    },

    download(file) {
      const link = document.createElement('a');
      link.href = "uploads/task_user/"  + file.filename;
      link.download = file.filename;
      link.click();
    }
  }
}
</script>

 <style scoped lang="scss"> 
.TaskCourseHistoryItem {
  width: 100%;
  &--status {
    .status--selector {
      @include boxed-selector;

      .selector-container {
        padding-bottom: 0.5rem;
        border: none !important;
        background-color: #e1e1e1 !important;

        @media #{min-small-screen()} {
          display: grid;
          grid-template-columns: repeat(4, auto);
          justify-content: center;
          gap: 0.25rem;
        }
      }

      button.selector {
        width: 100% !important;
        height: 100% !important;
        &.active {
          &.state-1 {
            border: 1px solid $color-primary !important;
            color: #212121;
          }
          &.state-2 {
            border: 1px solid $color-info !important;
            color: #212121;
          }
          &.state-3 {
            border: 1px solid $color-error !important;
            color: #212121;
          }
          &.state-4 {
            border: 1px solid $color-success !important;
            color: #212121;
          }
        }
      }
    }
  }

  &--files {
    width: 100%;
    &--content {
      display: grid;
      grid-template-columns: repeat(auto-fit, 320px);
      border-bottom: 1px solid $base-border-color;
      padding: 1rem;
      gap: 1rem;
      background: #f2f2f2;
      margin: 0;

      .file {
        display: grid;
        grid-template-columns: 25px auto 40px;
        align-items: center;
        gap: 0.5rem;
        padding: 0.25rem 0.5rem;

        p {
          display: flex;
          flex-flow: column;
          align-items: center;
          justify-content: flex-start;
          margin: 0;
          padding: 0;
        }

        span.icon-file {
          font-size: 22px;
          justify-self: center;
          color: #808080;
        }

        button {
          border: none;
          background-color: $color-primary;
          width: 40px;
          height: 40px;
          font-size: 18px;
          color: #ffffff;
          &:first-child {
            border-radius: 5px;
          }
          &:last-child {
            border-radius: 50%;
            &:hover {
              transform: scale(1.1);
              transition: all .5s ease-in;
            }
          }
          &:hover {
            border: none;
          }
        }
      }
    }
  }

  &--comments {
    background-color: $forum-background-color;
    padding: 1rem;
  }
}
</style>
