<template>
  <div class="TextType">
    <p class="my-0">{{ question }}</p>
    <span class="form-control disabled">{{ answers }}</span>
  </div>
</template>

<script>
import typesMixin from './typesMixin'

export default {
  name: "TextType",
  mixins: [typesMixin],
}
</script>

<style scoped lang="scss">
.TextType {
  span {
    word-break: break-word;
    height: fit-content;
    min-height: 1.5lh;
  }
}
</style>