<template>
  <home
    title="ANNOUNCEMENT.HOME.TITLE"
    description="ANNOUNCEMENT.HOME.DESCRIPTION"
    src-thumbnail="/assets/imgs/announcement_home.svg"
    :order-applied="order.order != null"
    @reset="order.order = null"
    :allow-filters="true"
    @apply-filters="applyFilters()"
    @clear-filters="resetFilters()"
  >
    <template v-slot:content-actions>
      <!--      <router-link v-if="$isGranted('ROLE_MANAGER')"-->
      <!--                   class="btn btn-primary"-->
      <!--                   :to="{ name: 'CreateAnnouncement' }">{{ $t('ANNOUNCEMENT.NEW_ANNOUNCEMENT') }}</router-link>-->



      <div v-if="$auth.hasPermission(ANNOUNCEMENT_PERMISSIONS.TEMPLATE)" >
        <input
          type="file"
          accept=".xls, .xlsx"
          @change="handleFileUpload"
          ref="fileInput"
          style="display: none"
        />

        <button type="button" class="btn btn-warning" @click="openFileInput">
          <i class="fa fa-upload"></i> {{ $t("UPLOAD_TEMPLATE") }}
        </button>

          <button type="button" class="btn btn-warning" @click="downloadExcel">
          <i class="fa fa-download"></i> {{ $t("DOWNLOAD_TEMPLATE") }}
        </button>
      </div>
      
      <div v-if="$auth.hasPermission(ANNOUNCEMENT_PERMISSIONS.EXPORT)"  >
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalParticipantReport">
          <i class="fa fa-download mr-2"></i>{{ $t('ANNOUNCEMENT.DOWNLOAD_EXCEL_AP') }}
        </button>     
        <modalParticipantReport 
          @download="downloadExcelWithDates"
          :statusOptions="statusOptions"
          :extraOptions="extraOptions"
          :selectedExtras="selectedExtras"
        />
      </div>

      <button
        type="button"
        class="btn btn-primary"
        data-bs-toggle="modal"
        data-bs-target="#announcement-type-selection"
        v-if="$isGranted('ROLE_MANAGER') && hasFormationExtern"
      >
        {{ $t("ANNOUNCEMENT.NEW_ANNOUNCEMENT") }}
      </button>

      <button
        type="button"
        class="btn btn-primary"
        @click="goToCreateAnnouncement"
        v-if="$isGranted('ROLE_MANAGER') && !hasFormationExtern"
      >
        {{ $t("ANNOUNCEMENT.NEW_ANNOUNCEMENT") }}
      </button>
    </template>
    <template v-slot:content-filters>
      <div class="d-flex flex-row flex-nowrap align-items-end">
        <div class="form-group col-xs-12 col-md-4 mb-0" v-if="activePane !== 'PARTICIPANT-REPORTS'">
          <Multiselect
            v-model="selectedType"
            :options="typeCourses"
            :searchable="true"
            :allow-empty="false"
            track-by="id"
            label="name"
            :placeholder="$t('COURSE.TYPE_COURSE')"
            :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
          ></Multiselect>
        </div>

        <div class="form-group col-xs-12 col-md-8 mb-0">
          <i class="fas fa-search content-search-icon"></i>
          <label class="content-search-label w-75 pb-0">
            <input
              type="search"
              class="form-control"
              :placeholder="$t('SUBSCRIPTION.FILTER.SEARCH')"
              v-model="filters.query"
            />
          </label>
        </div>
      </div>

      <div class="d-flex flex-row flex-nowrap align-items-end">
        <div class="form-group mb-0 col-xs-12 col-md-4">
          <label>{{ $t("DATE_TIME_TITLE.FROM") }}</label>
          <input type="date" class="form-control" v-model="filters.fromDate" />
        </div>
        <div class="form-group mb-0 col-xs-12 col-md-4">
          <label>{{ $t("DATE_TIME_TITLE.TO") }}</label>
          <input
            type="date"
            class="form-control"
            v-model="filters.toDate"
            width="100%"
          />
        </div>

        <!--        <radio-button-->
        <!--          v-for="radio in typeCourses"-->
        <!--          :label="radio.name"-->
        <!--          :id="radio.type"-->
        <!--          :icon="radio.icon"-->
        <!--          :radio-value="radio.type"-->
        <!--          name="type_selection"-->
        <!--          v-model="filters.type"-->
        <!--          :key="radio.id"-->
        <!--          :disabled="false"-->
        <!--        />-->
      </div>
      <div class="d-flex flex-row flex-nowrap align-items-end">
        <div
          v-for="(extra, key) in extraOptions"
          :key="extra.id"
          class="form-group col-xs-12 col-md-4 mb-0"
          v-if="activePane !== 'PARTICIPANT-REPORTS'"
        >
          <label :for="key">{{ extra.name | uppercase }}</label>
    
          <select v-model="selectedExtras[extra.id]" :id="key" class="form-control">
            <option :value="null">{{ $t('ANNOUNCEMENT.STATUS.ALL') }}</option>
            <option
              v-for="(option, index) in extra.options"
              :key="index"
              :value="option" 
            >
              {{ option | uppercase }} 
            </option>
          </select>
        </div>
      </div>
      
      
    </template>
    <template v-slot:content-main>
      <ul class="nav nav-tabs ps-4 user-select-none hideOnPrint">
        <li class="nav-item" role="presentation" v-for="status in statusList">
          <button
            class="nav-link"
            :class="activePane === status ? 'active' : ''"
            id="info-tab"
            @click="activePane = status"
          >
            <i class="fa fa-file"></i>
            {{
              $t(
                status === "REPORTS"
                  ? "REPORT"
                  : status === "PARTICIPANT-REPORTS"
                    ? "ANNOUNCEMENT.PARTICIPANT-REPORTS"
                    : "ANNOUNCEMENT.STATUS." + status
              )
            }}
          </button>
        </li>
      </ul>
      <div class="tab-content bg-white p-1 p-sm-4">
        <div class="tab-pane active" v-if="activePane !== 'REPORTS' && activePane !== 'PARTICIPANT-REPORTS'">
          <div class="table-container">
            <table class="table datagrid">
              <thead>
                <tr>
                  <th></th>
               <!--    <th>
                    <a
                      class="cursor-pointer"
                      :class="order.order === 'code' ? 'active' : ''"
                      @click="setOrder('startAt')"
                      >{{ $t("ANNOUNCEMENT.FORM.ENTITY.ANNOUNCEMENT_NAME") }}
                      <i
                        class="fas"
                        :class="order.order === 'code' ? orderClass : 'fa-sort'"
                      ></i
                    ></a>
                  </th> -->
                  <th style="width: 30%;">
                    <a
                      class="cursor-pointer"
                      :class="order.order === 'name' ? 'active' : ''"
                      @click="setOrder('name')"
                      >{{ $t("COURSES.COURSE_NAME") }}
                      <i
                        class="fas"
                        :class="order.order === 'name' ? orderClass : 'fa-sort'"
                      ></i
                    ></a>
                  </th>
                  <th style="width: 16%;">
                    <a
                      class="cursor-pointer"
                      :class="order.order === 'startAt' ? 'active' : ''"
                      @click="setOrder('startAt')"
                      >{{ $t("ANNOUNCEMENT.DATES") }}
                      <i
                        class="fas"
                        :class="
                          order.order === 'startAt' ? orderClass : 'fa-sort'
                        "
                      ></i
                    ></a>
                  </th>
                  <th style="width: 20%;">{{ $t("ANNOUNCEMENT.DEFAULT.MODALITY") }}</th>
                  <th>
                    <a
                      class="cursor-pointer"
                      :class="order.order === 'total' ? 'active' : ''"
                      @click="setOrder('total')"
                      >{{ $t("ANNOUNCEMENT.NUMBER_OF_USER_CALLED_NO_NUMBER") }}
                      <i
                        class="fas"
                        :class="
                          order.order === 'total' ? orderClass : 'fa-sort'
                        "
                      ></i
                    ></a>
                  </th>
                  <th>{{ $t("STATUS") }}</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <tr v-if="isLoading">
                  <td colspan="9">
                    <div
                      class="w-100 d-flex align-items-center justify-content-center"
                    >
                      <spinner />
                    </div>
                  </td>
                </tr>
                <tr
                  v-for="announcement in announcements"
                  :key="announcement.id"
                  v-else
                >
                  <td>
                    <div class="thumbnail">
                      <a
                        data-bs-toggle="modal"
                        data-bs-target="#base-preview"
                        @click="openImagePreview(announcement.image)"
                      >
                        <img
                          :src="
                            announcement.image
                              ? `uploads/images/course/${announcement.image}`
                              : 'assets/chapters/default-image.svg'
                          "
                          alt=""
                          @error="$event.target.src = defaultImage"
                        />
                      </a>
                    </div>
                  </td>
                <!--   <td>
                    <router-link
                      :to="{
                        name:
                          announcement.source.toUpperCase() === 'EXTERN'
                            ? 'AnnouncementExternInfo'
                            : 'ViewAnnouncement',
                        params: { id: announcement.id },
                      }"
                      >{{ announcement.announcementCode }}</router-link
                    >
                  </td> -->
                  <td>
                    <router-link
                      :to="{
                        name:
                          announcement.source.toUpperCase() === 'EXTERN'
                            ? 'AnnouncementExternInfo'
                            : 'ViewAnnouncement',
                        params: { id: announcement.id },
                      }"
                      >{{ announcement.courseName }}<br> <small>{{ announcement.announcementCode }}</small></router-link
                    >
                  </td>
                  <td>
                    <date-time-and-local
                      v-model="announcement.startAt"
                      :timezone="announcement.timezone"
                    ></date-time-and-local>
                    <date-time-and-local
                      v-model="announcement.finishAt"
                      :timezone="announcement.timezone"
                    ></date-time-and-local>
                  </td>
                  <td>
                    {{ announcement.modality }}
                    <small class="text-black text-secondary">{{
                      announcement.denomination
                    }}</small>
                  </td>
                  <td class="text-center">{{ announcement.total }}</td>
                  <td>
                    <div
                      v-if="announcement.status"
                      class="Announcement--status"
                      :class="`Announcement--status--${announcement.status}`"
                    >
                      {{ $t("ANNOUNCEMENT.STATUS." + announcement.status) }}
                    </div>
                  </td>
                  <td>
                    <div class="dropdown">
                      <button
                        class="btn btn-default"
                        type="button"
                        :id="`dropdown-menu-${announcement.id}`"
                        data-bs-toggle="dropdown"
                        aria-expanded="false"
                      >
                        <i class="fa fa-ellipsis-h"></i>
                      </button>
                      <ul
                        class="dropdown-menu"
                        :aria-labelledby="`dropdown-menu-${announcement.id}`"
                      >
                        <li v-if="$isGranted('ROLE_MANAGER')">
                          <router-link
                            :to="{
                              name:
                                announcement.source.toUpperCase() === 'EXTERN'
                                  ? 'UpdateAnnouncementExtern'
                                  : 'UpdateAnnouncement',
                              params: { id: announcement.id },
                            }"
                            class="dropdown-item"
                            >{{ $t("EDIT") }}</router-link
                          >
                        </li>
                        <li>
                          <router-link
                            class="dropdown-item"
                            :to="{
                              name:
                                announcement.source.toUpperCase() === 'EXTERN'
                                  ? 'AnnouncementExternInfo'
                                  : 'ViewAnnouncement',
                              params: { id: announcement.id },
                            }"
                            >{{ $t("VIEW") }}</router-link
                          >
                        </li>
                        <li v-if="$isGranted('ROLE_MANAGER')">
                          <a
                            class="dropdown-item"
                            @click="cloneAnnouncement(announcement)"
                            >{{ $t("CLONE") }}</a
                          >
                        </li>
                        <li
                          v-if="
                            $isGranted('ROLE_MANAGER') &&
                            announcement.status !== STATUS_IN_PROGRESS()
                            && isDeleteAnnouncement(announcement)
                          "
                        >
                          <a
                            class="dropdown-item delete"
                            @click="deleteAnnouncement(announcement)"
                            href="#"
                            >{{ $t("DELETE") }}</a
                          >
                        </li>
                      </ul>
                    </div>
                  </td>
                </tr>
                <tr v-if="announcements.length === 0 && !isLoading">
                  <td colspan="6">
                    <base-not-result />
                  </td>
                </tr>
              </tbody>
            </table>

            <BaseViewImagen identifier="base-preview" :image="imagePreview" />
          </div>

          <div class="Pagination">
            <pagination
              :prop-current-page="pagination.current"
              :total-items="pagination.totalItems"
              @current-page="onPageChange"
              v-if="pagination.totalItems && !isLoading"
            />
          </div>
        </div>

        <div v-if="activePane === 'REPORTS'" class="tab-pane active">
          <div class="table-container">
            <table class="table datagrid">
              <thead>
                <tr>
                  <th class="text-center">{{ $t("ANNOUNCEMENT.TITLE") }}</th>
                  <th class="text-center">{{ $t("FILE") }}</th>
                  <th class="text-center">{{ $t("CREATED_BY") }}</th>
                  <th class="text-center">{{ $t("CREATED_AT") }}</th>
                  <th class="text-center">{{ $t("STATUS") }}</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="r in reports" :key="r.taskId">
                  <td>
                    <router-link
                      :to="{
                        name:
                          r.source.toUpperCase() === 'EXTERN'
                            ? 'AnnouncementExternInfo'
                            : 'ViewAnnouncement',
                        params: { id: r.announcementId },
                      }"
                      >{{ r.announcementName }}</router-link
                    >
                  </td>
                  <td>
                    <a
                      v-if="zipIsCompleted(r.status)"
                      :href="`/files/${r.filename}`"
                      target="_blank"
                      >{{ r.originalName }}</a
                    >
                    <span v-else>{{ r.originalName }}</span>
                  </td>
                  <td>{{ r.createdByName }}</td>
                  <td>{{ r.createdAt }}</td>
                  <td class="text-center">
                    <span
                      v-if="zipIsPending(r.status)"
                      class="badge bg-warning text-white"
                      >{{ $t("STATUS_INFORMATION.PENDING") }}</span
                    >
                    <loader
                      style="padding: 0 !important"
                      v-if="zipIsInProgress(r.status)"
                      :is-loaded="zipIsInProgress(r.status)"
                    />
                    <span
                      v-if="zipIsCompleted(r.status)"
                      class="badge bg-success text-white"
                      >{{ $t("STATUS_INFORMATION.COMPLETED") }}</span
                    >
                    <span
                      v-if="zipIsFailed(r.status)"
                      class="badge bg-danger text-white"
                      >{{ $t("ERROR") }}</span
                    >
                  </td>
                </tr>
                <tr v-if="loadingReports">
                  <td colspan="5">
                    <div
                      class="d-flex align-items-center justify-content-center"
                    >
                      <spinner />
                    </div>
                  </td>
                </tr>
                <tr v-if="reports.length === 0 && !loadingReports">
                  <td colspan="5">
                    <base-not-result />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="Pagination">
            <pagination
              :prop-current-page="reportPage"
              :total-items="reportsTotalItems"
              @current-page="onReportPageChange"
              v-if="reportsTotalItems > 10 && !loadingReports"
            />
          </div>
        </div>

        <div v-if="activePane === 'PARTICIPANT-REPORTS'" class="tab-pane active">
          <div class="table-container">
            <table class="table datagrid">
              <thead>
                <tr>
                  <th class="text-center">{{ $t("CREATED_AT") }}</th>
                  <th class="text-center">{{ $t("AVALIABLE_UNTIL") }}</th>
                  <th class="text-center">{{ $t("FILENAME") }}</th>
                  <th class="text-center">{{ $t("META") }}</th>
                  <th class="text-center">{{ $t("FINISHED_AT") }}</th>
                  <th class="text-center">{{ $t("STATUS") }}</th>
                  <th class="text-center">{{ $t("ACTION") }}</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(p, index) in participants" :key="p.id">
                  <td>
                    {{ p.created_at }}
                  </td>
                  <td>
                    {{ p.available_until }}
                  </td>
                  <td>
                    {{ p.filename }}
                  </td>
                  <td>
                    <div v-if="p.meta && typeof p.meta === 'object'">
                      <div v-for="(value, key) in p.meta" :key="key" v-if="['startDate', 'endDate', 'status'].includes(key)">
                        <div v-if="value !== null && value !== ''">
                          <strong class="d-block">{{ key | uppercase  }}:</strong>
                          <span>{{ value | uppercase  }}</span>
                        </div>
                      </div>
                      <div v-if="p.meta.extra && typeof p.meta.extra === 'object'">
                        <div v-for="(value, key) in p.meta.extra" :key="key">
                          <div v-if="value !== null && value !== ''">
                            <strong class="d-block">{{ translatedExtrasMap[key] || key | uppercase }}:</strong>
                            <span>{{ value | uppercase }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <p>No data available</p>
                    </div>
                  </td>
                  <td>
                    {{ p.finished_at }}
                  </td>

                  <td class="text-center">
                    <span
                      v-if="zipIsPending(p.status)"
                      class="badge bg-warning text-white"
                      >{{ $t("STATUS_INFORMATION.PENDING") }}</span
                    >
                    <loader
                      style="padding: 0 !important"
                      v-if="zipIsInProgress(p.status)"
                      :is-loaded="zipIsInProgress(p.status)"
                    />
                    <span
                      v-if="zipIsCompleted(p.status)"
                      class="badge bg-success text-white"
                      >{{ $t("STATUS_INFORMATION.COMPLETED") }}</span
                    >
                    <span
                      v-if="zipIsFailed(p.status)"
                      class="badge bg-danger text-white"
                      >{{ $t("ERROR") }}</span
                    >
                  </td>
                  <td>
                    <button
                      type="button"
                      v-if="p.status == 2"
                      @click="downloadFile(index)"
                      class="btn btn-sm btn-success mr-1"
                    >
                      <i class="fas fa-download"></i>
                    </button>
                  </td>
                </tr>
                <tr v-if="loadingParticipants">
                  <td colspan="5">
                    <div
                      class="d-flex align-items-center justify-content-center"
                    >
                      <spinner />
                    </div>
                  </td>
                </tr>
                <tr v-if="participants.length === 0 && !loadingParticipants">
                  <td colspan="5">
                    <base-not-result />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="Pagination">
            <pagination
              :prop-current-page="participantPage"
              :total-items="participantsTotalItems"
              @current-page="onParticipantPageChange"
              v-if="participantsTotalItems > 10 && !loadingParticipants"
            />
          </div>
        </div>
      </div>
      <announcement-type-selection @go="goToFormSelection" />
    </template>
  </home>
</template>

<script>
import * as XLSX from "xlsx";
import { get, sync } from "vuex-pathify";
import BaseNotResult from "../../base/BaseNotResult.vue";
import Home from "../../base/Home.vue";
import Pagination from "../../admin/components/Pagination.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import RadioButton from "../../common/components/RadioButton.vue";
import dateTimeFormatterMixin from "../../common/mixins/dateTimeFormatterMixin";
import DateTimeAndLocal from "../../common/components/DateTimeAndLocal.vue";
import AnnouncementTypeSelection from "../components/AnnouncementTypeSelection.vue";
import Multiselect from "vue-multiselect";

import modalParticipantReport from '../components/details/modals/modalParticipantReport.vue';


import {
  STATUS_ACTIVE,
  STATUS_ARCHIVED,
  STATUS_CONFIGURATION,
  STATUS_FINISHED,
  STATUS_IN_PROGRESS,
} from "../mixins/constants";
import { SOURCE_ANNOUNCEMENT_EXTERN } from "../store/module/announcementForm/common";
import zipFileTaskStatusMixin from "../../common/constants/ZipFileTaskStatus";
import Loader from "../../admin/components/Loader.vue";
import axios from 'axios';
import TaskQueueMixin from "../../mixins/TaskQueueMixin";
import {
  ANNOUNCEMENT_ACTIONS_BY_ROLE,
  ANNOUNCEMENT_PERMISSIONS
} from "../../common/utils/auth/permissions/announcement.permissions";

export default {
  name: "HomeView",
  components: {
    Loader,
    Multiselect,
    AnnouncementTypeSelection,
    DateTimeAndLocal,
    RadioButton,
    BaseNotResult,
    Home,
    Spinner,
    Pagination,
    modalParticipantReport,
  },
  mixins: [dateTimeFormatterMixin, zipFileTaskStatusMixin, TaskQueueMixin],
  data() {
    return {
      announcements: [],
      pagination: {
        current: 1,
        totalItems: 0,
      },
      order: {
        order: null,
        direction: "ASC",
      },
      courseBasePath: "",
      imagePreview: null,

      typeCourses: [],

      filters: {
        query: "",
        visible: false,
        fromDate: null,
        toDate: null,
        type: null,
      },
      selectedType: null,
      activePane: "ALL",
      hasFormationExtern: false,
      hasReportZip: false,
      hasReportTask: false,
      statusOptions: [],
      extraOptions: [],
      selectedExtras: {},
      selectedCommons: {},
      students: [],

      locale      : 'es',
			toastLocales: {
				'es' : { error: 'Se ha producido un error', success: 'Solicitud realizada con exito', info: 'Procesando su solicitud, por favor espere', fileSuccess: 'Sus archivos han sido generados con exito', notFound: 'No se encontro el reporte' },
				'en' : { error: 'An error has occurred', success: 'Successful request', info: 'Processing your request, please wait', fileSuccess: 'Your files have been successfully generated', notFound: 'Report not found' },
				'pt' : { error: 'Ocorreu um erro.', success: 'Pedido de sucesso', info: 'Processamento do seu pedido, por favor, espere', fileSuccess: 'Os seus ficheiros foram gerados com sucesso', notFound: 'Relatório não encontrado' },
			},
      pageSize: 10,
    };
  },
  computed: {
    ANNOUNCEMENT_PERMISSIONS() {
      return ANNOUNCEMENT_PERMISSIONS
    },
    ANNOUNCEMENT_ACTIONS_BY_ROLE() {
      return ANNOUNCEMENT_ACTIONS_BY_ROLE
    },
    translatedExtrasMap() {
      const map = {};
      this.extraOptions.forEach((extra) => {
        map[extra.id] = extra.name;
      });
      return map;
    },
    isLoading() {
      return this.$store.getters["announcementModule/isLoading"];
    },
    useGlobalEventBus() {
      return this.$store.getters["contentTitleModule/getUseGlobalEventBus"];
    },
    orderClass() {
      if (this.order.direction === "ASC") return "fa-sort-up";
      else if (this.order.direction === "DESC") return "fa-sort-down";
      return "fa-sort";
    },
    defaultImage: get("configModule/defaultImageB64"),
    isReportEnabled: get("configModule/config@isReportEnabled"),
    statusList() {
      let status = ["ALL", "CONFIGURATION", "ACTIVE", "FINISHED"];

      if (this.$isAdmin()) {
        status.push("ARCHIVED");
      }

      if (this.hasReportZip) status.push("REPORTS");
      if (this.hasReportTask) status.push("PARTICIPANT-REPORTS");

      return status;
    },
    toastText () { return this.toastLocales[this.locale] || this.toastLocales['en']; },


    reportPage: sync("announcementReportModule/reports@page"),
    reportsTotalItems: get("announcementReportModule/reports@totalItems"),
    loadingReports: get("announcementReportModule/loading"),
    reports: get("announcementReportModule/reports@items"),

    participantPage: sync("announcementReportModule/participantReports@page"),
    participantsTotalItems: get("announcementReportModule/participantReports@totalItems"),
    loadingParticipants: get("announcementReportModule/loadingParticipants"),
    participants: get("announcementReportModule/participantReports@items"),
  },
  watch: {
    selectedType: {
      immediate: true,
      deep: true,
      handler: function () {
        this.filters.type = this.selectedType?.id;
      },
    },
    order: {
      handler: function (val, oldVal) {
        this.loadAnnouncements();
      },
      deep: true,
    },
    activePane() {
      this.pagination.current = 1;
      this.pagination.totalItems = 0;
      if (this.activePane !== "REPORTS" && this.activePane !==  "PARTICIPANT-REPORTS") this.loadAnnouncements();
      else this.getAllReports();
    },
  },

  async created() {
    this.$auth.setPermissionList(ANNOUNCEMENT_ACTIONS_BY_ROLE)
    this.loadAnnouncements();
    this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: this.$t("ANNOUNCEMENTS"),
        params: {},
      },
    });
    await this.loadAvailableFilters();
  },
  beforeDestroy() {
    this.$auth.setPermissionList({})
  },
  methods: {
    downloadExcel() {

      axios({
        url: `/admin/announcement/form/template-participants`,
        method: 'GET',
        responseType: 'blob',
      }).then((response) => {

        const contentType = response.headers['content-type'];
        if (contentType !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' && contentType !== 'application/vnd.ms-excel') {
          this.$toast.error(this.$t('ANNOUNCEMENT.FORM.USERS.FORBIDDEN'));
          return;
        }

        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'announcement_import_template.xlsx');
        document.body.appendChild(link);
        link.click();
        link.remove();
      }).catch((error) => {
        this.$toast.error(this.$t('ANNOUNCEMENT.FORM.USERS.FORBIDDEN'));
      });
    },

    openFileInput() {
      this.$refs.fileInput.click();
    },
    
    async handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      try {

        const validExtensions = ['xlsx', 'xls'];
        const fileExtension = file.name.split('.').pop().toLowerCase();
        
        if (!validExtensions.includes(fileExtension)) {
          this.$toast.error(this.$t('ANNOUNCEMENT.FORM.USERS.INVALID_FILE_TYPE'));
          return;
        }

        const validMimeTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel'
        ];
        
        if (!validMimeTypes.includes(file.type)) {
          this.$toast.error(this.$t('ANNOUNCEMENT.FORM.USERS.INVALID_FILE_TYPE'));
          return;
        }

        const fileData = await this.readFileAsync(file);

        let rows = this.parseExcelData(fileData);

        if (rows.length <= 1) {
          this.$toast.error(this.$t('NO_DATA') + '');
          return;
        }

        rows = rows.splice(0, 1);// Remove first row

        const duplicates = this.findDuplicateEmails(rows);
        if (duplicates.length > 0) {
          this.$toast.error(this.$t('ANNOUNCEMENT.FORM.UPLOAD_USERS.EMAIL_DUPLICATED') + '');
          let error = {
            error: true,
            type: 'AnnouncementStudents',
            data: {
              i18n: ['ANNOUNCEMENT.FORM.UPLOAD_USERS.EMAIL_DUPLICATED'],
              errorExtra: duplicates
            },
          };

          let errors = {
            'AnnouncementStudents': error
          };

          this.$store.dispatch('announcementFormModule/setErrors', errors);
          return;
        }

        if (!this.processExcelRows(rows)) {
          // Invalid email
          this.$toast.error(
            this.$t("ANNOUNCEMENT.FORM.ENTITY.USER_INVALID_EMAIL") + ""
          );
          return;
        }

        const formData = new FormData();
        formData.append("file", file);
        this.uploadingFiles = true;
        let result = await this.$store.dispatch(
          "announcementFormModule/uploadParticipantsExcelFile",
          formData
        );
        let error = result.error;
        let { data } = result.data;
        let not_found = result.data.not_found ?? [];

        if (!error) {
          this.$toast.success(
            this.$t("ANNOUNCEMENT.FORM.USERS.UPLOAD_FILE_SUCCESS") + ""
          );
          this.$eventBus.$emit("USER_FILTER.EVENT.SEARCH");

          if (not_found && not_found.length > 0) {
            let error = {
              error: true,
              type: 'AnnouncementStudents',
              data: {
                i18n: ['ANNOUNCEMENT.FORM.UPLOAD_USERS.USERS_NOT_FOUND'],
                errorExtra: not_found
              },
            };
            let errors = {
              'AnnouncementStudents': error
            };

            await this.$store.dispatch('announcementFormModule/setErrors', errors);
          }
        }else{
          this.$toast.error(
            result.data
          );
        }
      } catch (error) {
        console.error(error);
        this.$toast.error("Error processing Excel file");
      } finally {
        this.uploadingFiles = false;
      }
    },

    async readFileAsync(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(e);
        reader.readAsBinaryString(file);
      });
    },
    findDuplicateEmails(rows) {
      if (rows.length < 3) return [];
      let duplicates = [];
      let checked = [];
      rows.forEach(row => {
        if (checked.includes(row[2])) {
            duplicates.push(row[2]);
        }
        checked.push(row[2]);
      });

      return duplicates;
    },
    processExcelRows(rows) {
      this.usersExcel = [];
      this.countInvalidExcel = 0;
      return rows.slice(1).every((row) => {
        if (row.length >= 3) {
          const validRegex =
            /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
          return row[2]?.match(validRegex);
        }
      });
    },
    parseExcelData(data) {
      const workbook = XLSX.read(data, { type: "binary" });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      return XLSX.utils.sheet_to_json(sheet, {
        header: 1,
        blankrows: false,
      });
    },

    STATUS_IN_PROGRESS() {
      return STATUS_IN_PROGRESS;
    },
    async loadAvailableFilters() {
      const { data } = await this.$store.dispatch(
          "announcementModule/availableFilters"
      );
      const { typeCourses, hasFormationExtern, hasReportZip, hasReportTask, extraOptions } = data;
      this.typeCourses = typeCourses;
      this.hasFormationExtern = hasFormationExtern;
      this.hasReportZip = hasReportZip;
      this.hasReportTask = hasReportTask;

      this.statusOptions = extraOptions.STATUS;
      this.extraOptions = extraOptions.extra;

      this.extraOptions.forEach(extra => {
        this.$set(this.selectedExtras, extra.id, null);
      });

    },
    resetFilters() {
      this.pagination.current = 1;
      this.order.order = null;
      this.filters = {
        visible: false,
        startAt: null,
        finishAt: null,
        type: null,
      };
      this.selectedType= null;
      this.selectedExtras = {};
      this.loadAnnouncements();
    },
    setOrder(order) {
      if (this.order.order === order) {
        this.order.direction = this.order.direction === "ASC" ? "DESC" : "ASC";
      } else {
        this.order.order = order;
        this.order.direction = "ASC";
      }
    },
    onPageChange(page) {
      this.pagination.current = page;
      this.loadAnnouncements();
    },
    applyFilters() {
      this.pagination.current = 1;
      this.loadAnnouncements();
    },
    loadAnnouncements() {
      let type = this.activePane=='PARTICIPANT-REPORTS' ? 'participants' : 'announcements';
      let extra = this.selectedExtras || {};

      this.$store
        .dispatch("announcementModule/"+type, {
          ...{ page: this.pagination.current, query: "" },
          ...this.order,
          ...this.filters,
          ...{ status: this.activePane },
          ...(type === 'participants' && { page: this.participantPage, pageSize: this.pageSize }),
          ...{ extra }


        })
        .then((res) => {
          const { data, error } = res;
          if(type=='announcements'){
            this.announcements = data.items;
            this.pagination.totalItems = data["total-items"];
          }else{
            this.$store.dispatch('announcementReportModule/updateReports', {
              items: data,
              totalItems: data.length
            });
          }
        });
    },
    deleteAnnouncement(announcement) {
      let title = "ANNOUNCEMENT.DELETE.CONFIRM.TITLE";
      let description = "ANNOUNCEMENT.DELETE.CONFIRM.DESCRIPTION";
      let messages = 'ANNOUNCEMENT.DELETE.SUCCESS';
      switch (announcement.status) {
        case STATUS_ACTIVE:
          title = "ANNOUNCEMENT.DELETE.CONFIRM.ACTIVE.TITLE";
          description = "ANNOUNCEMENT.DELETE.CONFIRM.ACTIVE.DESCRIPTION";
          messages = 'ANNOUNCEMENT.STATUS_INACTIVE';
          break;
        case STATUS_ARCHIVED:
          messages = 'ANNOUNCEMENT.DELETE.SUCCESS_DELETE';
          break;
      }

      const fnDelete = () => {
        if (announcement.status === STATUS_ARCHIVED) {
          return this.$store.dispatch(
            "announcementModule/softDeleteAnnouncement",
            announcement.id
          );
        } else if (announcement.status === STATUS_ACTIVE) {
          return this.$store.dispatch(
            "announcementModule/deactivateAnnouncement",
            announcement.id
          );
        } else {
          return this.$store.dispatch(
            "announcementModule/deleteAnnouncement",
            announcement.id
          );
        }
      };

      this.$alertify.confirmWithTitle(
        this.$t(title),
        this.$t(description),
        () => {
          fnDelete().then((res) => {
            const { error } = res;
            if (error)
              this.$toast.error(this.$t("ANNOUNCEMENT.DELETE.FAILED") + "");
            else {
              this.$toast.success(this.$t(messages) + "");
              this.loadAnnouncements();
            }
          });
        },
        () => {}
      );
    },
    isDeleteAnnouncement(announcement){
      const hoy = new Date();
      const finishAt = new Date(announcement?.finishAt);
      const status = announcement?.status;

      let isDelete = false;
      if(( hoy <= finishAt || hoy >= finishAt ) && (status !== STATUS_ACTIVE && status !== STATUS_FINISHED))
        isDelete = true;

      return  isDelete;
    },

    cloneAnnouncement(announcement) {
      let title = "ANNOUNCEMENT.CLONE.CONFIRM.TITLE";
      let description = "ANNOUNCEMENT.CLONE.CONFIRM.DESCRIPTION";

      this.$alertify.confirmWithTitle(
        this.$t(title),
        this.$t(description),
        () => {
          this.$store
            .dispatch("announcementModule/cloneAnnouncement", announcement.id)
            .then((res) => {
              const { error, data } = res;
              if (error)
                this.$toast.error(this.$t("ANNOUNCEMENT.CLONE.FAILED") + "");
              else {
                this.$toast.success(this.$t("ANNOUNCEMENT.CLONE.SUCCESS") + "");

                this.$router.push({
                  name: "UpdateAnnouncement",
                  params: { id: data?.idAnnouncement },
                });

              }
            });
        },
        () => {}
      );
    },

    openImagePreview(image) {
      this.imagePreview = `uploads/images/course/${image}`;
    },

    goToFormSelection(selection) {
      if (selection === SOURCE_ANNOUNCEMENT_EXTERN)
        this.$router.push({ name: "CreateAnnouncementExtern" });
      else this.goToCreateAnnouncement();
    },

    goToCreateAnnouncement() {
      this.$router.push({ name: "CreateAnnouncement" });
    },

    getAllReports() {

      this.$store.dispatch(
        "announcementReportModule/getAllAnnouncementsReports",
        { page: this.activePane=='PARTICIPANT-REPORTS' ? this.participantPage : this.reportPage, pageSize: this.pageSize, type: this.activePane=='PARTICIPANT-REPORTS' ? 'participants' : null },
      );
    },

    onReportPageChange(page) {
      this.reportPage = page;
      this.getAllReports();
    },
    onParticipantPageChange(page) {
      this.participantPage = page;
      this.getAllReports();
    },
    downloadExcelWithDates(dates) {
      const payload = {
        courseId: this.courseId || 0,
        startDate: dates.startDate || null,
        endDate: dates.endDate || null,
        status: dates.status || null,
        extras: dates.extras || null,
      };

      this.enqueueTask({
        url: 'admin/announcement_participants_export',
        data: payload,
        messages: {
          success: this.$t('Reporte generado con éxito'),
          error: this.$t('Error al generar el reporte')
        }
      });
      
    },


    downloadFile(index, id = null, filename = null) {
			this.showLoader = true;

      if (typeof this.participants[index] === 'undefined') {
        console.error(`Index ${index} is invalid`);
        return;
      }

      const participant = this.participants[index];

      if (!participant) {
        console.error('The participant is not defined');
        return;
      }


			this.$toast.info( this.toastText.info);
			axios.post('/admin/stats/stats-download-file', this.prepareData({id: id || participant.id}), {responseType: 'blob'})
				.then(response => {
					this.$toast.success( this.toastText.success );
					this.parseAndDownloadFile(response, filename || participant.filename);
				}).catch(() => { this.$toast.error( this.toastText.notFound ); })
				.finally(() => { this.showLoader = false; });
		},

		prepareData(object) {
			const params = new URLSearchParams();
			Object.keys(object).forEach(key => params.append(key, object[key]))
			return params;
		},

		parseAndDownloadFile(response, filename = null) {
			const fileURL = window.URL.createObjectURL(new Blob([response.data]))
			let fileLink = document.createElement('a');

			fileLink.href = fileURL;
			fileLink.setAttribute('download', `${filename || this.filters.filename}.xlsx`);
			document.body.appendChild(fileLink);

			fileLink.click();
		},
    
    getTranslation(key) {
      return this.$t(key, {}, this.app.user.locale);
    }
  },
  filters: {
    uppercase (value) {
      if (!value) return '';
      return value.toString().toUpperCase();
    }
  },
};
</script>

 <style scoped lang="scss"> 
.thumbnail {
  width: 50px !important;
  height: 50px !important;
  img {
    width: 100%;
    height: 100%;
  }
}
.dropdown-item.delete {
  background-color: #dc3545 !important;
  color: #ffffff;
  &:hover {
    background-color: #f87672;
  }
}

.table-container {
  width: 100%;
  overflow: auto;
}
</style>
