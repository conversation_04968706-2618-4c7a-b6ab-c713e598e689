<template>
  <div class="AnnouncementTasks">
    <div
      class="w-100 d-flex align-items-center justify-content-center flex-column"
      v-if="loading"
    >
      <spinner />
    </div>
    <div v-show="!loading">
      <div class="w-100">
        <button type="button" class="mr-1 btn btn-sm"
                v-for="(g, index) in groupStudents"
                :key="index"
                :class="g.groupInfo.id === selectedGroup?.groupInfo?.id ? 'btn-primary' : 'btn-default'"
                @click.stop="setSelectedGroup(g)">{{ g.name }}</button>
      </div>
      <div class="d-flex align-items-center justify-content-end mt-3">
        <router-link
          :to="{
            name: 'CreateAnnouncementTask',
            params: { id: announcement.id },
          }"
          class="btn btn-primary"
        >
          <i class="fa fa-plus mr-2"></i>{{ $t("ANNOUNCEMENT.TASKTAB.BUTTON") }}
        </router-link>
      </div>
      <div class="AnnouncementTasks--tasks mb-3 pb-3">
        <DataNotFound
          :hide-on="!filteredTasks.length"
          :text="$t('ANNOUNCEMENT.TASKTAB.NOT_FOUND') || ''"
          icon="fa-tasks"
          :banner="true"
        />
        <table class="table table-sm table-condensed m-0" v-show="filteredTasks.length">
          <thead>
            <tr>
              <th style="width: 50px"></th>
              <th>{{ $t("NAME") }}</th>
              <th class="text-center">{{ $t("TASK_COURSE.START_DATE") }}</th>
              <th class="text-center">{{ $t("TASK_COURSE.DEADLINE") }}</th>
              <th class="text-center">{{ $t("ANNOUNCEMENT.GROUP") }}</th>
              <th class="text-center">{{ $t("TASK_COURSE.FILES") }}</th>
              <!-- <th class="text-center">{{ $t('STATUS') }}</th> -->
              <th class="text-center">{{ $t("LIBRARY.COMMENTS.VISIBLE") }}</th>
              <th class="text-center" style="width: 90px">
                {{ $t("ACTIONS") }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="task in filteredTasks" :key="task.id">
              <td>
                <router-link
                  :to="{
                    name: 'ViewTaskCourse',
                    params: {
                      parentName: 'ViewAnnouncement',
                      parentId: announcement.id,
                      id: task.id,
                    },
                  }"
                  class="btn btn-sm btn-info"
                >
                  <i class="fa fa-list-ul"></i>
                </router-link>
              </td>
              <td>
                <router-link
                    :to="{
                    name: 'ViewTaskCourse',
                    params: {
                      parentName: 'ViewAnnouncement',
                      parentId: announcement.id,
                      id: task.id,
                    },
                  }">
                  {{ task.title }}
                </router-link>
              </td>
              <td class="text-center">
                <date-time-and-local
                    v-model="task.startDate"
                    :timezone="task.timezone"
                ></date-time-and-local>
              </td>
              <td class="text-center">
                <date-time-and-local
                    v-model="task.dateDelivery"
                    :timezone="task.timezone"
                ></date-time-and-local>
              </td>

              <td>{{ task.groups }}</td>
              <td class="text-center">
                <i class="fa fa-paperclip mr-1 subtitle"></i>
                <span class="badge">{{ task.total_files }}</span>
              </td>
              <!--
              <td class="text-center">
                <progress-bar
                  :value="task.progress"
                  :show-text="true"
                  :maxSize="50"
                />
              </td>
              -->
              <td class="text-center">
                <div class="custom-control custom-switch">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    :id="'switch_' + task.id"
                    v-model="task.isVisible"
                    @change="activateTask(task)"
                  />
                  <label
                    class="custom-control-label"
                    :for="'switch_' + task.id"
                  ></label>
                </div>
              </td>
              <td class="text-center">
                <div class="d-flex align-items-center justify-content-evenly">
                  <router-link
                    :to="{
                      name: 'UpdateTaskCourse',
                      params: { announcementId: announcement.id, id: task.id },
                    }"
                    class="btn btn-sm btn-primary ml-1"
                  >
                    <i class="fa fa-pencil"></i>
                  </router-link>
                  <button
                    type="button"
                    class="btn btn-sm btn-danger"
                    @click="deleteTask(task.id)"
                  >
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Loader from "../../../admin/components/Loader.vue";
import BaseNotResult from "../../../base/BaseNotResult.vue";
import ProgressBar from "../../components/details/progressBar";
import { UtilsMixin } from "../../mixins/utilsMixin";
import DateTimeTag from "../../components/details/dateTimeTag";
import DataNotFound from "../../components/details/DataNotFound";
import Spinner from "../../../admin/components/base/Spinner";
import DateTimeFormatterMixin from "../../../common/mixins/dateTimeFormatterMixin";
import DateTimeAndLocal from "../../../common/components/DateTimeAndLocal.vue";

export default {
  name: "AnnouncementTasks",
  mixins: [UtilsMixin, DateTimeFormatterMixin],
  components: {
    DateTimeAndLocal,
    Spinner,
    DataNotFound,
    DateTimeTag,
    ProgressBar,
    BaseNotResult,
    Loader,
  },
  data() {
    return {
      selectedGroup: null
    };
  },
  computed: {
    tasks: get("announcementModule/tasksCourse"),
    refresh: get("taskCourseModule/refresh"),
    announcement: get("announcementModule/announcement"),
    loading: get("announcementModule/loadingTasksCourse"),
    groupStudents: get('announcementModule/calledUsers'),
    canCreate() {
      if (this.announcement) {
        const current = new Date();
        const finishAt = new Date(this.announcement.finishAt);
        return current < finishAt;
      }
      return false;
    },
    filteredTasks() {
      if (this.selectedGroup == null) return this.tasks;
      return this.tasks.filter(task => (task.idsGroups.includes(this.selectedGroup.groupInfo.id)));
    }
  },
  watch: {
    refresh: {
      handler: function (val) {
        if (val) this.loadAnnouncementTasks();
      },
      immediate: true,
    },
  },
  methods: {
    setSelectedGroup(group) {
      if (this.selectedGroup?.groupInfo?.id === group.groupInfo.id) {
        this.selectedGroup = null;
        return;
      }
      this.selectedGroup = group;
    },
    loadAnnouncementTasks() {
      this.$store.dispatch(
        "announcementModule/loadAnnouncementTasks",
        this.announcement.id
      );
    },

    activateTask(task) {
      this.$store
        .dispatch("taskCourseModule/setTaskCourseVisible", {
          id: task.id,
          visible: task.isVisible,
        })
        .then((res) => {
          const { error } = res;
          if (error)
            this.$toast.error(
              this.$t("TASK_COURSE.VISIBILITY_UPDATE.FAILED") + ""
            );
        });
    },

    deleteTask(taskID) {
      this.$alertify.confirmWithTitle(
        this.$t("TASK_COURSE.DELETE.CONFIRM.TITLE"),
        this.$t("TASK_COURSE.DELETE.CONFIRM.DESCRIPTION"),
        () => {
          this.$store
            .dispatch("taskCourseModule/deleteTaskCourse", taskID)
            .then((res) => {
              const { error } = res;
              if (error)
                this.$toast.error(this.$t("TASK_COURSE.DELETE.FAILED") + "");
              else {
                this.$toast.success(this.$t("TASK_COURSE.DELETE.SUCCESS") + "");
                this.loadAnnouncementTasks();
              }
            });
        },
        () => {}
      );
    },
  },
};
</script>

 <style scoped lang="scss"> 
.AnnouncementTasks {
  width: 100%;

  .AnnouncementTasks--tasks {
    overflow-x: auto;
  }

  tr > th {
    border: none;
    white-space: nowrap;
  }

  .smallCol {
    width: 55px;
  }

  .subtitle {
    color: var(--color-neutral-mid-darker);
    font-size: 0.9rem;
  }

  .badge {
    background-color: var(--color-primary) !important;
    color: white !important;
  }

  .btn-info {
    background-color: var(--color-primary-light) !important;

    &:hover {
      color: var(--color-primary) !important;
    }
  }

  &--tasks {
    margin-top: 1rem;
  }
}
</style>
