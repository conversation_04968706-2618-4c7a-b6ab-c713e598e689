import { get } from 'vuex-pathify';

export const announcementManageMixin = {
    data() {
        return {
            loadingAnnouncementManagers: true,
            announcementManagers: [],
        }
    },
    computed:{
        announcement: get('announcementModule/announcement'),

        canManageAnnouncementContent() {
            if (!this.$auth.isManager()) {
                return false
            }

            const user = this.$auth.getUser()
            if (this.announcement?.createdBy === user.id) {
                return true
            }

            return this.announcementManagers?.some(manager => manager.id === user.id);
        }
    },

    watch: {
        announcement: {
            immediate: true,
            handler(newVal) {
                if (newVal && newVal.id) {
                    this.loadAnnouncementManagers(newVal.id);
                }
            }
        }
    },

    methods: {
        async loadAnnouncementManagers(id) {
            this.loadingAnnouncementManagers = true;
            try {
                const { data } = await this.$store.dispatch(
                    "announcementModule/loadAnnouncementManagers",
                    id
                );
                if (data) {
                    this.announcementManagers = data;
                }
            } catch (error) {
                console.error(error);
            } finally {
                this.loadingAnnouncementManagers = false;
            }
        },
    }
}
