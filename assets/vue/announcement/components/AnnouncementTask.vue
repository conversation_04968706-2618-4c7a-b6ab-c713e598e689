<template>
  <div class="d-flex align-items-center justify-content-center" v-if="loading">
    <loader :is-loaded="loading"></loader>
  </div>
  <div class="AnnouncementTask" v-else>
    <form
      action=""
      class="AnnouncementTask--form"
      @submit.prevent
      id="form-new-task"
    >
      <div class="AnnouncementTask--form--info">
        <div class="form-group required">
          <label for="">{{ $t("TASK_COURSE.TITLE") }} </label>
          <input
            id="title"
            name="title"
            type="text"
            class="form-control"
            v-model="task.title"
            :class="{ 'is-invalid': validationErrors.title }"
          />
          <div v-if="validationErrors.title" class="alert alert-danger mt-2">
            <small>
              <i class="fa fa-exclamation-triangle me-1"></i>
              {{ validationErrors.title }}
            </small>
          </div>
        </div>
        
        <div class="form-group required">
          <label>{{ $t("TASK_COURSE.DESCRIPTION") }}</label>
          <froala
            tag="textarea"
            v-model="task.description"
            :config="froalaDescriptionConfig"
          ></froala>
          <div v-if="validationErrors.description" class="alert alert-danger mt-2">
            <small>
              <i class="fa fa-exclamation-triangle me-1"></i>
              {{ validationErrors.description }}
            </small>
          </div>
        </div>

        <div class="form-group required">
          <label for="">{{ $t("TASK_COURSE.GROUP") }}</label>
          <Multiselect
            v-model="task.groups"
            :options="groups"
            :multiple="true"
            track-by="name"
            label="name"
            :placeholder="$t('MULTISELECT.PLACEHOLDER')"
            :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
            :deselectLabel="$t('MULTISELECT.DESELECT_LABEL')"
            :class="{ 'is-invalid': validationErrors.groups }"
          ></Multiselect>
          <div v-if="validationErrors.groups" class="alert alert-danger mt-2">
            <small>
              <i class="fa fa-exclamation-triangle me-1"></i>
              {{ validationErrors.groups }}
            </small>
          </div>
        </div>
      </div>
      
      <div class="AnnouncementTask--form--date">
        <div class="form-group required">
          <label for="start_date">{{ $t("TASK_COURSE.START_DATE") }}</label>
          <input
            id="start_date"
            name="start_date"
            type="datetime-local"
            class="form-control"
            v-model="task.startAt"
            :min="announcementStartAtFormatted"
            :max="announcementFinishAtFormatted"
            :class="{ 'is-invalid': validationErrors.startAt }"
          />
          <div v-if="validationErrors.startAt" class="alert alert-danger mt-2">
            <small>
              <i class="fa fa-exclamation-triangle me-1"></i>
              {{ validationErrors.startAt }}
            </small>
          </div>
        </div>
        
        <div class="form-group required">
          <label for="deadline">{{ $t("TASK_COURSE.DEADLINE") }}</label>
          <input
            id="deadline"
            name="deadline"
            type="datetime-local"
            class="form-control"
            v-model="task.deadline"
            :min="announcementStartAtFormatted"
            :max="announcementFinishAtFormatted"
            :class="{ 'is-invalid': validationErrors.deadline }"
          />
          <div v-if="validationErrors.deadline" class="alert alert-danger mt-2">
            <small>
              <i class="fa fa-exclamation-triangle me-1"></i>
              {{ validationErrors.deadline }}
            </small>
          </div>
        </div>
        
        <button-with-description
          title="TASK_COURSE.VISIBLE.TITLE"
          description="TASK_COURSE.VISIBLE.DESCRIPTION"
          v-model="task.visible"
          icon="fa fa-eye"
        ></button-with-description>
      </div>
    </form>
  </div>
</template>

<script>
import ButtonWithDescription from "../../common/components/ButtonWithDescription.vue";
import Loader from "../../admin/components/Loader.vue";
import Multiselect from "vue-multiselect";
import { get } from "vuex-pathify";

export default {
  name: "AnnouncementTask",
  components: { Loader, ButtonWithDescription, Multiselect },
  data() {
    return {
      loading: true,
      task: {
        title: "",
        description: "",
        startAt: "",
        deadline: "",
        visible: false,
        groups: [],
      },
      groups: [],
      groupsValue: [],
      validationErrors: {
        title: null,
        description: null,
        groups: null,
        startAt: null,
        deadline: null,
      },
    };
  },
  computed: {
    ...get("announcementModule", ["announcement"]),

    froalaDescriptionConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 250,
        pluginsEnabled: [
          "align",
          "link",
          "url",
          "lists",
          "paragraphStyle",
          "paragraphFormat",
          "quote",
        ],
      };
    },
    useGlobalEventBus() {
      return this.$store.getters["contentTitleModule/getUseGlobalEventBus"];
    },

    announcementStartAtFormatted() {
      if (!this.announcement?.startAt) return null;
      return new Date(this.announcement.startAt).toISOString().slice(0, 16);
    },

    announcementFinishAtFormatted() {
      if (!this.announcement?.finishAt) return null;
      return new Date(this.announcement.finishAt).toISOString().slice(0, 16);
    },
  },
  watch: {
    "task.title"() {
      if (this.validationErrors.title) {
        this.validateTitle();
      }
    },
    "task.description"() {
      if (this.validationErrors.description) {
        this.validateDescription();
      }
    },
    "task.startAt"() {
      if (this.validationErrors.startAt || this.validationErrors.deadline) {
        this.validateStartDate();
        this.validateDateRelation();
      }
    },
    "task.deadline"() {
      if (this.validationErrors.deadline || this.validationErrors.startAt) {
        this.validateDeadline();
        this.validateDateRelation();
      }
    },
    "task.groups"() {
      if (this.validationErrors.groups) {
        this.validateGroups();
      }
    },
  },

  created() {
    this.handleRouteParams();
    this.getGroupsAnnouncement();
    this.loadAnnouncementData();
  },
  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on("onTaskSave", () => {
        this.submit();
      });
    }
  },
  beforeDestroy() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$off("onTaskSave");
    }
  },
  methods: {
    handleRouteParams() {
      const isUpdating = this.$route.name === "UpdateTaskCourse";
      this.$store.dispatch("contentTitleModule/addRoute", {
        routeName: this.$route.name,
        params: {
          linkName: isUpdating
            ? this.$t("TASK_COURSE.UPDATE_TASK")
            : this.$t("TASK_COURSE.NEW_TASK"),
          params: this.$route.params,
        },
      });

      this.$store.dispatch("contentTitleModule/setActions", {
        route: this.$route.name,
        actions: [
          {
            name: this.$t("SAVE"),
            event: "onTaskSave",
            class: "btn btn-primary",
          },
        ],
      });

      if (isUpdating) {
        this.loading = true;
        this.$store
          .dispatch("taskCourseModule/loadTaskCourse", this.$route.params.id)
          .then((res) => {
            const { error, data } = res;
            if (error) {
              this.$toast.error("TASK_COURSE.FAILED_TO_LOAD");
              this.$router.go(-1);
            } else {
              this.task = data;
              this.task.deadline = this.task.deadline.slice(0, 16);
              this.task.startAt = this.task.startAt.slice(0, 16);
              this.loading = false;
            }
          });
      } else {
        this.loading = false;
      }
    },

    validateTitle() {
      this.validationErrors.title = null;
      
      if (!this.task.title || this.task.title.trim().length < 1) {
        this.validationErrors.title = this.$t("TASK_COURSE.TITLE_REQUIRED");
        return false;
      }
      
      return true;
    },

    validateDescription() {
      this.validationErrors.description = null;
      
      if (!this.task.description || this.task.description.trim().length < 1) {
        this.validationErrors.description = this.$t("TASK_COURSE.DESCRIPTION_REQUIRED");
        return false;
      }
      
      return true;
    },

    validateStartDate() {
      this.validationErrors.startAt = null;

      if (!this.task.startAt) {
        this.validationErrors.startAt = this.$t("TASK_COURSE.START_REQUIRED");
        return false;
      }

      const taskStartAt = new Date(this.task.startAt);
      const announcementStartAt = new Date(this.announcement.startAt);
      const announcementFinishAt = new Date(this.announcement.finishAt);

      const announcementStartFormatted = this.formatDateTimeForUser(announcementStartAt);
      const announcementFinishFormatted = this.formatDateTimeForUser(announcementFinishAt);

      if (taskStartAt < announcementStartAt) {
        this.validationErrors.startAt = this.$t(
          "TASK_COURSE.VALIDATION.START_DATE_AFTER",
          { date: announcementStartFormatted }
        );
        return false;
      }

      if (taskStartAt > announcementFinishAt) {
        this.validationErrors.startAt = this.$t(
          "TASK_COURSE.VALIDATION.START_DATE_BEFORE",
          { date: announcementFinishFormatted }
        );
        return false;
      }

      return true;
    },

    validateDeadline() {
      this.validationErrors.deadline = null;

      if (!this.task.deadline) {
        this.validationErrors.deadline = this.$t("TASK_COURSE.DEADLINE_REQUIRED");
        return false;
      }

      const taskDeadline = new Date(this.task.deadline);
      const announcementStartAt = new Date(this.announcement.startAt);
      const announcementFinishAt = new Date(this.announcement.finishAt);

      const announcementStartFormatted = this.formatDateTimeForUser(announcementStartAt);
      const announcementFinishFormatted = this.formatDateTimeForUser(announcementFinishAt);

      if (taskDeadline < announcementStartAt) {
        this.validationErrors.deadline = this.$t(
          "TASK_COURSE.VALIDATION.DEADLINE_AFTER",
          { date: announcementStartFormatted }
        );
        return false;
      }

      if (taskDeadline > announcementFinishAt) {
        this.validationErrors.deadline = this.$t(
          "TASK_COURSE.VALIDATION.DEADLINE_BEFORE",
          { date: announcementFinishFormatted }
        );
        return false;
      }

      return true;
    },

    validateDateRelation() {
      if (!this.task.startAt || !this.task.deadline) {
        return true;
      }

      const taskStartAt = new Date(this.task.startAt);
      const taskDeadline = new Date(this.task.deadline);

      if (taskStartAt > taskDeadline) {
        this.validationErrors.startAt = this.$t("TASK_COURSE.VALIDATION.START_BEFORE_DEADLINE");
        return false;
      }

      return true;
    },

    validateDates() {
      const isStartValid = this.validateStartDate();
      const isDeadlineValid = this.validateDeadline();
      const isRelationValid = this.validateDateRelation();

      return isStartValid && isDeadlineValid && isRelationValid;
    },

    formatDateTimeForUser(date) {
      if (!date) return '';

      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      };

      return date.toLocaleString('es-ES', options);
    },

    validateGroups() {
      this.validationErrors.groups = null;

      if (!this.task.groups || this.task.groups.length === 0) {
        this.validationErrors.groups = this.$t("TASK_COURSE.GROUP_REQUIRED");
        return false;
      }

      return true;
    },

    validateForm() {
      return [
        this.validateTitle,
        this.validateDescription,
        this.validateDates,
        this.validateGroups
      ].every(fn => fn.call(this))
    },


    submit() {
      if (!this.validateForm()) {
        return;
      }

      const currentForm = document.forms["form-new-task"];
      const formData = new FormData(currentForm);

      formData.append("visible", this.task.visible);
      formData.append("description", this.task.description);

      const isUpdating = this.$route.name === "UpdateTaskCourse";
      this.$alertify.confirmWithTitle(
        this.$t(
          "TASK_COURSE." + (isUpdating ? "UPDATE" : "CREATE") + ".CONFIRM.TITLE"
        ),
        this.$t(
          "TASK_COURSE." +
            (isUpdating ? "UPDATE" : "CREATE") +
            ".CONFIRM.DESCRIPTION"
        ),
        () => {
          this.saveTask(formData, isUpdating);
        },
        () => {}
      );
    },

    saveTask(formData, update = false) {
      const self = this;

      //parsear los grupos
      formData.append("groups", JSON.stringify(this.task.groups));

      function store() {
        if (update)
          return self.$store.dispatch(
            "announcementTaskModule/updateAnnouncementTask",
            {
              announcementId: self.$route.params.announcementId,
              taskId: self.$route.params.id,
              formData,
            }
          );
        else
          return self.$store.dispatch(
            "announcementTaskModule/addNewTaskToAnnouncement",
            {
              id: self.$route.params.id,
              formData,
            }
          );
      }

      store().then((res) => {
        const { data, error } = res;
        this.$toast.clear();
        if (error) {
          this.$toast.error(this.$t(data) + "");
        } else {
          this.$toast.success(
            this.$t(`TASK_COURSE${update ? ".UPDATE" : ".CREATE"}.SUCCESS`) + ""
          );
          this.$store.dispatch(
            "contentTitleModule/removeRouteFromContentTitle",
            this.$route.name
          );
          this.$store.dispatch("routerModule/setDeleteLastRoute", true);
          this.$store.dispatch("announcementModule/refreshAction", "tasks");
          this.$router.replace({
            name: "ViewTaskCourse",
            params: { id: data.id, announcementId: data.announcementId },
          });
        }
      }).catch(err => {
        this.$toast.clear();
        this.$toast.error(this.$t(err.response.data.data) + "");
      });
    },

    async getGroupsAnnouncement() {
      const idAnnouncement = this.$route.params?.announcementId
        ? this.$route.params?.announcementId
        : this.$route.params.id;

      const groups = await this.$store.dispatch(
        "announcementTaskModule/getGroupsAnnouncement",
        idAnnouncement
      );

      this.groups = groups;
    },

    async loadAnnouncementData() {
      const idAnnouncement = this.$route.params?.announcementId
        ? this.$route.params?.announcementId
        : this.$route.params.id;

      if (!this.announcement || this.announcement.id != idAnnouncement) {
        await this.$store.dispatch("announcementModule/loadAnnouncement", idAnnouncement);
      }
    },
  },
};
</script>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>

<style scoped lang="scss"> 
.AnnouncementTask {
  width: 100%;

  &--form {
    width: 100%;
    display: flex;
    flex-flow: row wrap;
    padding: 1rem;

    @media #{min-small-screen()} {
      display: grid;
      grid-template-columns: auto 320px;
    }

    div {
      padding: 0.25rem;
    }
  }
}

.form-group {
  .multiselect.is-invalid {
    border: 1px solid #dc3545;
    border-radius: 0.25rem;
  }

  .form-control.is-invalid {
    border-color: #dc3545;

    &:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
  }
}

// Estilos para las alertas
.alert {
  border-radius: 0.375rem;
  padding: 0.75rem 1rem;
  margin-bottom: 0;
  
  &.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
  }
  
  &.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
  }
  
  small {
    font-size: 0.875rem;
    margin: 0;
    
    .fa {
      margin-right: 0.25rem;
    }
  }
}
</style>