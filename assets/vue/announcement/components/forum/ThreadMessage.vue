<template>
  <div class="ThreadMessage p-3" :id="`thread-message-${message.id}`">
    <img :src="`url(/uploads/users/avatars/${message.avatar ?? 'default.svg'})`"
         class="userAvatar" alt=" ">
    <div class="message">
      <div class="message--header">
        <p class="username mb-0">
          <span class="mr-3">{{ message.name || (message.firstName + ' ' + message.lastName).trim() }}</span>
          <span class="subtitle">{{ getDateText(message.createdAt, false) }}</span>
        </p>
      </div>
      <div class="message--body line-break-anywhere">
        {{ message.message }}
      </div>
      <div class="message--reactions">
        <span class="badge cursor-pointer mb-0"
              @click="sendReaction"
              :class="{'badge-primary': message.liked, 'loadingAnimation': sendingInfo }">
          <i class="fa fa-thumbs-up mr-1"></i> {{ message.likes || 0 }}
        </span>
        <span class="badge mb-0" v-if="showReply"><i class="fa fa-comment-o mr-1"></i> {{ replies }}</span>
        <span class="badge cursor-pointer mb-0"
              :class="{'badge-danger': message.reported, 'loadingAnimation': sendingReport}"
              @click="reportMessage">
          <i class="fa" :class="{'fa-flag': message.reported, 'fa-flag-o': !message.reported}"></i>
        </span>
        <span v-if="showReply"
              class="badge cursor-pointer mb-0"
              @click="$emit('reply', message)">
          <i class="fa fa-reply"></i>
        </span>
      </div>
    </div>
  </div>
</template>

<script>/**
 * @event reply
 */
import {UtilsMixin} from "../../mixins/utilsMixin";

export default {
  name: "ThreadMessage",
  mixins: [UtilsMixin],
  props: {
    message: {
      type: Array|Object,
      required: true
    },
    showReply: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      sendingInfo: false,
      sendingReport: false,
    }
  },
  computed: {
    replies() {
      return this.message.replies ? (this.message.replies.length || 0) : 0;
    }
  },
  methods: {
    sendReaction() {
      if (this.sendingInfo) return;

      const { liked, likes } = { ...this.message };
      this.message.liked = !this.message.liked;
      this.message.likes -= (this.message.liked ? -1 : 1);

      this.sendingInfo = true;
      this.$store.dispatch('forumModule/sendReaction', this.message.id).then((response) => {
        this.message.likes = parseInt(response.data, 10);
      }).catch(() => {
        this.message.liked = liked;
        this.message.likes = likes;
      }).finally(() => { this.sendingInfo = false; });
    },
    reportMessage(){
      if (this.sendingReport) return;

      const { reported } = { ...this.message };
      this.message.reported = !this.message.reported;

      this.sendingReport = true;
      this.$store.dispatch('forumModule/sendReport', this.message.id).then(() => {}).catch(() => {
        this.message.reported = reported;
      }).finally(() => { this.sendingReport = false; });
    }
  }
}
</script>

 <style scoped lang="scss"> 
.ThreadMessage {
  @include post;
  border: solid var(--color-neutral-mid);
  border-width: 2px 0 0;
  margin: 0 auto;

  &:hover {
    background-color: initial;
  }

  .userAvatar {
    width: 2.5rem;
    height: 2.5rem;
  }

  .subtitle {
    color: var(--color-neutral-mid-darker);
    font-size: 0.8rem;
    font-weight: normal;
    white-space: nowrap;
  }

  .message--reactions {
    .badge {
      border: none;
      border-radius: 1rem;
      color: black;
      padding: 0.25rem 0.75rem;

      &.badge-danger {
        color: #E4534E;

        &.loadingAnimation {
          background-size: 60px 100%;
          animation-duration: 30s;
          background: linear-gradient(to right, #FFCDD2 8%, #FFEBEE 18%, #FFCDD2 33%);
        }
      }

      &.badge-primary {
        background-color: var(--color-primary) !important;
        color: white !important;

        &.loadingAnimation {
          background: linear-gradient(to right, var(--color-primary) 8%, var(--color-primary-light) 18%, var(--color-primary) 33%);
        }
      }
    }
  }
  .sendButton {
    align-self: stretch;
    width: 2.7rem;
  }
  .loadingAnimation {
    animation-duration: 30s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: loading;
    animation-timing-function: linear;
    background: #ddd;
    background: linear-gradient(to right, #F6F6F6 8%, #F0F0F0 18%, #F6F6F6 33%);
    background-size: 1200px 100%;
  }
  @-webkit-keyframes loading {
    0% { background-position: -100% 0; }
    100% { background-position: 100% 0; }
  }

  @keyframes loading {
    0% { background-position: -1200px 0; }
    100% { background-position: 1200px 0; }
  }
  .line-break-anywhere {
    overflow-wrap: anywhere !important;
  }
}
</style>
