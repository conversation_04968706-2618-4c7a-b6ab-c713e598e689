parameters:
  oauth2.enabled: "%env(bool:OAUTH2_ENABLED)%"
  oauth2.provider: "%env(OAUTH2_ACTIVE_PROVIDER)%"

  oauth2.client_serializer: App\Security\OAuth2\Factorial\VicioSchoolSerializer
  # Read filters and set values to different categories, where field1 is the key in
  # the result array, the value can be understood based on the Serializer
  oauth2.filters:
    "Sistema Reticular":
      - 1 # Category ID 1
    "Store":
      - 2

  # Define the roles
  oauth2.role.field: role
  # Read the value from oauth2.role.field and compare against oauth2.role.roles
  oauth2.role.roles:
    ROLE_USER:
      - Value 1
      - Value 2
    ROLE_ADMIN:
      - "TI Admin"
