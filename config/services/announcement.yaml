parameters:
  app.fundae.max_students_per_group: 80 # Número máximo de alumnos por grupo

 # Puntuación mínima para los criterios de aprobación convccatorias
  app.fundae.min_passing_score: 75
  app.fundae.action_types:
    - FUNDAE_TRIPARTITA
    - HOBETUZ
# ANNOUNCEMENT.FORM.ENTITY.FUNDAE_CONFIGURATION.OFFER
  app.course.sections:
    - INFORMATION # Agregar secciones a los cursos
    - CONTENT # Agrega contenido a los cursos
    - OPINIONS # Agregar opiniones a los cursos
    - TASK # Agregar tareas a los cursos
    - CHAT # Agregar chat a los cursos
    - MATERIALS # Agregar materiales a los cursos
    - FORUM # Agregar foro a los cursos

  app.course.materials.enabled: true # Habilitar la creación de materiales en los cursos desde el admin
  app.course.tasks.enabled: true # Habilitar la creación de tareas en los cursos desde el admin


  app.course.criteria:
    MAXIMUM_INACTIVITY_TIME: 5 # Tiempo minimo de inactividad en minutos
  app.fundae.default_entry_margin: 5
  app.fundae.default_exit_margin: 5
  app.fundae: true
  app.fundae.encryption_key: 'FUNDAE0000' # clave de encriptación para ocultar datos del usuario en la url
  app.fundae.register_on_qr: false
  app.announcement.managers.sharing: false
